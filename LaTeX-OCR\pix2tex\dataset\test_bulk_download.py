#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量下载功能的脚本
用于验证新的bulk模式是否正常工作
"""

import os
import sys
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入arxiv模块
from arxiv import *

def test_url_building():
    """测试URL构建功能"""
    print("=== 测试URL构建功能 ===")
    
    # 测试physics分类的URL构建
    urls = build_single_category_urls(
        category='physics',
        years=[2023, 2022],
        batch_size=100,
        enable_subcategories=True,
        monthly_search=True
    )
    
    print(f"构建了 {len(urls)} 个URL")
    print("前5个URL示例:")
    for i, (subcat, time_desc, url) in enumerate(urls[:5]):
        print(f"  {i+1}. {subcat} - {time_desc}")
        print(f"     {url}")
    
    return len(urls) > 0

def test_paper_id_extraction():
    """测试论文ID提取功能"""
    print("\n=== 测试论文ID提取功能 ===")
    
    # 测试从一个真实的arXiv页面提取ID
    test_url = "https://arxiv.org/list/math-ph/2023?skip=0&show=25"
    
    try:
        ids = get_arxiv_ids_from_url(test_url, max_retries=2)
        print(f"从测试URL提取到 {len(ids)} 个论文ID")
        
        if ids:
            print("前5个ID示例:")
            for i, paper_id in enumerate(ids[:5]):
                print(f"  {i+1}. {paper_id}")
        
        return len(ids) > 0
    except Exception as e:
        print(f"提取论文ID时出错: {e}")
        return False

def test_small_batch_download():
    """测试小批量下载功能"""
    print("\n=== 测试小批量下载功能 ===")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 修改配置进行小规模测试
        original_config = CONFIG.copy()
        original_bulk_config = BULK_CONFIG.copy()
        
        try:
            # 临时修改配置
            CONFIG['output_dir'] = temp_dir
            CONFIG['verbose'] = True
            BULK_CONFIG['target_paper_count'] = 5  # 只下载5篇论文进行测试
            BULK_CONFIG['primary_category'] = 'math-ph'  # 选择数学物理，论文相对较少
            BULK_CONFIG['search_years'] = [2023]  # 只搜索2023年
            BULK_CONFIG['monthly_search'] = False  # 不按月搜索，减少复杂度
            BULK_CONFIG['enable_subcategories'] = False  # 不启用子分类
            BULK_CONFIG['download_delay'] = 1.0  # 减少延迟以加快测试
            
            print("开始小批量测试下载...")
            bulk_download_papers()
            
            # 检查结果
            visited_file = os.path.join(temp_dir, 'visited_arxiv.txt')
            math_file = os.path.join(temp_dir, 'math_arxiv.txt')
            progress_file = os.path.join(temp_dir, 'progress.json')
            
            success = True
            
            if os.path.exists(visited_file):
                with open(visited_file, 'r', encoding='utf-8') as f:
                    visited_count = len([line for line in f if line.strip()])
                print(f"✓ 成功处理 {visited_count} 篇论文")
            else:
                print("✗ 未找到visited_arxiv.txt文件")
                success = False
            
            if os.path.exists(math_file):
                with open(math_file, 'r', encoding='utf-8') as f:
                    math_count = len([line for line in f if line.strip()])
                print(f"✓ 成功提取 {math_count} 个数学公式")
            else:
                print("✗ 未找到math_arxiv.txt文件")
                success = False
            
            if os.path.exists(progress_file):
                print("✓ 成功生成进度文件")
            else:
                print("✗ 未找到progress.json文件")
                success = False
            
            return success
            
        finally:
            # 恢复原始配置
            CONFIG.update(original_config)
            BULK_CONFIG.update(original_bulk_config)

def main():
    """运行所有测试"""
    print("开始测试批量下载功能...\n")
    
    tests = [
        ("URL构建", test_url_building),
        ("论文ID提取", test_paper_id_extraction),
        ("小批量下载", test_small_batch_download),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name}: ✗ 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！批量下载功能可以正常使用。")
        print("\n使用说明:")
        print("1. 修改 CONFIG['mode'] = 'bulk'")
        print("2. 设置 BULK_CONFIG['target_paper_count'] = 10000")
        print("3. 选择 BULK_CONFIG['primary_category'] = 'physics' (或其他分类)")
        print("4. 运行 python arxiv.py")
    else:
        print("❌ 部分测试失败，请检查配置和网络连接。")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
