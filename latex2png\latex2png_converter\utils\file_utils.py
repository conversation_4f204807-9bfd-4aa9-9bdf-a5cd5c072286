"""
文件操作工具函数
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
from ..monitoring.logger import get_logger

def read_latex_file(file_path: str, encoding: str = 'utf-8') -> List[str]:
    """
    读取LaTeX文件，返回代码列表
    
    Args:
        file_path: 文件路径
        encoding: 文件编码
        
    Returns:
        List[str]: LaTeX代码列表
    """
    logger = get_logger(__name__)

    try:
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not path.is_file():
            raise ValueError(f"路径不是文件: {file_path}")

        with open(path, 'r', encoding=encoding) as f:
            lines = [line.strip() for line in f.readlines()]
            
        # 过滤空行和注释行
        latex_codes = []
        for line in lines:
            if line and not line.startswith('#') and not line.startswith('%'):
                latex_codes.append(line)

        logger.info(f"成功读取 {len(latex_codes)} 条LaTeX代码 (原始行数: {len(lines)})")
        return latex_codes

    except UnicodeDecodeError as e:
        logger.error(f"文件编码错误 {file_path}: {e}")
        # 尝试其他编码
        for alt_encoding in ['gbk', 'latin1', 'cp1252']:
            try:
                logger.info(f"尝试使用 {alt_encoding} 编码读取文件")
                return read_latex_file(file_path, alt_encoding)
            except Exception:
                continue
        raise
    except Exception as e:
        logger.error(f"读取文件失败 {file_path}: {e}")
        raise

def create_output_dirs(output_dir: str, output_config) -> Dict[str, Path]:
    """
    创建输出目录结构
    
    Args:
        output_dir: 输出根目录
        output_config: 输出配置对象
        
    Returns:
        Dict[str, Path]: 创建的目录路径映射
    """
    logger = get_logger(__name__)

    base_path = Path(output_dir)
    
    # 需要创建的目录
    dirs_to_create = {
        'base': base_path,
        'images': base_path / output_config.images_subdir,
        'error_logs': base_path / output_config.error_log_dir,
        'temp': base_path / 'temp'  # 临时文件目录
    }

    created_dirs = {}
    
    for name, dir_path in dirs_to_create.items():
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            created_dirs[name] = dir_path
            logger.debug(f"创建目录: {dir_path}")
        except Exception as e:
            logger.error(f"创建目录失败 {dir_path}: {e}")
            raise

    logger.info(f"输出目录结构创建完成: {output_dir}")
    return created_dirs

def write_latex_file(latex_codes: List[str], file_path: str, encoding: str = 'utf-8'):
    """
    写入LaTeX代码到文件
    
    Args:
        latex_codes: LaTeX代码列表
        file_path: 输出文件路径
        encoding: 文件编码
    """
    logger = get_logger(__name__)

    try:
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, 'w', encoding=encoding) as f:
            for code in latex_codes:
                f.write(code + '\n')

        logger.info(f"成功写入 {len(latex_codes)} 条LaTeX代码到 {file_path}")

    except Exception as e:
        logger.error(f"写入文件失败 {file_path}: {e}")
        raise

def backup_file(file_path: str, backup_suffix: str = '.bak') -> Optional[str]:
    """
    备份文件
    
    Args:
        file_path: 原文件路径
        backup_suffix: 备份文件后缀
        
    Returns:
        Optional[str]: 备份文件路径，失败时返回None
    """
    logger = get_logger(__name__)

    try:
        source_path = Path(file_path)
        if not source_path.exists():
            logger.warning(f"源文件不存在，无法备份: {file_path}")
            return None

        backup_path = source_path.with_suffix(source_path.suffix + backup_suffix)
        
        # 如果备份文件已存在，添加时间戳
        if backup_path.exists():
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = source_path.with_suffix(f"{source_path.suffix}.{timestamp}{backup_suffix}")

        import shutil
        shutil.copy2(source_path, backup_path)
        
        logger.info(f"文件备份成功: {file_path} -> {backup_path}")
        return str(backup_path)

    except Exception as e:
        logger.error(f"文件备份失败 {file_path}: {e}")
        return None

def clean_temp_files(temp_dir: str, pattern: str = "*", max_age_hours: int = 24):
    """
    清理临时文件
    
    Args:
        temp_dir: 临时文件目录
        pattern: 文件匹配模式
        max_age_hours: 最大文件年龄（小时）
    """
    logger = get_logger(__name__)

    try:
        temp_path = Path(temp_dir)
        if not temp_path.exists():
            return

        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        deleted_count = 0
        for file_path in temp_path.glob(pattern):
            try:
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        deleted_count += 1
                        logger.debug(f"删除临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件失败 {file_path}: {e}")

        if deleted_count > 0:
            logger.info(f"清理了 {deleted_count} 个临时文件")

    except Exception as e:
        logger.error(f"清理临时文件失败: {e}")

def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        Dict[str, Any]: 文件信息
    """
    logger = get_logger(__name__)

    try:
        path = Path(file_path)
        if not path.exists():
            return {'exists': False}

        stat = path.stat()
        
        return {
            'exists': True,
            'is_file': path.is_file(),
            'is_dir': path.is_dir(),
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'modified_time': stat.st_mtime,
            'created_time': stat.st_ctime,
            'permissions': oct(stat.st_mode)[-3:],
            'extension': path.suffix,
            'name': path.name,
            'parent': str(path.parent)
        }

    except Exception as e:
        logger.error(f"获取文件信息失败 {file_path}: {e}")
        return {'exists': False, 'error': str(e)}

def ensure_directory_exists(dir_path: str) -> bool:
    """
    确保目录存在
    
    Args:
        dir_path: 目录路径
        
    Returns:
        bool: 是否成功
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception:
        return False

def safe_filename(filename: str, replacement: str = '_') -> str:
    """
    生成安全的文件名
    
    Args:
        filename: 原文件名
        replacement: 替换字符
        
    Returns:
        str: 安全的文件名
    """
    import re
    # 移除或替换不安全的字符
    unsafe_chars = r'[<>:"/\\|?*]'
    safe_name = re.sub(unsafe_chars, replacement, filename)
    
    # 移除前后空格和点
    safe_name = safe_name.strip(' .')
    
    # 确保不为空
    if not safe_name:
        safe_name = 'unnamed'
    
    # 限制长度
    if len(safe_name) > 255:
        safe_name = safe_name[:255]
    
    return safe_name

def count_lines_in_file(file_path: str) -> int:
    """
    统计文件行数
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 行数
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0
