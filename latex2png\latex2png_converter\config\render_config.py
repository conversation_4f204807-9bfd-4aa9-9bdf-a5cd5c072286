"""
渲染配置管理
"""

import yaml
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List
from enum import Enum

class BackgroundType(Enum):
    """背景类型枚举"""
    TRANSPARENT = "transparent"
    SOLID_COLOR = "solid_color"
    IMAGE = "image"  # 为将来扩展预留

@dataclass
class BackgroundConfig:
    """背景配置"""
    type: str = "transparent"  # transparent, solid_color, image
    color: str = "white"       # 当type为solid_color时的颜色
    image_path: Optional[str] = None  # 当type为image时的图片路径
    opacity: float = 1.0       # 背景不透明度 (0.0-1.0)

@dataclass
class RenderConfig:
    """渲染配置"""
    dpi: int = 300
    transparent_background: bool = True  # 保持向后兼容
    font_family: str = "Computer Modern"
    font_size: int = 12
    image_format: str = "png"
    output_dir: str = "./output"
    background: Optional[BackgroundConfig] = None

    # matplotlib配置
    matplotlib_backend: str = "Agg"
    figure_size: Optional[List[float]] = None  # None表示自适应
    auto_size: bool = True
    padding_inches: float = 0.05  # 新的背景配置

@dataclass
class ProcessConfig:
    """处理配置"""
    max_workers: int = 16
    batch_size: int = 2000
    timeout_seconds: int = 30
    retry_attempts: int = 2

@dataclass
class OutputConfig:
    """输出配置"""
    output_dir: str = "./output"
    images_subdir: str = "images"
    json_filename: str = "mapping.json"
    error_log_dir: str = "error_logs"

@dataclass
class PreprocessingConfig:
    """预处理配置"""
    enabled_levels: list = None
    level1_enabled: bool = True
    level2_enabled: bool = False  # 迭代1暂不启用
    level3_enabled: bool = False  # 迭代1暂不启用
    
    def __post_init__(self):
        if self.enabled_levels is None:
            self.enabled_levels = []
            if self.level1_enabled:
                self.enabled_levels.append('level1')
            if self.level2_enabled:
                self.enabled_levels.append('level2')
            if self.level3_enabled:
                self.enabled_levels.append('level3')

@dataclass
class AppConfig:
    """应用总配置"""
    render: RenderConfig
    process: ProcessConfig
    output: OutputConfig
    preprocessing: PreprocessingConfig

    def get_background_config(self) -> BackgroundConfig:
        """
        获取背景配置，处理向后兼容性

        Returns:
            BackgroundConfig: 解析后的背景配置
        """
        # 如果有新的背景配置，优先使用
        if self.render.background is not None:
            return self.render.background

        # 否则根据旧的transparent_background配置创建背景配置
        if self.render.transparent_background:
            return BackgroundConfig(type="transparent")
        else:
            return BackgroundConfig(type="solid_color", color="white")

def load_config(config_file: Optional[str] = None) -> AppConfig:
    """加载配置文件"""
    if config_file and Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 处理渲染配置，包括背景配置
            render_data = config_data.get('render', {})
            background_data = render_data.pop('background', None)

            render_config = RenderConfig(**render_data)

            # 如果有背景配置，创建BackgroundConfig对象
            if background_data:
                render_config.background = BackgroundConfig(**background_data)

            return AppConfig(
                render=render_config,
                process=ProcessConfig(**config_data.get('process', {})),
                output=OutputConfig(**config_data.get('output', {})),
                preprocessing=PreprocessingConfig(**config_data.get('preprocessing', {}))
            )
        except Exception as e:
            print(f"警告：配置文件加载失败 {config_file}: {e}")
            print("使用默认配置")
    
    # 返回默认配置
    return AppConfig(
        render=RenderConfig(),
        process=ProcessConfig(),
        output=OutputConfig(),
        preprocessing=PreprocessingConfig()
    )

def save_config(config: AppConfig, config_file: str):
    """保存配置到文件"""
    config_data = {
        'render': asdict(config.render),
        'process': asdict(config.process),
        'output': asdict(config.output),
        'preprocessing': asdict(config.preprocessing)
    }

    # 确保目录存在
    config_path = Path(config_file)
    config_path.parent.mkdir(parents=True, exist_ok=True)

    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)

def create_default_config_file(config_file: str = "config/default_config.yaml"):
    """创建默认配置文件"""
    default_config = AppConfig(
        render=RenderConfig(),
        process=ProcessConfig(),
        output=OutputConfig(),
        preprocessing=PreprocessingConfig()
    )
    save_config(default_config, config_file)
    return config_file
