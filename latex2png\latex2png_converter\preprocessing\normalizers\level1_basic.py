"""
Level1基础规范化器
处理基础的LaTeX语法修复
"""

import re
from typing import Dict, Pattern, List, Tuple
from ...monitoring.logger import get_logger

class Level1BasicNormalizer:
    """Level1基础规范化器"""

    def __init__(self):
        self.logger = get_logger(__name__)

        # 基础修复规则 - 使用元组存储 (pattern, replacement, description)
        self.basic_rules: List[Tuple[Pattern, str, str]] = [
            # SI单位包映射 (优先级最高，解决22%的失败问题)
            (re.compile(r'\\SI\{([^}]+)\}\{\\nano\\meter\}'), r'\1\,\text{nm}', 'SI纳米单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\nano\\metre\}'), r'\1\,\text{nm}', 'SI纳米单位转换(英式)'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\micro\\meter\}'), r'\1\,\text{μm}', 'SI微米单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\micro\\metre\}'), r'\1\,\text{μm}', 'SI微米单位转换(英式)'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\milli\\meter\}'), r'\1\,\text{mm}', 'SI毫米单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\degree\}'), r'\1°', 'SI角度单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\steradian\}'), r'\1\,\text{sr}', 'SI立体角单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\tesla\}'), r'\1\,\text{T}', 'SI特斯拉单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\kelvin\}'), r'\1\,\text{K}', 'SI开尔文单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\ampere\}'), r'\1\,\text{A}', 'SI安培单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\volt\}'), r'\1\,\text{V}', 'SI伏特单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\watt\}'), r'\1\,\text{W}', 'SI瓦特单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\henry\}'), r'\1\,\text{H}', 'SI亨利单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\weber\}'), r'\1\,\text{Wb}', 'SI韦伯单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\hertz\}'), r'\1\,\text{Hz}', 'SI赫兹单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\second\}'), r'\1\,\text{s}', 'SI秒单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\pascal\}'), r'\1\,\text{Pa}', 'SI帕斯卡单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\mega\\pascal\}'), r'\1\,\text{MPa}', 'SI兆帕单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\giga\\pascal\}'), r'\1\,\text{GPa}', 'SI吉帕单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\kilo\\hertz\}'), r'\1\,\text{kHz}', 'SI千赫兹单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\milli\\second\}'), r'\1\,\text{ms}', 'SI毫秒单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\micro\\second\}'), r'\1\,\text{μs}', 'SI微秒单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\micro\\litre\}'), r'\1\,\text{μL}', 'SI微升单位转换'),
            (re.compile(r'\\SI\{([^}]+)\}\{\\steradian\}'), r'\1\,\text{sr}', 'SI立体角单位转换'),

            # si命令映射
            (re.compile(r'\\si\{\\nano\\meter\}'), r'\\text{nm}', 'si纳米单位转换'),
            (re.compile(r'\\si\{\\nano\\metre\}'), r'\\text{nm}', 'si纳米单位转换(英式)'),
            (re.compile(r'\\si\{\\micro\\meter\}'), r'\\text{μm}', 'si微米单位转换'),
            (re.compile(r'\\si\{\\micro\\metre\}'), r'\\text{μm}', 'si微米单位转换(英式)'),
            (re.compile(r'\\si\{\\micro\\litre\}'), r'\\text{μL}', 'si微升单位转换'),
            (re.compile(r'\\si\{\\hertz\}'), r'\\text{Hz}', 'si赫兹单位转换'),
            (re.compile(r'\\si\{\\kilo\\hertz\}'), r'\\text{kHz}', 'si千赫兹单位转换'),
            (re.compile(r'\\si\{\\ampere\}'), r'\\text{A}', 'si安培单位转换'),
            (re.compile(r'\\si\{\\volt\}'), r'\\text{V}', 'si伏特单位转换'),
            (re.compile(r'\\si\{\\watt\}'), r'\\text{W}', 'si瓦特单位转换'),
            (re.compile(r'\\si\{\\henry\}'), r'\\text{H}', 'si亨利单位转换'),
            (re.compile(r'\\si\{\\weber\}'), r'\\text{Wb}', 'si韦伯单位转换'),
            (re.compile(r'\\si\{\\tesla\}'), r'\\text{T}', 'si特斯拉单位转换'),
            (re.compile(r'\\si\{\\kelvin\}'), r'\\text{K}', 'si开尔文单位转换'),
            (re.compile(r'\\si\{\\second\}'), r'\\text{s}', 'si秒单位转换'),
            (re.compile(r'\\si\{\\milli\\second\}'), r'\\text{ms}', 'si毫秒单位转换'),
            (re.compile(r'\\si\{\\micro\\second\}'), r'\\text{μs}', 'si微秒单位转换'),

            # 复合单位处理
            (re.compile(r'\\si\{\\watt/\\meter\}'), r'\\text{W/m}', 'si复合单位转换'),
            (re.compile(r'\\si\{\\ampere/\\meter\}'), r'\\text{A/m}', 'si复合单位转换'),
            (re.compile(r'\\si\{\\volt/\\meter\}'), r'\\text{V/m}', 'si复合单位转换'),
            (re.compile(r'\\si\{\\henry/\\meter\}'), r'\\text{H/m}', 'si复合单位转换'),
            (re.compile(r'\\si\{\\weber/\\meter\}'), r'\\text{Wb/m}', 'si复合单位转换'),
            (re.compile(r'\\si\{\\ampere/\\meter\^2\}'), r'\\text{A/m²}', 'si复合单位转换'),

            # 数字+单位组合处理
            (re.compile(r'(\d+)\\,\\si\{\\nano\\metre\}'), r'\1\,\\text{nm}', '数字+si单位组合'),
            (re.compile(r'(\d+)\\,\\si\{\\micro\\metre\}'), r'\1\,\\text{μm}', '数字+si单位组合'),
            (re.compile(r'(\d+)\\,\\si\{\\micro\\litre\}'), r'\1\,\\text{μL}', '数字+si单位组合'),
            (re.compile(r'(\d+)\\,\\si\{\\hertz\}'), r'\1\,\\text{Hz}', '数字+si单位组合'),
            (re.compile(r'(\d+)\\,\\si\{\\kilo\\hertz\}'), r'\1\,\\text{kHz}', '数字+si单位组合'),

            # 常用宏映射 (解决17%的失败问题)
            (re.compile(r'\\myRe\b'), r'\\text{Re}', '自定义雷诺数宏'),
            (re.compile(r'\\hbl\b'), r'h_{\\text{bl}}', '边界层高度宏'),
            (re.compile(r'\\surften\b'), r'\\sigma', '表面张力宏'),
            (re.compile(r'\\Weightk\b'), r'W_k', '权重系数宏'),
            (re.compile(r'\\Timec\b'), r'\\tau_c', '时间常数宏'),
            (re.compile(r'\\Vc\b'), r'V_c', '临界电压宏'),
            (re.compile(r'\\Ic\b'), r'I_c', '临界电流宏'),
            (re.compile(r'\\Rmat\b'), r'R_{\\text{mat}}', '材料电阻宏'),
            (re.compile(r'\\Rfil\b'), r'R_{\\text{fil}}', '细丝电阻宏'),

            # 向量和矩阵宏
            (re.compile(r'\\VEC\{([^}]+)\}'), r'\\mathbf{\1}', '向量宏转换'),
            (re.compile(r'\\MAT\{([^}]+)\}'), r'\\mathbf{\1}', '矩阵宏转换'),
            (re.compile(r'\\mb\{([^}]+)\}'), r'\\mathbf{\1}', '粗体宏转换'),

            # 工程术语宏
            (re.compile(r'\\stressb\b'), r'\\boldsymbol{\\sigma}', '应力张量宏'),
            (re.compile(r'\\strainb\b'), r'\\boldsymbol{\\varepsilon}', '应变张量宏'),
            (re.compile(r'\\plforb\b'), r'f_{\\text{pl}}', '塑性函数宏'),

            # 物理量宏
            (re.compile(r'\\Rey_b\b'), r'\\text{Re}_b', '雷诺数宏'),
            (re.compile(r'\\apprle\b'), r'\\approx', '近似符号宏'),

            # 下标宏简化
            (re.compile(r'\\mr\{([^}]+)\}'), r'\\text{\1}', '下标文本宏'),
            (re.compile(r'\\mathrm\{([^}]+)\}'), r'\\text{\1}', '正体文本简化'),

            # 字体命令现代化
            (re.compile(r'\\rm\s+([^{}]+)'), r'\\mathrm{\1}', '旧式字体命令转换'),
            (re.compile(r'\\bf\s+([^{}]+)'), r'\\mathbf{\1}', '粗体命令现代化'),
            (re.compile(r'\\it\s+([^{}]+)'), r'\\mathit{\1}', '斜体命令现代化'),
            
            # 语法修复规则 (解决6%的失败问题)
            (re.compile(r'\\p\b'), r'\\partial', '偏导数符号修复'),
            (re.compile(r'\\f\{([^}]+)\}'), r'\\text{\1}', '不完整分数修复'),
            (re.compile(r'\\theta\\degree\b'), r'\\theta°', '角度符号修复'),
            (re.compile(r'\\numberthis'), r'', '自定义编号命令移除'),
            (re.compile(r'\\tag\{\'([^}]*)\}'), r'\\tag{\1}', '标签格式修复'),

            # 特殊符号修复
            (re.compile(r'\\cancel\{([^}]+)\}'), r'\\not{\1}', 'cancel命令替换'),
            (re.compile(r'\\tfrac'), r'\\frac', '小分数命令标准化'),
            (re.compile(r'\\underbrace\{([^}]+)\}_\{([^}]+)\}'), r'\\underbrace{\1}_{\2}', 'underbrace格式修复'),

            # 过时语法现代化
            (re.compile(r'\\over'), r'\\frac', '分数语法现代化'),
            (re.compile(r'\\choose'), r'\\binom', '组合数语法更新'),
            (re.compile(r'\\atop'), r'\\genfrac{}{}{}{}', 'atop语法替换'),
            
            # 空格标准化
            (re.compile(r'\s+'), ' ', '多余空格合并'),
            (re.compile(r'\s*{\s*'), '{', '括号内空格清理'),
            (re.compile(r'\s*}\s*'), '}', '括号外空格清理'),
            (re.compile(r'\s*\^\s*'), '^', '上标空格清理'),
            (re.compile(r'\s*_\s*'), '_', '下标空格清理'),
            
            # 移除多余的数学模式标记
            (re.compile(r'^\$+'), '', '开头数学模式标记移除'),
            (re.compile(r'\$+$'), '', '结尾数学模式标记移除'),
            (re.compile(r'\$\$'), '', '双美元符号移除'),
            
            # 常见符号修复
            (re.compile(r'\\rm\s*\\AA'), r'\\mathrm{\\AA}', 'AA符号修复'),
            (re.compile(r'\\times\s+'), r'\\times ', '乘号空格标准化'),
            (re.compile(r'\\cdot\s+'), r'\\cdot ', '点乘空格标准化'),
            
            # 环境标准化
            (re.compile(r'\\begin\s*{\s*eqnarray\s*}'), r'\\begin{align}', 'eqnarray环境替换'),
            (re.compile(r'\\end\s*{\s*eqnarray\s*}'), r'\\end{align}', 'eqnarray环境结束替换'),
            
            # 括号匹配修复（简单情况）
            (re.compile(r'\\left\s*\('), r'\\left(', 'left括号空格清理'),
            (re.compile(r'\\right\s*\)'), r'\\right)', 'right括号空格清理'),
            (re.compile(r'\\left\s*\['), r'\\left[', 'left方括号空格清理'),
            (re.compile(r'\\right\s*\]'), r'\\right]', 'right方括号空格清理'),
        ]

        # 特殊处理规则（需要更复杂逻辑的）
        self.special_rules = [
            self._fix_frac_syntax,
            self._fix_nested_braces,
            self._fix_subscript_superscript,
        ]

    def normalize(self, latex_code: str) -> str:
        """应用基础规范化规则"""
        if not latex_code or not latex_code.strip():
            return latex_code
            
        result = latex_code.strip()
        changes_made = []

        # 应用基础正则表达式规则
        for pattern, replacement, description in self.basic_rules:
            old_result = result
            result = pattern.sub(replacement, result)
            if old_result != result:
                changes_made.append(description)
                self.logger.debug(f"应用规则: {description}")

        # 应用特殊处理规则
        for special_rule in self.special_rules:
            try:
                old_result = result
                result = special_rule(result)
                if old_result != result:
                    changes_made.append(f"特殊规则: {special_rule.__name__}")
                    self.logger.debug(f"应用特殊规则: {special_rule.__name__}")
            except Exception as e:
                self.logger.warning(f"特殊规则 {special_rule.__name__} 执行失败: {e}")

        # 最终清理
        result = result.strip()
        
        if changes_made:
            self.logger.debug(f"Level1规范化完成，应用了 {len(changes_made)} 个规则")
        
        return result

    def _fix_frac_syntax(self, latex_code: str) -> str:
        """修复分数语法"""
        # 处理 a \over b 格式
        pattern = re.compile(r'([^{}\\]+)\s*\\over\s*([^{}\\]+)')
        
        def replace_over(match):
            numerator = match.group(1).strip()
            denominator = match.group(2).strip()
            return f'\\frac{{{numerator}}}{{{denominator}}}'
        
        return pattern.sub(replace_over, latex_code)

    def _fix_nested_braces(self, latex_code: str) -> str:
        """修复嵌套括号问题"""
        # 移除多余的嵌套括号 {{content}} -> {content}
        while True:
            old_code = latex_code
            latex_code = re.sub(r'\{\{([^{}]*)\}\}', r'{\1}', latex_code)
            if old_code == latex_code:
                break
        return latex_code

    def _fix_subscript_superscript(self, latex_code: str) -> str:
        """修复上下标语法"""
        # 确保上下标内容被正确括起来
        # 处理多字符上下标
        latex_code = re.sub(r'_([a-zA-Z0-9]{2,})', r'_{\1}', latex_code)
        latex_code = re.sub(r'\^([a-zA-Z0-9]{2,})', r'^{\1}', latex_code)
        
        # 处理连续的上下标
        latex_code = re.sub(r'_\{([^}]+)\}_\{([^}]+)\}', r'_{\1\2}', latex_code)
        latex_code = re.sub(r'\^\{([^}]+)\}\^\{([^}]+)\}', r'^{\1\2}', latex_code)
        
        return latex_code

    def get_applied_rules(self, original: str, normalized: str) -> List[str]:
        """获取应用的规则列表（用于调试）"""
        applied_rules = []
        
        # 重新应用规则以检测哪些被使用
        temp_result = original.strip()
        
        for pattern, replacement, description in self.basic_rules:
            old_temp = temp_result
            temp_result = pattern.sub(replacement, temp_result)
            if old_temp != temp_result:
                applied_rules.append(description)
        
        return applied_rules

    def validate_result(self, result: str) -> bool:
        """验证规范化结果的基本正确性"""
        try:
            # 检查括号匹配
            if not self._check_brace_balance(result):
                return False
            
            # 检查基本LaTeX语法
            if not self._check_basic_syntax(result):
                return False
                
            return True
        except Exception:
            return False

    def _check_brace_balance(self, latex_code: str) -> bool:
        """检查括号是否平衡"""
        brace_count = 0
        for char in latex_code:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    return False
        return brace_count == 0

    def _check_basic_syntax(self, latex_code: str) -> bool:
        """检查基本LaTeX语法"""
        # 检查是否有未闭合的命令
        if latex_code.count('\\begin{') != latex_code.count('\\end{'):
            return False
        
        # 检查是否有孤立的控制字符
        if re.search(r'\\[^a-zA-Z]', latex_code):
            return False
            
        return True
