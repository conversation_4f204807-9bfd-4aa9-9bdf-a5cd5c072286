# ArXiv 批量下载工具使用指南

## 🚀 快速开始

### 获取10000篇Physics论文

1. **配置参数**（在 `arxiv.py` 文件顶部）：
   ```python
   CONFIG['mode'] = 'bulk'  # 使用批量下载模式
   BULK_CONFIG['target_paper_count'] = 10000  # 目标论文数量
   BULK_CONFIG['primary_category'] = 'physics'  # 主要分类
   ```

2. **运行下载**：
   ```bash
   python arxiv.py
   ```

3. **或者使用示例脚本**：
   ```bash
   python example_bulk_download.py
   ```

## 📊 功能特性

### ✅ 已解决的问题

- **单领域深度挖掘**：专注于单个学科领域（如physics）获取大量论文
- **突破API限制**：通过多策略搜索绕过arXiv的数量限制
- **断点续传**：支持中断后继续下载，不会丢失进度
- **智能去重**：自动避免重复下载相同论文
- **错误重试**：网络错误时自动重试，提高成功率
- **进度追踪**：实时显示下载进度和统计信息

### 🔧 搜索策略

1. **子分类搜索**：
   - Physics: `math-ph`, `hep-th`, `gr-qc`, `quant-ph`, `cond-mat` 等
   - Math: `math.AP`, `math.DG`, `math.AG`, `math.NT` 等

2. **时间范围搜索**：
   - 按年搜索：2024, 2023, 2022, ...
   - 按月搜索：2024年1月, 2024年2月, ...

3. **批次处理**：
   - 每批次最多2000篇论文（arXiv API限制）
   - 遵守3.5秒/请求的速率限制

## ⚙️ 配置说明

### 基础配置 (CONFIG)

```python
CONFIG = {
    'mode': 'bulk',  # 批量下载模式
    'output_dir': r'D:\formula_ge_dataset\arxiv_physics_10k',  # 输出目录
    'save_papers': None,  # 不保存论文文件（节省空间）
    'use_demacro': False,  # 不使用demacro（提高速度）
    'verbose': True,  # 显示详细信息
}
```

### 批量下载配置 (BULK_CONFIG)

```python
BULK_CONFIG = {
    'target_paper_count': 10000,  # 目标论文数量
    'primary_category': 'physics',  # 主要分类
    'batch_size': 2000,  # 每批次最大论文数
    'download_delay': 3.5,  # 下载间隔(秒)
    'batch_delay': 10,  # 批次间延迟(秒)
    'max_retries': 5,  # 最大重试次数
    'enable_subcategories': True,  # 启用子分类搜索
    'monthly_search': True,  # 按月搜索
    'enable_deduplication': True,  # 启用去重
    'search_years': [2024, 2023, 2022, ...],  # 搜索年份
}
```

## 📁 输出文件

下载完成后，输出目录将包含：

- `visited_arxiv.txt` - 已处理的论文ID列表
- `math_arxiv.txt` - 提取的数学公式
- `progress.json` - 详细进度信息（用于断点续传）

## 🕐 时间估算

- **10000篇论文**：约8-12小时
- **5000篇论文**：约4-6小时
- **1000篇论文**：约1-2小时

*时间取决于网络速度和论文处理复杂度*

## 🔄 断点续传

如果下载中断，再次运行程序将自动从中断处继续：

```bash
python arxiv.py  # 自动检测并继续之前的下载
```

## 📈 进度监控

### 查看实时进度
程序运行时会显示：
```
--- 批次 15: math-ph - 2023年3月 (URL 45/120) ---
处理论文 23/156: 2303.12345
  提取到 89 个公式
总进度: 3456/10000 篇论文 (34.6%)
```

### 查看详细进度
```python
python example_bulk_download.py  # 选择选项4查看进度
```

## 🛠️ 故障排除

### 常见问题

1. **下载速度慢**
   - 正常现象，受arXiv API速率限制
   - 每3.5秒下载一篇论文是最优速度

2. **部分论文下载失败**
   - 程序会自动重试
   - 失败的论文会被跳过，不影响整体进度

3. **网络中断**
   - 程序支持断点续传
   - 重新运行即可继续

4. **磁盘空间不足**
   - 设置 `save_papers = None` 不保存论文文件
   - 只保存提取的数学公式

### 优化建议

1. **提高成功率**：
   ```python
   BULK_CONFIG['max_retries'] = 10  # 增加重试次数
   BULK_CONFIG['download_delay'] = 4.0  # 增加延迟
   ```

2. **加快速度**（风险：可能被限制）：
   ```python
   BULK_CONFIG['download_delay'] = 3.0  # 减少延迟（不推荐低于3秒）
   ```

3. **节省空间**：
   ```python
   CONFIG['save_papers'] = None  # 不保存论文文件
   ```

## 📊 不同领域的论文数量估算

| 领域 | 年均论文数 | 推荐搜索年份 | 获取10k论文难度 |
|------|------------|--------------|-----------------|
| physics | ~3000 | 2024-2020 | 容易 |
| math | ~2000 | 2024-2019 | 中等 |
| cs | ~4000 | 2024-2021 | 容易 |
| stat | ~800 | 2024-2012 | 困难 |

## 🎯 使用建议

1. **首次使用**：建议先测试小批量（如100篇）
2. **长期运行**：建议在稳定网络环境下运行
3. **存储规划**：10000篇论文的公式约需要100-200MB存储空间
4. **分批下载**：可以分多次下载，每次2000-3000篇

## 📞 技术支持

如果遇到问题，请检查：
1. 网络连接是否稳定
2. Python环境是否正确
3. 输出目录是否有写入权限
4. 磁盘空间是否充足

---

**注意**：请遵守arXiv的使用条款，合理使用API，避免对服务器造成过大负担。
