# LaTeX2PNG 使用指南

## 快速开始

### 1. 测试引擎（推荐先运行）

```powershell
.\run_latex2png.ps1 -TestEngines
```

### 2. 处理示例文件

```powershell
.\run_latex2png.ps1 examples/sample_input.txt
```

### 3. 处理自定义文件

```powershell
.\run_latex2png.ps1 your_input.txt --output-dir ./my_output
```

## 详细用法

### 基本语法

```powershell
.\run_latex2png.ps1 <输入文件> [选项]
```

### 常用选项

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-InputFile` | 输入文件路径（必需） | - |
| `-OutputDir` | 输出目录 | `./output` |
| `-MaxWorkers` | 最大并发数 | `16` |
| `-BatchSize` | 批次大小 | `2000` |
| `-DPI` | 图像分辨率 | `300` |
| `-Verbose` | 详细输出 | `false` |
| `-TestEngines` | 测试引擎 | `false` |

### 示例命令

```powershell
# 基本使用
.\run_latex2png.ps1 input.txt

# 高分辨率输出
.\run_latex2png.ps1 input.txt -DPI 600

# 高并发处理
.\run_latex2png.ps1 input.txt -MaxWorkers 32 -BatchSize 5000

# 详细日志
.\run_latex2png.ps1 input.txt -Verbose

# 自定义输出目录
.\run_latex2png.ps1 input.txt -OutputDir "D:\my_output"
```

## 输入文件格式

创建一个文本文件，每行一个LaTeX公式：

```
# 这是注释行
x^2 + y^2 = z^2
\frac{1}{2}\pi r^2
\int_0^\infty e^{-x} dx
\sum_{i=1}^n i = \frac{n(n+1)}{2}
\begin{bmatrix} a & b \\ c & d \end{bmatrix}
```

## 输出结构

```
output/
├── images/                    # PNG图像文件
│   ├── math_0000001.png
│   ├── math_0000002.png
│   └── ...
├── mapping.json              # 文件名与LaTeX代码映射
├── processing_statistics.json # 处理统计
├── processing_summary.txt    # 处理摘要
└── error_logs/               # 错误日志（如果有）
    ├── latex_syntax_errors.txt
    ├── system_render_errors.txt
    └── unknown_errors.txt
```

## 系统要求

- **Python 3.8+** (formula环境)
- **TeX Live 2024+** (推荐) 或 matplotlib mathtext
- **8GB+ 内存** (推荐16GB+)
- **多核CPU** (并发处理)

## 故障排除

### 1. TeX Live相关问题

```powershell
# 检查TeX Live安装
latex --version

# 如果命令不存在，检查路径
.\find_texlive_simple.ps1
```

### 2. Python环境问题

```powershell
# 检查Python环境
& "D:\miniforge3\envs\formula\python.exe" --version

# 测试基本功能
& "D:\miniforge3\envs\formula\python.exe" test_environment.py
```

### 3. 性能问题

- **内存不足**：减少 `-MaxWorkers` 和 `-BatchSize`
- **速度慢**：增加 `-MaxWorkers`（不超过CPU核心数）
- **质量问题**：提高 `-DPI` 值

### 4. 路径配置问题

脚本会自动配置以下路径：
- **TeX Live**: `D:\Program Files\texlive\2025\bin\windows`
- **Python**: `D:\miniforge3\envs\formula\python.exe`

如果路径不同，请修改脚本中的默认值或使用参数：

```powershell
.\run_latex2png.ps1 input.txt -TexLivePath "C:\texlive\2024\bin\windows" -PythonPath "C:\Python\python.exe"
```

## 性能优化

### 1. 并发配置

```powershell
# 根据CPU核心数调整
.\run_latex2png.ps1 input.txt -MaxWorkers 24

# 大批次处理
.\run_latex2png.ps1 input.txt -BatchSize 5000
```

### 2. 内存优化

```powershell
# 小批次处理（节省内存）
.\run_latex2png.ps1 input.txt -MaxWorkers 8 -BatchSize 1000
```

### 3. 质量vs速度

```powershell
# 高质量（慢）
.\run_latex2png.ps1 input.txt -DPI 600

# 标准质量（快）
.\run_latex2png.ps1 input.txt -DPI 300

# 预览质量（最快）
.\run_latex2png.ps1 input.txt -DPI 150
```

## 高级用法

### 1. 批量处理多个文件

```powershell
# 处理目录中的所有txt文件
Get-ChildItem *.txt | ForEach-Object {
    .\run_latex2png.ps1 $_.Name -OutputDir "output_$($_.BaseName)"
}
```

### 2. 监控处理进度

```powershell
# 使用详细模式查看进度
.\run_latex2png.ps1 large_file.txt -Verbose
```

### 3. 错误分析

处理完成后检查错误日志：
```powershell
Get-Content output/error_logs/latex_syntax_errors.txt
```

## 技术支持

如果遇到问题：

1. **运行诊断**：`.\run_latex2png.ps1 -TestEngines`
2. **检查环境**：`python test_environment.py`
3. **查看日志**：使用 `-Verbose` 选项
4. **检查输出**：查看 `error_logs/` 目录
