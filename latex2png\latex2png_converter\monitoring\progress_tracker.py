"""
进度跟踪器
"""

import time
from typing import Optional
from .logger import get_logger

class ProgressTracker:
    """进度跟踪器"""

    def __init__(self, name: str = "处理"):
        self.name = name
        self.logger = get_logger(__name__)
        self.total: int = 0
        self.completed: int = 0
        self.start_time: Optional[float] = None
        self.last_update_time: float = 0
        self.update_interval: float = 5.0  # 5秒更新一次
        self.last_logged_progress: int = 0

    def set_total(self, total: int):
        """设置总数"""
        self.total = total
        self.completed = 0
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.last_logged_progress = 0
        self.logger.info(f"开始{self.name}，总数: {total}")

    def update(self, increment: int = 1):
        """更新进度"""
        self.completed += increment

        current_time = time.time()
        # 定时更新或者完成时更新
        if (current_time - self.last_update_time >= self.update_interval or 
            self.completed >= self.total):
            self._log_progress()
            self.last_update_time = current_time

    def _log_progress(self):
        """记录进度信息"""
        if self.total == 0:
            return

        progress_percent = (self.completed / self.total) * 100
        elapsed_time = time.time() - self.start_time if self.start_time else 0

        if self.completed > 0 and elapsed_time > 0:
            rate = self.completed / elapsed_time
            eta = (self.total - self.completed) / rate if rate > 0 else 0

            # 避免重复记录相同进度
            if self.completed != self.last_logged_progress:
                self.logger.info(
                    f"{self.name}进度: {self.completed}/{self.total} "
                    f"({progress_percent:.1f}%) "
                    f"速度: {rate:.1f}/秒 "
                    f"预计剩余: {eta:.0f}秒"
                )
                self.last_logged_progress = self.completed

    def get_stats(self) -> dict:
        """获取统计信息"""
        if not self.start_time:
            return {}
            
        elapsed_time = time.time() - self.start_time
        progress_percent = (self.completed / self.total * 100) if self.total > 0 else 0
        rate = self.completed / elapsed_time if elapsed_time > 0 else 0
        eta = (self.total - self.completed) / rate if rate > 0 and self.completed < self.total else 0

        return {
            'total': self.total,
            'completed': self.completed,
            'progress_percent': progress_percent,
            'elapsed_time': elapsed_time,
            'rate': rate,
            'eta': eta,
            'is_complete': self.completed >= self.total
        }

    def finish(self):
        """完成进度跟踪"""
        if self.start_time:
            elapsed_time = time.time() - self.start_time
            rate = self.completed / elapsed_time if elapsed_time > 0 else 0
            self.logger.info(
                f"{self.name}完成: {self.completed}/{self.total} "
                f"总耗时: {elapsed_time:.1f}秒 "
                f"平均速度: {rate:.1f}/秒"
            )

    def reset(self):
        """重置进度跟踪器"""
        self.total = 0
        self.completed = 0
        self.start_time = None
        self.last_update_time = 0
        self.last_logged_progress = 0

class MultiProgressTracker:
    """多任务进度跟踪器"""
    
    def __init__(self):
        self.trackers = {}
        self.logger = get_logger(__name__)
    
    def create_tracker(self, name: str, total: int) -> ProgressTracker:
        """创建新的进度跟踪器"""
        tracker = ProgressTracker(name)
        tracker.set_total(total)
        self.trackers[name] = tracker
        return tracker
    
    def get_tracker(self, name: str) -> Optional[ProgressTracker]:
        """获取指定名称的跟踪器"""
        return self.trackers.get(name)
    
    def get_overall_stats(self) -> dict:
        """获取总体统计信息"""
        total_items = sum(tracker.total for tracker in self.trackers.values())
        completed_items = sum(tracker.completed for tracker in self.trackers.values())
        
        return {
            'total_tasks': len(self.trackers),
            'total_items': total_items,
            'completed_items': completed_items,
            'overall_progress': (completed_items / total_items * 100) if total_items > 0 else 0,
            'task_stats': {name: tracker.get_stats() for name, tracker in self.trackers.items()}
        }
