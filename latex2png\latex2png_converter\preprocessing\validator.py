"""
LaTeX语法验证器
"""

import re
from typing import List, Tuple, Optional
from ..monitoring.logger import get_logger

class LaTeXValidator:
    """LaTeX语法验证器"""

    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 常见的LaTeX命令模式
        self.valid_commands = {
            # 数学符号
            'frac', 'sqrt', 'sum', 'int', 'prod', 'lim',
            'sin', 'cos', 'tan', 'log', 'ln', 'exp',
            'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'theta', 'lambda', 'mu', 'pi', 'sigma',
            'infty', 'partial', 'nabla', 'times', 'cdot', 'pm', 'mp',
            
            # 字体和格式
            'mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak',
            'text', 'textbf', 'textit', 'emph',
            
            # 结构
            'begin', 'end', 'left', 'right', 'big', 'Big', 'bigg', 'Bigg',
            'over', 'under', 'overline', 'underline',
            
            # 环境
            'align', 'equation', 'eqnarray', 'matrix', 'bmatrix', 'pmatrix', 'vmatrix',
            'cases', 'split', 'gather', 'multline',
            
            # 其他
            'label', 'ref', 'cite', 'footnote', 'caption'
        }
        
        # 常见的LaTeX环境
        self.valid_environments = {
            'align', 'align*', 'equation', 'equation*', 'eqnarray', 'eqnarray*',
            'matrix', 'bmatrix', 'pmatrix', 'vmatrix', 'Vmatrix',
            'cases', 'split', 'gather', 'gather*', 'multline', 'multline*',
            'array', 'tabular', 'table', 'figure'
        }

    def validate(self, latex_code: str) -> Tuple[bool, List[str]]:
        """
        验证LaTeX代码
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        if not latex_code or not latex_code.strip():
            return False, ["LaTeX代码不能为空"]
        
        errors = []
        
        # 检查括号平衡
        brace_errors = self._check_brace_balance(latex_code)
        errors.extend(brace_errors)
        
        # 检查环境匹配
        env_errors = self._check_environment_matching(latex_code)
        errors.extend(env_errors)
        
        # 检查命令语法
        cmd_errors = self._check_command_syntax(latex_code)
        errors.extend(cmd_errors)
        
        # 检查数学模式
        math_errors = self._check_math_mode(latex_code)
        errors.extend(math_errors)
        
        is_valid = len(errors) == 0
        return is_valid, errors

    def _check_brace_balance(self, latex_code: str) -> List[str]:
        """检查括号平衡"""
        errors = []
        
        # 检查花括号 {}
        brace_count = 0
        for i, char in enumerate(latex_code):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    errors.append(f"位置 {i}: 多余的右花括号 '}}'")
                    break
        
        if brace_count > 0:
            errors.append(f"缺少 {brace_count} 个右花括号 '}}'")
        
        # 检查方括号 []
        bracket_count = 0
        for i, char in enumerate(latex_code):
            if char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count < 0:
                    errors.append(f"位置 {i}: 多余的右方括号 ']'")
                    break
        
        if bracket_count > 0:
            errors.append(f"缺少 {bracket_count} 个右方括号 ']'")
        
        # 检查圆括号 ()
        paren_count = 0
        for i, char in enumerate(latex_code):
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count < 0:
                    errors.append(f"位置 {i}: 多余的右圆括号 ')'")
                    break
        
        if paren_count > 0:
            errors.append(f"缺少 {paren_count} 个右圆括号 ')'")
        
        return errors

    def _check_environment_matching(self, latex_code: str) -> List[str]:
        """检查环境匹配"""
        errors = []
        
        # 查找所有 \begin{} 和 \end{}
        begin_pattern = re.compile(r'\\begin\s*\{\s*(\w+)\s*\}')
        end_pattern = re.compile(r'\\end\s*\{\s*(\w+)\s*\}')
        
        begins = [(m.group(1), m.start()) for m in begin_pattern.finditer(latex_code)]
        ends = [(m.group(1), m.start()) for m in end_pattern.finditer(latex_code)]
        
        # 检查数量匹配
        begin_envs = [env for env, _ in begins]
        end_envs = [env for env, _ in ends]
        
        for env in set(begin_envs + end_envs):
            begin_count = begin_envs.count(env)
            end_count = end_envs.count(env)
            
            if begin_count != end_count:
                errors.append(f"环境 '{env}' 不匹配: {begin_count} 个 \\begin, {end_count} 个 \\end")
        
        # 检查环境名称有效性
        for env, pos in begins + ends:
            if env not in self.valid_environments:
                errors.append(f"位置 {pos}: 未知环境 '{env}'")
        
        return errors

    def _check_command_syntax(self, latex_code: str) -> List[str]:
        """检查命令语法"""
        errors = []
        
        # 查找所有LaTeX命令
        command_pattern = re.compile(r'\\([a-zA-Z]+)')
        commands = command_pattern.findall(latex_code)
        
        for cmd in commands:
            if cmd not in self.valid_commands:
                # 这里只是警告，不是错误，因为可能有自定义命令
                self.logger.debug(f"未知命令: \\{cmd}")
        
        # 检查常见的语法错误
        # 1. 孤立的反斜杠
        if re.search(r'\\(?![a-zA-Z])', latex_code):
            errors.append("发现孤立的反斜杠")
        
        # 2. 未闭合的命令参数
        if re.search(r'\\[a-zA-Z]+\s*\{[^}]*$', latex_code):
            errors.append("发现未闭合的命令参数")
        
        return errors

    def _check_math_mode(self, latex_code: str) -> List[str]:
        """检查数学模式"""
        errors = []
        
        # 检查美元符号匹配
        dollar_count = latex_code.count('$')
        if dollar_count % 2 != 0:
            errors.append("数学模式美元符号不匹配")
        
        # 检查双美元符号
        double_dollar_pattern = re.compile(r'\$\$')
        double_dollars = double_dollar_pattern.findall(latex_code)
        if len(double_dollars) % 2 != 0:
            errors.append("显示数学模式（$$）不匹配")
        
        return errors

    def suggest_fixes(self, latex_code: str, errors: List[str]) -> List[str]:
        """根据错误提供修复建议"""
        suggestions = []
        
        for error in errors:
            if "花括号" in error:
                suggestions.append("检查花括号 {} 的配对，确保每个 { 都有对应的 }")
            elif "方括号" in error:
                suggestions.append("检查方括号 [] 的配对，通常用于可选参数")
            elif "圆括号" in error:
                suggestions.append("检查圆括号 () 的配对，或使用 \\left( \\right) 进行自动调整")
            elif "环境" in error and "不匹配" in error:
                suggestions.append("检查 \\begin{} 和 \\end{} 环境的配对")
            elif "数学模式" in error:
                suggestions.append("检查数学模式符号 $ 的配对，或使用 \\[ \\] 替代 $$ $$")
            elif "未知环境" in error:
                suggestions.append("检查环境名称拼写，或确认是否需要额外的包支持")
        
        return suggestions

    def is_simple_expression(self, latex_code: str) -> bool:
        """判断是否为简单表达式（可用SymPy处理）"""
        # 简单表达式的特征：
        # - 不包含复杂环境
        # - 不包含复杂的格式命令
        # - 主要是基本的数学符号和运算
        
        complex_patterns = [
            r'\\begin\{',  # 环境
            r'\\end\{',
            r'\\array',    # 数组
            r'\\matrix',   # 矩阵
            r'\\cases',    # 分情况
            r'\\align',    # 对齐
            r'\\text',     # 文本
            r'\\mathcal',  # 花体
            r'\\mathbb',   # 黑板粗体
            r'\\mathfrak', # 哥特体
        ]
        
        for pattern in complex_patterns:
            if re.search(pattern, latex_code):
                return False
        
        return True
