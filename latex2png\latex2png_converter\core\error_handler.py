"""
错误处理器
负责错误分类和存储
"""

import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
from ..core import RenderResult, ProcessStatus, ErrorType
from ..monitoring.logger import get_logger

class ErrorHandler:
    """错误处理器"""

    def __init__(self, error_log_dir: str):
        self.error_log_dir = Path(error_log_dir)
        self.error_log_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(__name__)

        # 错误分类模式
        self.acceptable_patterns = [
            # LaTeX语法错误（可接受的错误）
            r'Undefined control sequence',
            r'Missing \$ inserted',
            r'Extra \}',
            r'Missing \}',
            r'Illegal unit of measure',
            r'Bad math environment delimiter',
            r'Missing number, treated as zero',
            r'Illegal parameter number',
            r'Use of .* doesn\'t match its definition',
            r'Paragraph ended before .* was complete',
            r'Missing control sequence inserted',
            r'Double subscript',
            r'Double superscript',
            r'Missing \{ inserted',
            r'Extra alignment tab',
            r'Misplaced alignment tab',
            r'Missing \cr inserted',
            r'Extra \cr',
        ]

        self.unacceptable_patterns = [
            # 系统环境错误（不可接受的错误）
            r'Package .* not found',
            r'Font .* not found',
            r'File .* not found',
            r'Package .* Error',
            r'LaTeX Error: Option clash',
            r'Memory allocation error',
            r'! Emergency stop',
            r'Fatal error occurred',
            r'System command failed',
            r'Permission denied',
            r'Disk full',
            r'No space left on device',
            r'Cannot write to file',
            r'matplotlib.*error',
            r'RuntimeError',
            r'MemoryError',
            r'OSError',
        ]

        # 错误统计
        self.error_stats = {
            ErrorType.ACCEPTABLE: 0,
            ErrorType.UNACCEPTABLE: 0,
            ErrorType.UNKNOWN: 0
        }

        # 错误文件映射
        self.error_files = {
            ErrorType.ACCEPTABLE: 'latex_syntax_errors.txt',
            ErrorType.UNACCEPTABLE: 'system_render_errors.txt',
            ErrorType.UNKNOWN: 'unknown_errors.txt'
        }

        self.logger.info(f"错误处理器初始化完成，日志目录: {self.error_log_dir}")

    def classify_error(self, error_message: str) -> ErrorType:
        """分类错误类型"""
        if not error_message:
            return ErrorType.UNKNOWN

        error_message_lower = error_message.lower()

        # 检查可接受错误（LaTeX语法问题）
        for pattern in self.acceptable_patterns:
            if re.search(pattern, error_message, re.IGNORECASE):
                self.logger.debug(f"错误分类为可接受: {pattern}")
                return ErrorType.ACCEPTABLE

        # 检查不可接受错误（系统环境问题）
        for pattern in self.unacceptable_patterns:
            if re.search(pattern, error_message, re.IGNORECASE):
                self.logger.debug(f"错误分类为不可接受: {pattern}")
                return ErrorType.UNACCEPTABLE

        # 未匹配任何模式
        self.logger.debug("错误分类为未知类型")
        return ErrorType.UNKNOWN

    def log_error(self, result: RenderResult):
        """记录错误到对应文件"""
        if result.status != ProcessStatus.FAILED:
            return

        # 分类错误（如果尚未分类）
        if result.error_type is None:
            result.error_type = self.classify_error(result.error_message or "")

        error_type = result.error_type
        self.error_stats[error_type] += 1

        # 确定错误日志文件
        log_file = self.error_log_dir / self.error_files[error_type]

        # 写入错误记录
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"=== 错误时间: {datetime.now().isoformat()} ===\n")
                f.write(f"索引: {result.item.index}\n")
                f.write(f"类别: {result.item.category}\n")
                f.write(f"原始LaTeX: {result.item.original_latex}\n")
                f.write(f"规范化LaTeX: {result.item.normalized_latex}\n")
                f.write(f"错误类型: {error_type.value}\n")
                f.write(f"错误信息: {result.error_message or 'N/A'}\n")
                f.write(f"处理时间: {result.processing_time:.3f}秒\n")
                
                # 记录元数据
                if result.metadata:
                    f.write(f"元数据: {result.metadata}\n")
                
                f.write("-" * 80 + "\n")

            self.logger.debug(f"错误已记录到 {log_file}: {error_type.value}")

        except Exception as e:
            self.logger.error(f"写入错误日志失败: {e}")

    def log_batch_errors(self, results: List[RenderResult]):
        """批量记录错误"""
        error_results = [r for r in results if r.status == ProcessStatus.FAILED]
        
        if not error_results:
            return

        self.logger.info(f"批量记录 {len(error_results)} 个错误")

        for result in error_results:
            self.log_error(result)

    def generate_error_report(self) -> str:
        """生成错误统计报告"""
        total_errors = sum(self.error_stats.values())
        
        if total_errors == 0:
            return "无错误发生"

        report = f"""
错误统计报告:
============

总体统计:
- 总错误数: {total_errors}
- 可接受错误(LaTeX语法): {self.error_stats[ErrorType.ACCEPTABLE]} ({self.error_stats[ErrorType.ACCEPTABLE]/total_errors*100:.2f}%)
- 不可接受错误(系统问题): {self.error_stats[ErrorType.UNACCEPTABLE]} ({self.error_stats[ErrorType.UNACCEPTABLE]/total_errors*100:.2f}%)
- 未知错误: {self.error_stats[ErrorType.UNKNOWN]} ({self.error_stats[ErrorType.UNKNOWN]/total_errors*100:.2f}%)

错误文件位置:
- 可接受错误: {self.error_log_dir / self.error_files[ErrorType.ACCEPTABLE]}
- 不可接受错误: {self.error_log_dir / self.error_files[ErrorType.UNACCEPTABLE]}
- 未知错误: {self.error_log_dir / self.error_files[ErrorType.UNKNOWN]}

建议:
- 可接受错误通常是LaTeX语法问题，可以通过改进预处理规则解决
- 不可接受错误需要检查系统环境和配置
- 未知错误需要人工分析，可能需要更新错误分类规则
        """
        
        return report.strip()

    def get_error_statistics(self) -> Dict:
        """获取错误统计信息"""
        total_errors = sum(self.error_stats.values())
        
        return {
            'total_errors': total_errors,
            'acceptable_errors': self.error_stats[ErrorType.ACCEPTABLE],
            'unacceptable_errors': self.error_stats[ErrorType.UNACCEPTABLE],
            'unknown_errors': self.error_stats[ErrorType.UNKNOWN],
            'acceptable_rate': (self.error_stats[ErrorType.ACCEPTABLE] / total_errors * 100) if total_errors > 0 else 0,
            'unacceptable_rate': (self.error_stats[ErrorType.UNACCEPTABLE] / total_errors * 100) if total_errors > 0 else 0,
            'unknown_rate': (self.error_stats[ErrorType.UNKNOWN] / total_errors * 100) if total_errors > 0 else 0,
            'error_files': {
                error_type.value: str(self.error_log_dir / filename)
                for error_type, filename in self.error_files.items()
            }
        }

    def analyze_error_patterns(self, limit: int = 10) -> Dict:
        """分析错误模式"""
        patterns = {
            'most_common_acceptable': [],
            'most_common_unacceptable': [],
            'most_common_unknown': []
        }

        # 这里可以实现更复杂的错误模式分析
        # 例如：读取错误日志文件，统计最常见的错误类型
        
        return patterns

    def suggest_fixes(self, error_type: ErrorType, error_message: str) -> List[str]:
        """根据错误类型提供修复建议"""
        suggestions = []

        if error_type == ErrorType.ACCEPTABLE:
            suggestions.extend([
                "检查LaTeX语法是否正确",
                "确认括号、环境标签是否匹配",
                "检查命令拼写是否正确",
                "考虑启用更高级别的预处理规范化"
            ])
        elif error_type == ErrorType.UNACCEPTABLE:
            suggestions.extend([
                "检查TeX Live安装是否完整",
                "确认系统PATH环境变量设置",
                "检查磁盘空间是否充足",
                "确认文件权限设置正确",
                "检查matplotlib配置是否正确"
            ])
        else:  # UNKNOWN
            suggestions.extend([
                "查看详细错误日志",
                "尝试简化LaTeX代码",
                "检查是否使用了不支持的语法",
                "考虑更新错误分类规则"
            ])

        return suggestions

    def clear_error_logs(self):
        """清空错误日志文件"""
        for error_type, filename in self.error_files.items():
            log_file = self.error_log_dir / filename
            if log_file.exists():
                try:
                    log_file.unlink()
                    self.logger.info(f"已清空错误日志: {log_file}")
                except Exception as e:
                    self.logger.error(f"清空错误日志失败 {log_file}: {e}")

        # 重置统计
        for error_type in self.error_stats:
            self.error_stats[error_type] = 0

    def export_error_summary(self, output_file: str):
        """导出错误摘要到文件"""
        try:
            summary = self.generate_error_report()
            stats = self.get_error_statistics()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(summary)
                f.write("\n\n")
                f.write("详细统计数据:\n")
                f.write("=" * 20 + "\n")
                for key, value in stats.items():
                    f.write(f"{key}: {value}\n")
            
            self.logger.info(f"错误摘要已导出到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"导出错误摘要失败: {e}")

    def reset_statistics(self):
        """重置错误统计"""
        for error_type in self.error_stats:
            self.error_stats[error_type] = 0
        self.logger.info("错误统计已重置")
