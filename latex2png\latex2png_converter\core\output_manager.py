"""
输出管理器
负责生成最终的输出文件
"""

import json
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
from ..core import BatchResult, ProcessStatus
from ..monitoring.logger import get_logger
from ..utils.file_utils import backup_file

class OutputManager:
    """输出管理器"""

    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)

    async def generate_outputs(self, batch_result: BatchResult, output_dir: str):
        """生成所有输出文件"""
        output_path = Path(output_dir)
        
        self.logger.info(f"开始生成输出文件到: {output_dir}")

        # 生成JSON映射文件
        await self._generate_json_mapping(batch_result, output_path)

        # 生成统计报告
        await self._generate_statistics_report(batch_result, output_path)
        
        # 生成处理摘要
        await self._generate_processing_summary(batch_result, output_path)
        
        # 生成失败项目列表（如果有）
        if batch_result.failed_count > 0:
            await self._generate_failed_items_list(batch_result, output_path)

        self.logger.info(f"输出文件生成完成: {output_dir}")

    async def _generate_json_mapping(self, batch_result: BatchResult, output_path: Path):
        """生成JSON映射文件"""
        mapping = {}
        successful_results = [r for r in batch_result.results if r.status == ProcessStatus.SUCCESS]

        for result in successful_results:
            if result.image_path:
                # 生成键名（不包含.png扩展名）
                image_name = Path(result.image_path).stem
                mapping[image_name] = result.item.normalized_latex

        # 备份现有文件（如果存在）
        json_file = output_path / self.config.json_filename
        if json_file.exists():
            backup_file(str(json_file))

        # 写入JSON文件
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2, sort_keys=True)

        self.logger.info(f"JSON映射文件已生成: {json_file} (包含 {len(mapping)} 个映射)")

    async def _generate_statistics_report(self, batch_result: BatchResult, output_path: Path):
        """生成统计报告"""
        stats = self._calculate_detailed_statistics(batch_result)

        # 写入统计文件
        stats_file = output_path / 'processing_statistics.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        self.logger.info(f"统计报告已生成: {stats_file}")

    async def _generate_processing_summary(self, batch_result: BatchResult, output_path: Path):
        """生成处理摘要"""
        summary = self._create_processing_summary(batch_result)
        
        summary_file = output_path / 'processing_summary.txt'
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)

        self.logger.info(f"处理摘要已生成: {summary_file}")

    async def _generate_failed_items_list(self, batch_result: BatchResult, output_path: Path):
        """生成失败项目列表"""
        failed_results = [r for r in batch_result.results if r.status == ProcessStatus.FAILED]
        
        if not failed_results:
            return

        failed_items = []
        for result in failed_results:
            failed_items.append({
                'index': result.item.index,
                'original_latex': result.item.original_latex,
                'normalized_latex': result.item.normalized_latex,
                'error_message': result.error_message,
                'error_type': result.error_type.value if result.error_type else 'unknown',
                'processing_time': result.processing_time
            })

        # 写入失败项目文件
        failed_file = output_path / 'failed_items.json'
        with open(failed_file, 'w', encoding='utf-8') as f:
            json.dump(failed_items, f, ensure_ascii=False, indent=2)

        self.logger.info(f"失败项目列表已生成: {failed_file} (包含 {len(failed_items)} 个失败项目)")

    def _calculate_detailed_statistics(self, batch_result: BatchResult) -> Dict[str, Any]:
        """计算详细统计信息"""
        successful_results = [r for r in batch_result.results if r.status == ProcessStatus.SUCCESS]
        failed_results = [r for r in batch_result.results if r.status == ProcessStatus.FAILED]
        
        # 基本统计
        basic_stats = {
            'total_processed': batch_result.total_count,
            'successful': batch_result.success_count,
            'failed': batch_result.failed_count,
            'success_rate': (batch_result.success_count / batch_result.total_count * 100) if batch_result.total_count > 0 else 0,
            'failure_rate': (batch_result.failed_count / batch_result.total_count * 100) if batch_result.total_count > 0 else 0,
            'processing_time_seconds': batch_result.processing_time,
            'average_time_per_item': batch_result.processing_time / batch_result.total_count if batch_result.total_count > 0 else 0
        }

        # 处理时间统计
        processing_times = [r.processing_time for r in batch_result.results if r.processing_time > 0]
        time_stats = {}
        if processing_times:
            time_stats = {
                'min_processing_time': min(processing_times),
                'max_processing_time': max(processing_times),
                'median_processing_time': sorted(processing_times)[len(processing_times)//2],
                'total_processing_time': sum(processing_times)
            }

        # 错误类型统计
        error_stats = {}
        if failed_results:
            from ..core import ErrorType
            error_counts = {
                ErrorType.ACCEPTABLE.value: 0,
                ErrorType.UNACCEPTABLE.value: 0,
                ErrorType.UNKNOWN.value: 0
            }
            
            for result in failed_results:
                if result.error_type:
                    error_counts[result.error_type.value] += 1
                else:
                    error_counts[ErrorType.UNKNOWN.value] += 1
            
            error_stats = {
                'error_type_distribution': error_counts,
                'most_common_error_type': max(error_counts.items(), key=lambda x: x[1])[0]
            }

        # 性能指标
        performance_stats = {
            'items_per_second': batch_result.total_count / batch_result.processing_time if batch_result.processing_time > 0 else 0,
            'items_per_minute': (batch_result.total_count / batch_result.processing_time * 60) if batch_result.processing_time > 0 else 0,
            'estimated_time_for_million': (1000000 / (batch_result.total_count / batch_result.processing_time)) if batch_result.processing_time > 0 and batch_result.total_count > 0 else 0
        }

        # 合并所有统计
        all_stats = {
            'timestamp': datetime.now().isoformat(),
            'basic': basic_stats,
            'timing': time_stats,
            'errors': error_stats,
            'performance': performance_stats
        }

        return all_stats

    def _create_processing_summary(self, batch_result: BatchResult) -> str:
        """创建处理摘要文本"""
        stats = self._calculate_detailed_statistics(batch_result)
        
        summary = f"""
LaTeX to PNG 转换处理摘要
========================

处理时间: {stats['timestamp']}

基本统计:
--------
- 总处理数: {stats['basic']['total_processed']:,}
- 成功数: {stats['basic']['successful']:,}
- 失败数: {stats['basic']['failed']:,}
- 成功率: {stats['basic']['success_rate']:.2f}%
- 失败率: {stats['basic']['failure_rate']:.2f}%

性能指标:
--------
- 总处理时间: {stats['basic']['processing_time_seconds']:.2f} 秒
- 平均每项时间: {stats['basic']['average_time_per_item']:.3f} 秒
- 处理速率: {stats['performance']['items_per_second']:.2f} 项/秒
- 处理速率: {stats['performance']['items_per_minute']:.0f} 项/分钟
"""

        # 添加时间统计（如果有）
        if stats['timing']:
            summary += f"""
时间分布:
--------
- 最快处理时间: {stats['timing']['min_processing_time']:.3f} 秒
- 最慢处理时间: {stats['timing']['max_processing_time']:.3f} 秒
- 中位处理时间: {stats['timing']['median_processing_time']:.3f} 秒
"""

        # 添加错误统计（如果有）
        if stats['errors'] and stats['basic']['failed'] > 0:
            summary += f"""
错误分析:
--------
- 可接受错误(LaTeX语法): {stats['errors']['error_type_distribution'].get('acceptable', 0)}
- 不可接受错误(系统问题): {stats['errors']['error_type_distribution'].get('unacceptable', 0)}
- 未知错误: {stats['errors']['error_type_distribution'].get('unknown', 0)}
- 主要错误类型: {stats['errors']['most_common_error_type']}
"""

        # 添加性能预测
        if stats['performance']['estimated_time_for_million'] > 0:
            estimated_hours = stats['performance']['estimated_time_for_million'] / 3600
            summary += f"""
性能预测:
--------
- 处理100万项预计时间: {estimated_hours:.1f} 小时
"""

        # 添加输出文件信息
        summary += f"""
输出文件:
--------
- JSON映射文件: {self.config.json_filename}
- 统计报告: processing_statistics.json
- 错误日志: error_logs/ 目录
- 图像文件: images/ 目录 (包含 {stats['basic']['successful']:,} 个文件)
"""

        return summary.strip()

    def generate_batch_report(self, batch_results: List[BatchResult], output_dir: str) -> str:
        """生成多批次处理报告"""
        if not batch_results:
            return "无批次数据"

        # 合并所有批次的统计
        total_processed = sum(br.total_count for br in batch_results)
        total_successful = sum(br.success_count for br in batch_results)
        total_failed = sum(br.failed_count for br in batch_results)
        total_time = sum(br.processing_time for br in batch_results)

        report = f"""
多批次处理报告
=============

批次概览:
- 总批次数: {len(batch_results)}
- 总处理项目: {total_processed:,}
- 总成功项目: {total_successful:,}
- 总失败项目: {total_failed:,}
- 整体成功率: {(total_successful/total_processed*100) if total_processed > 0 else 0:.2f}%
- 总处理时间: {total_time:.2f} 秒
- 平均处理速率: {(total_processed/total_time) if total_time > 0 else 0:.2f} 项/秒

各批次详情:
"""

        for i, batch_result in enumerate(batch_results):
            success_rate = (batch_result.success_count / batch_result.total_count * 100) if batch_result.total_count > 0 else 0
            processing_rate = (batch_result.total_count / batch_result.processing_time) if batch_result.processing_time > 0 else 0
            
            report += f"""
批次 {i+1}:
  - 处理数: {batch_result.total_count:,}
  - 成功数: {batch_result.success_count:,}
  - 失败数: {batch_result.failed_count:,}
  - 成功率: {success_rate:.2f}%
  - 处理时间: {batch_result.processing_time:.2f} 秒
  - 处理速率: {processing_rate:.2f} 项/秒
"""

        return report.strip()

    def export_results_csv(self, batch_result: BatchResult, output_path: str):
        """导出结果到CSV文件"""
        import csv
        
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'index', 'category', 'original_latex', 'normalized_latex',
                    'status', 'image_path', 'error_message', 'error_type',
                    'processing_time'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in batch_result.results:
                    writer.writerow({
                        'index': result.item.index,
                        'category': result.item.category,
                        'original_latex': result.item.original_latex,
                        'normalized_latex': result.item.normalized_latex,
                        'status': result.status.value,
                        'image_path': result.image_path or '',
                        'error_message': result.error_message or '',
                        'error_type': result.error_type.value if result.error_type else '',
                        'processing_time': result.processing_time
                    })
            
            self.logger.info(f"结果已导出到CSV: {output_path}")
            
        except Exception as e:
            self.logger.error(f"导出CSV失败: {e}")

    def cleanup_output_directory(self, output_dir: str, keep_images: bool = True):
        """清理输出目录"""
        try:
            output_path = Path(output_dir)
            
            # 清理临时文件
            temp_dir = output_path / 'temp'
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                self.logger.info("已清理临时文件")
            
            # 可选：清理图像文件
            if not keep_images:
                images_dir = output_path / self.config.images_subdir
                if images_dir.exists():
                    import shutil
                    shutil.rmtree(images_dir)
                    self.logger.info("已清理图像文件")
            
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {e}")
