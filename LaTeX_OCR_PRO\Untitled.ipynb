{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import pyecharts"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pyecharts.options as opts\n", "from pyecharts.charts import Line"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["formula_file_path = 'data/full/formulas/train.formulas.norm.txt'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["with open(formula_file_path) as f:\n", "    lines = f.readlines()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["xy = {}\n", "for i in lines:\n", "    for j in i.split(' '):\n", "        if j not in xy:\n", "            xy[j] = 1\n", "        else:\n", "            xy[j] += 1"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('{', 718216),\n", " ('}', 706914),\n", " ('_', 236929),\n", " ('^', 203093),\n", " ('2', 124083),\n", " ('(', 117967),\n", " (')', 113197),\n", " ('=', 93088),\n", " ('1', 92271),\n", " ('-', 86251),\n", " ('\\\\frac', 64213),\n", " ('+', 63557),\n", " (',', 57549),\n", " ('i', 48792),\n", " ('0', 42354),\n", " ('x', 37163),\n", " ('d', 33777),\n", " ('a', 33367),\n", " ('\\\\mu', 31480),\n", " ('n', 31208),\n", " ('.\\n', 27519),\n", " ('k', 25178),\n", " ('r', 22273),\n", " ('e', 22067),\n", " ('m', 21904),\n", " ('\\\\partial', 21695),\n", " (',\\n', 21525),\n", " ('\\\\alpha', 21130),\n", " ('A', 20465),\n", " ('p', 19599),\n", " ('t', 19426),\n", " ('c', 19073),\n", " ('j', 18993),\n", " ('3', 18906),\n", " ('\\\\left(', 18018),\n", " ('4', 17697),\n", " ('g', 16816),\n", " ('\\\\right)', 16368),\n", " ('\\\\prime', 16080),\n", " ('\\\\nu', 16046),\n", " ('\\\\pi', 15365),\n", " ('z', 14999),\n", " ('b', 14684),\n", " ('\\\\phi', 14408),\n", " ('s', 14220),\n", " ('|', 13519),\n", " ('l', 13199),\n", " ('\\\\mathrm', 13153),\n", " ('\\\\cal', 13036),\n", " ('\\\\delta', 12555),\n", " ('f', 12428),\n", " ('N', 12290),\n", " ('q', 12194),\n", " ('\\\\lambda', 11977),\n", " ('T', 11864),\n", " ('S', 11725),\n", " ('\\\\beta', 11699),\n", " ('[', 11466),\n", " ('R', 11442),\n", " ('\\\\bar', 11418),\n", " ('\\\\int', 11412),\n", " ('}\\n', 11306),\n", " ('D', 11081),\n", " ('M', 10972),\n", " ('L', 10924),\n", " (']', 10862),\n", " ('\\\\', 10783),\n", " ('B', 10408),\n", " ('&', 10299),\n", " ('F', 10100),\n", " ('y', 10026),\n", " ('\\\\sigma', 9994),\n", " ('\\\\\\\\', 9885),\n", " ('\\\\theta', 9790),\n", " ('\\\\gamma', 9222),\n", " ('/', 9100),\n", " ('\\\\psi', 8942),\n", " ('\\\\hat', 8746),\n", " ('\\\\sqrt', 8676),\n", " ('H', 8563),\n", " ('\\\\sum', 8555),\n", " ('\\\\rho', 8356),\n", " ('\\\\tilde', 8340),\n", " ('u', 8304),\n", " ('\\\\tau', 7763),\n", " ('C', 7560),\n", " ('G', 7464),\n", " ('P', 7460),\n", " ('h', 7386),\n", " ('V', 7333),\n", " ('.', 7275),\n", " ('I', 7237),\n", " ('E', 7122),\n", " ('\\\\omega', 7088),\n", " ('X', 7031),\n", " ('\\\\epsilon', 6976),\n", " ('<PERSON>', 6427),\n", " ('\\\\bf', 6306),\n", " ('\\\\eta', 6276),\n", " ('Q', 6117),\n", " ('\\\\xi', 6084),\n", " ('v', 6045),\n", " ('\\\\Phi', 6041),\n", " ('\\\\quad', 5945),\n", " ('\\\\vec', 5942),\n", " ('\\\\Gamma', 5552),\n", " ('K', 5482),\n", " ('\\\\infty', 5183),\n", " ('\\\\left[', 4998),\n", " ('\\\\pm', 4804),\n", " ('\\\\Lambda', 4790),\n", " ('U', 4765),\n", " ('\\\\dot', 4752),\n", " ('5', 4718),\n", " ('W', 4707),\n", " (')\\n', 4649),\n", " ('<PERSON>', 4634),\n", " ('\\\\varphi', 4553),\n", " ('o', 4539),\n", " ('*', 4497),\n", " ('\\\\Delta', 4457),\n", " ('\\\\right]', 4441),\n", " ('6', 4232),\n", " ('\\\\rangle', 4211),\n", " ('w', 4095),\n", " ('\\\\chi', 3993),\n", " ('\\\\Omega', 3985),\n", " ('\\\\kappa', 3711),\n", " ('\\\\qquad', 3632),\n", " ('\\\\begin{array}', 3603),\n", " (';', 3540),\n", " ('\\\\{', 3522),\n", " ('\\\\cdot', 3440),\n", " ('\\\\Psi', 3421),\n", " ('8', 3419),\n", " ('\\\\equiv', 3413),\n", " ('\\\\}', 3264),\n", " ('\\\\end{array}', 2868),\n", " ('\\\\overline', 2809),\n", " ('\\\\!', 2784),\n", " ('\\\\langle', 2770),\n", " ('\\\\rightarrow', 2628),\n", " ('\\\\dagger', 2566),\n", " ('>', 2499),\n", " ('\\\\zeta', 2440),\n", " ('\\\\varepsilon', 2424),\n", " ('\\\\nabla', 2371),\n", " ('<', 2348),\n", " ('O', 2331),\n", " ('\\\\exp', 2288),\n", " ('Y', 2206),\n", " ('\\\\Sigma', 2124),\n", " (':', 2080),\n", " ('\\\\ln', 2049),\n", " ('\\\\mathcal', 2034),\n", " ('\\\\cdots', 2009),\n", " ('\\\\ldots', 1911),\n", " ('\\\\left\\\\{', 1858),\n", " ('\\\\ell', 1853),\n", " ('\\\\:', 1776),\n", " ('0\\n', 1742),\n", " ('\\\\sin', 1729),\n", " ('\\\\otimes', 1659),\n", " ('\\\\right)\\n', 1654),\n", " ('\\\\wedge', 1597),\n", " ('\\\\sim', 1596),\n", " ('\\\\Pi', 1554),\n", " ('!', 1525),\n", " ('\\\\cos', 1491),\n", " ('\\\\prod', 1468),\n", " ('\\\\hbar', 1450),\n", " ('7', 1450),\n", " ('\\\\in', 1402),\n", " ('9', 1355),\n", " ('\\\\widetilde', 1334),\n", " ('\\\\vert', 1331),\n", " ('\\\\Big', 1257),\n", " ('\\\\to', 1242),\n", " ('\\\\Theta', 1183),\n", " ('\\\\times', 1152),\n", " ('\\\\mid', 1144),\n", " ('\\\\right\\\\}', 1125),\n", " ('\\\\right|', 1035),\n", " ('\\\\mathbf', 1016),\n", " ('\\\\underline', 987),\n", " ('\\\\ast', 940),\n", " ('\\\\left|', 894),\n", " ('\\\\leq', 884),\n", " ('\\\\dots', 879),\n", " ('\\\\approx', 876),\n", " ('\\\\star', 862),\n", " ('\\\\log', 853),\n", " ('\\\\widehat', 800),\n", " ('\\\\stackrel', 795),\n", " ('\\\\sinh', 747),\n", " ('\\\\lim', 740),\n", " ('\\\\end{array}\\n', 735),\n", " ('\\\\big', 727),\n", " ('\\\\displaystyle', 719),\n", " ('\\\\begin{matrix}', 708),\n", " ('\\\\perp', 706),\n", " ('\\\\cosh', 701),\n", " (']\\n', 687),\n", " ('\\\\end{matrix}', 676),\n", " ('\\\\det', 675),\n", " ('\\\\left.', 617),\n", " ('\\\\right]\\n', 564),\n", " ('\\\\geq', 560),\n", " ('\\\\mp', 560),\n", " ('\\\\simeq', 558),\n", " ('\\\\dag', 556),\n", " ('\\\\Bigr', 551),\n", " ('\\\\vartheta', 536),\n", " ('\\\\Bigl', 521),\n", " (\"'\", 512),\n", " ('\\\\neq', 510),\n", " ('\\\\circ', 505),\n", " ('\\\\right.\\n', 500),\n", " ('\\\\right\\\\rangle', 473),\n", " ('\\\\longrightarrow', 468),\n", " ('\\\\oint', 462),\n", " ('\\\\biggl', 444),\n", " ('\\\\bigg', 443),\n", " ('\\\\biggr', 440),\n", " ('\\\\textstyle', 387),\n", " ('\\\\not', 374),\n", " ('\\\\left\\\\langle', 373),\n", " ('\\\\ddot', 369),\n", " ('\\\\bigr', 369),\n", " ('\\\\bigl', 367),\n", " ('\\\\oplus', 365),\n", " ('\\\\,\\n', 362),\n", " ('\\\\Xi', 338),\n", " ('\\\\boldmath', 336),\n", " ('1\\n', 303),\n", " (';\\n', 299),\n", " ('\\\\propto', 271),\n", " ('\\\\}\\n', 270),\n", " ('\\\\check', 264),\n", " ('\\\\triangle', 260),\n", " ('\\\\le', 255),\n", " ('\\\\rangle\\n', 242),\n", " ('\\\\right.', 236),\n", " ('\\\\varrho', 232),\n", " ('\\\\tan', 231),\n", " ('\\\\ge', 230),\n", " ('\\\\imath', 219),\n", " ('\\\\forall', 218),\n", " ('--', 218),\n", " ('\\\\|', 217),\n", " ('\\\\scriptscriptstyle', 214),\n", " ('=\\n', 206),\n", " ('\\\\right\\\\}\\n', 204),\n", " ('\\\\right>', 193),\n", " ('\\\\tanh', 192),\n", " ('\\\\nonumber\\n', 188),\n", " ('\\\\lbrack', 187),\n", " ('\\\\sp', 186),\n", " ('\\\\bot', 178),\n", " ('\\\\it', 171),\n", " ('\\\\mapsto', 170),\n", " ('\\\\Rightarrow', 168),\n", " ('\\\\parallel', 168),\n", " ('\\\\leftrightarrow', 167),\n", " ('\\\\iota', 165),\n", " ('\\\\subset', 164),\n", " ('\\\\textrm', 162),\n", " ('\\\\scriptsize', 151),\n", " ('\\\\Bigg', 150),\n", " ('>\\n', 148),\n", " ('\\\\l', 148),\n", " ('\\\\left<', 140),\n", " ('\\\\binom', 133),\n", " ('\\\\overrightarrow', 129),\n", " ('\\\\hspace', 129),\n", " ('\\\\jmath', 128),\n", " ('\\\\ll', 125),\n", " ('\\\\ne', 118),\n", " ('\\\\d', 116),\n", " ('\\\\phantom', 116),\n", " ('\\\\tiny', 115),\n", " ('\\\\cong', 115),\n", " ('\\\\sf', 115),\n", " ('\\\\Biggr', 114),\n", " ('\\\\Biggl', 111),\n", " ('x\\n', 110),\n", " ('\\\\gg', 110),\n", " ('+\\n', 109),\n", " ('\\\\Upsilon', 107),\n", " ('\\\\o', 104),\n", " ('\\\\psi\\n', 103),\n", " ('\\\\L', 103),\n", " ('\\\\bullet', 103),\n", " ('\\\\vee', 102),\n", " ('\\\\\\n', 100),\n", " ('2\\n', 99),\n", " ('\\\\breve', 98),\n", " ('\\\\bigoplus', 98),\n", " ('\\\\ldots\\n', 96),\n", " ('\\\\wp', 96),\n", " ('\\\\scriptstyle', 94),\n", " ('\\\\small', 94),\n", " ('\\\\downarrow', 92),\n", " ('\\\\cot', 92),\n", " ('\\\\atop', 92),\n", " ('\\\\cdots\\n', 91),\n", " ('\\\\varpi', 90),\n", " ('\\\\coth', 85),\n", " ('\\\\#', 81),\n", " ('\\\\sb', 80),\n", " ('\\\\vdots', 79),\n", " ('\\\\uparrow', 78),\n", " ('\\\\cap', 77),\n", " ('\\\\nonumber', 76),\n", " ('\\\\Im', 76),\n", " ('\\\\kern', 75),\n", " ('\\\\supset', 73),\n", " ('\\\\cup', 71),\n", " ('\\\\slash', 70),\n", " ('\\\\arctan', 70),\n", " ('\\\\Re', 69),\n", " ('-\\n', 69),\n", " ('\\\\upsilon', 69),\n", " ('\\\\rbrack', 68),\n", " ('\\\\Longrightarrow', 66),\n", " ('\\\\mit', 65),\n", " ('|\\n', 64),\n", " ('\\\\begin{cases}', 62),\n", " ('\\\\end{cases}', 62),\n", " ('\\\\underbrace', 61),\n", " ('\\\\ddots', 60),\n", " ('\\\\acute', 60),\n", " ('\\\\dots\\n', 58),\n", " ('t\\n', 57),\n", " ('\\\\hline', 55),\n", " ('\\\\qquad\\n', 55),\n", " ('n\\n', 54),\n", " ('\\\\right\\\\rangle\\n', 54),\n", " ('\\\\min', 54),\n", " ('\\\\quad\\n', 53),\n", " (':\\n', 53),\n", " ('\\\\lbrace', 52),\n", " ('\\\\dim', 51),\n", " ('\\\\infty\\n', 50),\n", " ('\\\\phi\\n', 48),\n", " ('\\\\O', 48),\n", " ('3\\n', 47),\n", " ('\\\\mathsf', 46),\n", " ('', 45),\n", " ('\\\\bigtriangleup', 44),\n", " ('\\\\varsigma', 44),\n", " ('\\\\Leftrightarrow', 44),\n", " ('\\\\S', 44),\n", " ('\\\\rbrace', 43),\n", " ('\\\\longleftrightarrow', 43),\n", " ('~\\n', 43),\n", " ('\\\\Large', 42),\n", " ('\\\\vspace', 41),\n", " ('\\\\rightharpoonup', 41),\n", " ('`', 41),\n", " ('N\\n', 40),\n", " ('\\\\:\\n', 40),\n", " ('\\\\i', 40),\n", " ('\\\\leftarrow', 40),\n", " ('\\\\theta\\n', 39),\n", " ('\\\\footnotesize', 39),\n", " ('\\\\Vert', 39),\n", " ('\\\\Longleftrightarrow', 37),\n", " ('\\\\enspace', 37),\n", " ('\\\\large', 36),\n", " ('\\\\right\\\\vert', 35),\n", " ('\\\\left\\\\vert', 34),\n", " ('(\\n', 34),\n", " ('\\\\deg', 34),\n", " ('\"', 34),\n", " ('\\\\textbf', 34),\n", " ('\\\\makebox', 34),\n", " ('\\\\raisebox', 33),\n", " ('\\\\Psi\\n', 33),\n", " ('\\\\bigotimes', 33),\n", " ('\\\\put', 33),\n", " ('\\\\hfill', 33),\n", " ('\\\\Phi\\n', 32),\n", " ('\\\\end{matrix}\\n', 32),\n", " ('\\\\tt', 32),\n", " ('\\\\doteq', 32),\n", " ('\\\\max', 32),\n", " ('R\\n', 31),\n", " ('\\\\vphantom', 31),\n", " ('\\\\P', 31),\n", " ('\\\\mathop', 30),\n", " ('\\\\overleftarrow', 30),\n", " ('g\\n', 30),\n", " ('\\\\tau\\n', 30),\n", " ('\\\\pi\\n', 30),\n", " ('\\\\left\\\\|', 30),\n", " ('\\\\emptyset', 30),\n", " ('\\\\sl', 30),\n", " ('z\\n', 30),\n", " ('a\\n', 29),\n", " ('\\\\right\\\\|', 29),\n", " ('\\\\llap', 29),\n", " ('m\\n', 28),\n", " ('\\\\backslash', 28),\n", " ('\\\\arg', 28),\n", " ('\\\\sharp', 28),\n", " ('k\\n', 27),\n", " ('V\\n', 27),\n", " ('F\\n', 27),\n", " ('r\\n', 26),\n", " ('\\\\raise', 26),\n", " ('\\\\protect\\n', 26),\n", " ('\\\\ref', 26),\n", " ('\\\\buildrel', 26),\n", " ('\\\\epsilon\\n', 25),\n", " ('\\\\eta\\n', 25),\n", " ('\\\\flat', 25),\n", " ('\\\\omega\\n', 25),\n", " ('\\\\cdotp\\n', 25),\n", " ('M\\n', 24),\n", " ('A\\n', 24),\n", " ('\\\\textup', 24),\n", " ('\\\\sigma\\n', 24),\n", " ('\\\\odot', 24),\n", " ('d\\n', 24),\n", " ('\\\\alpha\\n', 24),\n", " ('\\\\right>\\n', 23),\n", " ('\\\\protect', 23),\n", " ('\\\\lambda\\n', 23),\n", " ('p\\n', 23),\n", " ('\\\\,', 22),\n", " ('y\\n', 22),\n", " ('\\\\mathit', 22),\n", " ('\\\\label', 22),\n", " ('s\\n', 22),\n", " ('\\\\varphi\\n', 22),\n", " ('\\\\bigcup', 22),\n", " ('\\\\strut', 22),\n", " ('\\\\thinspace', 21),\n", " ('\\\\rfloor', 21),\n", " ('\\\\rho\\n', 21),\n", " ('H\\n', 21),\n", " ('\\\\longmapsto', 21),\n", " ('\\\\right|\\n', 21),\n", " ('\\\\overbrace', 21),\n", " ('\\\\xi\\n', 20),\n", " ('\\\\/', 20),\n", " ('B\\n', 20),\n", " ('\\\\chi\\n', 20),\n", " ('\\\\unitlength', 20),\n", " ('\\\\cdot\\n', 20),\n", " ('C\\n', 19),\n", " ('D\\n', 19),\n", " ('T\\n', 19),\n", " ('\\\\colon', 19),\n", " ('\\\\Omega\\n', 19),\n", " ('\\\\subseteq', 19),\n", " ('S\\n', 19),\n", " ('4\\n', 19),\n", " ('Z\\n', 19),\n", " ('\\\\setlength', 19),\n", " ('\\\\pounds', 19),\n", " ('\\\\ni', 18),\n", " ('\\\\diamond', 18),\n", " ('\\\\noalign', 18),\n", " ('v\\n', 17),\n", " ('5\\n', 17),\n", " ('\\\\ominus', 17),\n", " ('\\\\enskip', 17),\n", " ('L\\n', 16),\n", " ('\\\\_', 16),\n", " ('\\\\bigwedge', 16),\n", " ('\\\\line', 16),\n", " ('6\\n', 15),\n", " ('\\\\fbox', 15),\n", " ('f\\n', 15),\n", " ('u\\n', 15),\n", " ('\\\\lfloor', 14),\n", " ('I\\n', 14),\n", " ('\\\\Lambda\\n', 14),\n", " ('b\\n', 14),\n", " ('\\\\aleph', 14),\n", " ('j\\n', 13),\n", " ('Q\\n', 13),\n", " ('\\\\circle', 13),\n", " ('U\\n', 13),\n", " ('G\\n', 13),\n", " ('i\\n', 13),\n", " ('\\\\sup', 12),\n", " ('c\\n', 12),\n", " ('\\\\arccos', 12),\n", " ('\\\\gamma\\n', 12),\n", " ('\\\\begin{picture}', 12),\n", " ('\\\\end{picture}', 12),\n", " ('\\\\arcsin', 12),\n", " ('\\\\bigtriangledown', 12),\n", " ('\\\\bigcap', 12),\n", " ('l\\n', 12),\n", " ('\\\\vline', 12),\n", " ('\\\\smallskip', 11),\n", " ('\\\\b', 11),\n", " ('\\\\sec', 11),\n", " ('\\\\land', 11),\n", " ('\\\\bmod', 11),\n", " ('\\\\space', 11),\n", " ('W\\n', 11),\n", " ('\\\\mu\\n', 11),\n", " ('\\\\left\\\\lbrack', 11),\n", " ('?', 11),\n", " ('\\\\hookrightarrow', 11),\n", " ('\\\\times\\n', 11),\n", " ('E\\n', 11),\n", " ('\\\\rlap', 11),\n", " ('\\\\vert\\n', 10),\n", " ('9\\n', 10),\n", " ('8\\n', 10),\n", " ('\\\\mid\\n', 10),\n", " ('\\\\diamondsuit', 10),\n", " ('q\\n', 10),\n", " ('\\\\right\\\\rbrack', 10),\n", " ('\\\\natural', 10),\n", " ('\\\\pmod', 9),\n", " ('\\\\beta\\n', 9),\n", " ('\\\\setminus', 9),\n", " ('\\\\varsigma\\n', 9),\n", " ('!\\n', 9),\n", " ('[\\n', 9),\n", " ('\\\\cdotp', 9),\n", " ('K\\n', 9),\n", " ('X\\n', 9),\n", " ('\\\\ker', 9),\n", " ('\\\\ddagger', 9),\n", " ('\\\\right\\\\rfloor', 8),\n", " ('P\\n', 8),\n", " ('\\\\Delta\\n', 8),\n", " ('\\\\textit', 8),\n", " ('\\\\&', 8),\n", " ('7\\n', 8),\n", " ('\\\\-', 8),\n", " ('\\\\zeta\\n', 8),\n", " ('e\\n', 8),\n", " ('\\\\longleftarrow', 8),\n", " ('\\\\c', 7),\n", " ('Y\\n', 7),\n", " ('h\\n', 7),\n", " ('\\\\left\\\\lbrace', 7),\n", " ('\\\\rbrace\\n', 7),\n", " ('\\\\vdash', 7),\n", " ('w\\n', 7),\n", " ('J\\n', 7),\n", " ('\\\\vskip', 7),\n", " ('\\\\framebox', 7),\n", " ('\\\\normalsize', 7),\n", " ('\\\\bigm', 7),\n", " ('\\\\rbrack\\n', 6),\n", " ('\\\\relax', 6),\n", " ('\\\\equiv\\n', 6),\n", " ('\\\\thicklines', 6),\n", " ('\\\\int\\n', 6),\n", " ('\\\\lower', 6),\n", " ('\\\\csc', 6),\n", " ('\\\\sc', 6),\n", " ('\\\\hfil', 6),\n", " ('\\\\top', 6),\n", " ('\\\\right\\\\rbrace', 5),\n", " ('\\\\nu\\n', 5),\n", " ('\\\\j', 5),\n", " ('\\\\varepsilon\\n', 5),\n", " ('\\\\Theta\\n', 5),\n", " ('\\\\Pi\\n', 5),\n", " ('\\\\textsf', 5),\n", " ('\\\\mkern', 5),\n", " ('\\\\textnormal', 5),\n", " ('\\\\supseteq', 5),\n", " ('\\\\medskip', 5),\n", " ('\\\\exists', 5),\n", " ('\\\\smash', 5),\n", " ('\\\\surd', 5),\n", " ('\\\\Biggm', 5),\n", " ('\\\\Gamma\\n', 5),\n", " ('\\\\sqcup', 5),\n", " ('\\\\null', 5),\n", " ('\\\\special', 5),\n", " ('\\\\itshape', 5),\n", " ('\\\\delta\\n', 4),\n", " ('\\\\lceil', 4),\n", " ('\\\\!\\n', 4),\n", " ('\\\\do', 4),\n", " ('\\\\inf', 4),\n", " ('\\\\lefteqn', 4),\n", " ('---', 4),\n", " ('\\\\unboldmath', 4),\n", " ('\\\\prec', 4),\n", " ('\\\\LARGE', 4),\n", " ('\\\\cite', 4),\n", " ('\\\\Longleftarrow', 4),\n", " ('\\\\triangleright', 4),\n", " ('\\\\ensuremath', 4),\n", " ('\\\\cup\\n', 4),\n", " ('\\\\amalg', 4),\n", " ('\\\\rightleftharpoons', 3),\n", " ('\\\\grave', 3),\n", " (\"\\\\'\", 3),\n", " ('\\\\sim\\n', 3),\n", " ('\\\\protectu', 3),\n", " ('\\\\asymp', 3),\n", " ('\\\\oslash', 3),\n", " ('\\\\setcounter', 3),\n", " ('\\\\renewcommand', 3),\n", " ('\\\\arraystretch', 3),\n", " ('\\\\smile', 3),\n", " ('\\\\ae', 3),\n", " ('\\\\Bigm', 3),\n", " ('\\\\ooalign', 3),\n", " ('\\\\crcr', 3),\n", " ('\\\\skew', 3),\n", " ('\\\\fboxsep', 3),\n", " ('\\\\Sigma\\n', 3),\n", " ('\\\\ss', 3),\n", " ('\\\\*', 3),\n", " ('\\\\kappa\\n', 3),\n", " ('\\\\AA', 3),\n", " ('\\\\sqcap', 3),\n", " ('\\\\vss', 3),\n", " ('\\\\mathbin', 2),\n", " ('\\\\left\\\\lfloor', 2),\n", " ('\\\\em', 2),\n", " ('\\\\succeq', 2),\n", " ('\\\\limsup', 2),\n", " ('\\\\rfloor\\n', 2),\n", " ('\\\\bigsqcup', 2),\n", " ('\\\\right\\\\rbrace\\n', 2),\n", " ('\\\\hbar\\n', 2),\n", " ('\\\\texttt', 2),\n", " ('\\\\Huge', 2),\n", " ('\\\\wedge\\n', 2),\n", " ('\\\\emptyset\\n', 2),\n", " ('\\\\/\\n', 2),\n", " ('\\\\leq\\n', 2),\n", " ('\\\\lq', 2),\n", " ('\\\\nolinebreak', 2),\n", " ('\\\\parbox', 2),\n", " ('\\\\sum\\n', 2),\n", " ('\\\\nearrow', 2),\n", " ('\\\\exp\\n', 2),\n", " ('\\\\multiput', 2),\n", " ('\\\\SS', 2),\n", " ('\\\\notin', 2),\n", " ('\\\\succ', 2),\n", " ('\\\\searrow', 2),\n", " ('\\\\mathversion', 2),\n", " ('\\\\hphantom', 2),\n", " ('\\\\ddag', 2),\n", " ('\\\\lgroup', 2),\n", " ('\\\\rgroup', 2),\n", " ('\\\\arrowvert', 2),\n", " ('\\\\mathrel', 2),\n", " ('\\\\vartheta\\n', 2),\n", " ('\\\\lg', 2),\n", " ('\\\\Downarrow', 2),\n", " ('\\\\smallint', 2),\n", " ('@', 2),\n", " ('\\\\mskip', 2),\n", " ('?\\n', 2),\n", " ('\\\\hss', 2),\n", " ('O\\n', 1),\n", " ('\\\\leftrightarrow\\n', 1),\n", " ('\\\\mathaccent', 1),\n", " ('\\\\left/', 1),\n", " ('\\\\^', 1),\n", " ('\\\\tan\\n', 1),\n", " ('\\\\ell\\n', 1),\n", " ('\\\\brack', 1),\n", " ('\\\\protecte', 1),\n", " ('\\\\bigvee', 1),\n", " ('\\\\doteq\\n', 1),\n", " ('\\\\biggm', 1),\n", " ('\\\\hfill\\n', 1),\n", " ('\\\\fill', 1),\n", " ('\\\\verb', 1),\n", " ('\\\\gg\\n', 1),\n", " ('\\\\circ\\n', 1),\n", " ('\\\\right\\\\|\\n', 1),\n", " ('\\\\protectZ', 1),\n", " ('\\\\rightarrowfill', 1),\n", " ('\\\\left>', 1),\n", " ('\\\\right<', 1),\n", " ('\\\\prime\\n', 1),\n", " ('\\\\right\\\\rbrack\\n', 1),\n", " ('\"\\n', 1),\n", " ('\\\\protectE', 1),\n", " ('\\\\nwarrow', 1),\n", " ('\\\\swarrow', 1),\n", " ('\\\\relax\\n', 1),\n", " ('\\\\mathord', 1),\n", " ('\\\\protectm', 1),\n", " ('\\\\arraycolsep', 1),\n", " ('\\n', 1),\n", " ('\\\\triangleleft', 1),\n", " ('\\\\root', 1),\n", " ('\\\\of', 1),\n", " ('\\\\arctan\\n', 1),\n", " ('\\\\parallel\\n', 1),\n", " ('\\\\preceq', 1),\n", " ('\\\\longleftrightarrow\\n', 1),\n", " ('\\\\vector', 1),\n", " ('\\\\in\\n', 1),\n", " ('\\\\ss\\n', 1),\n", " ('\\\\multicolumn', 1),\n", " ('\\\\rightarrow\\n', 1),\n", " ('\\\\overwithdelims', 1),\n", " ('\\\\partial\\n', 1),\n", " ('\\\\left]', 1),\n", " ('\\\\right[\\n', 1),\n", " ('\\\\Upsilon\\n', 1),\n", " ('\\\\left\\\\lceil', 1),\n", " ('\\\\right\\\\rceil', 1),\n", " ('\\\\leavevmode', 1),\n", " ('\\\\mathstrut', 1),\n", " ('\\\\symbol', 1),\n", " ('\\\\displaystyle\\n', 1),\n", " ('\\\\begin{tabular}', 1),\n", " ('\\\\end{tabular}', 1),\n", " ('\\\\geq\\n', 1),\n", " ('*\\n', 1),\n", " ('\\\\liminf', 1),\n", " ('\\\\triangle\\n', 1),\n", " ('\\\\slash\\n', 1),\n", " ('\\\\langle\\n', 1),\n", " ('\\\\|\\n', 1),\n", " ('\\\\nabla\\n', 1),\n", " ('\\\\footnotemark', 1),\n", " ('\\\\mathopen', 1),\n", " ('\\\\mathclose', 1),\n", " ('\\\\nulldelimiterspace', 1),\n", " ('\\\\brace', 1),\n", " ('\\\\Xi\\n', 1),\n", " ('\\\\mapsto\\n', 1),\n", " ('\\\\to\\n', 1),\n", " ('\\\\atopwithdelims', 1),\n", " ('\\\\right/', 1),\n", " ('\\\\vcenter', 1)]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(xy.items(), key=lambda x: x[1], reverse=True)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["x = []\n", "y = []\n", "for i in sorted(xy.items(), key=lambda x: x[1], reverse=True):\n", "    x.append(i[0])\n", "    y.append(i[1])"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/html": ["<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "    <div id=\"32e4caf85aa5491f8a9adbf39c6cf147\" style=\"width:900px; height:500px;\"></div>\n", "\n", "\n", "<script>\n", "    require(['echarts'], function(echarts) {\n", "        var chart_32e4caf85aa5491f8a9adbf39c6cf147 = echarts.init(\n", "            document.getElementById('32e4caf85aa5491f8a9adbf39c6cf147'), 'white', {renderer: 'canvas'});\n", "        var option_32e4caf85aa5491f8a9adbf39c6cf147 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"color\": [\n", "        \"#c23531\",\n", "        \"#2f4554\",\n", "        \"#61a0a8\",\n", "        \"#d48265\",\n", "        \"#749f83\",\n", "        \"#ca8622\",\n", "        \"#bda29a\",\n", "        \"#6e7074\",\n", "        \"#546570\",\n", "        \"#c4ccd3\",\n", "        \"#f05b72\",\n", "        \"#ef5b9c\",\n", "        \"#f47920\",\n", "        \"#905a3d\",\n", "        \"#fab27b\",\n", "        \"#2a5caa\",\n", "        \"#444693\",\n", "        \"#726930\",\n", "        \"#b2d235\",\n", "        \"#6d8346\",\n", "        \"#ac6767\",\n", "        \"#1d953f\",\n", "        \"#6950a1\",\n", "        \"#918597\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"line\",\n", "            \"name\": \"\\u6570\\u91cf\",\n", "            \"connectNulls\": false,\n", "            \"symbolSize\": 4,\n", "            \"showSymbol\": true,\n", "            \"smooth\": false,\n", "            \"step\": false,\n", "            \"data\": [\n", "                [\n", "                    \"{\",\n", "                    718216\n", "                ],\n", "                [\n", "                    \"}\",\n", "                    706914\n", "                ],\n", "                [\n", "                    \"_\",\n", "                    236929\n", "                ],\n", "                [\n", "                    \"^\",\n", "                    203093\n", "                ],\n", "                [\n", "                    \"2\",\n", "                    124083\n", "                ],\n", "                [\n", "                    \"(\",\n", "                    117967\n", "                ],\n", "                [\n", "                    \")\",\n", "                    113197\n", "                ],\n", "                [\n", "                    \"=\",\n", "                    93088\n", "                ],\n", "                [\n", "                    \"1\",\n", "                    92271\n", "                ],\n", "                [\n", "                    \"-\",\n", "                    86251\n", "                ],\n", "                [\n", "                    \"\\\\frac\",\n", "                    64213\n", "                ],\n", "                [\n", "                    \"+\",\n", "                    63557\n", "                ],\n", "                [\n", "                    \",\",\n", "                    57549\n", "                ],\n", "                [\n", "                    \"i\",\n", "                    48792\n", "                ],\n", "                [\n", "                    \"0\",\n", "                    42354\n", "                ],\n", "                [\n", "                    \"x\",\n", "                    37163\n", "                ],\n", "                [\n", "                    \"d\",\n", "                    33777\n", "                ],\n", "                [\n", "                    \"a\",\n", "                    33367\n", "                ],\n", "                [\n", "                    \"\\\\mu\",\n", "                    31480\n", "                ],\n", "                [\n", "                    \"n\",\n", "                    31208\n", "                ]\n", "            ],\n", "            \"hoverAnimation\": true,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"position\": \"top\",\n", "                \"margin\": 8\n", "            },\n", "            \"lineStyle\": {\n", "                \"width\": 1,\n", "                \"opacity\": 1,\n", "                \"curveness\": 0,\n", "                \"type\": \"solid\"\n", "            },\n", "            \"areaStyle\": {\n", "                \"opacity\": 0\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u6570\\u91cf\"\n", "            ],\n", "            \"selected\": {\n", "                \"\\u6570\\u91cf\": true\n", "            },\n", "            \"show\": true\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": false,\n", "                \"lineStyle\": {\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"{\",\n", "                \"}\",\n", "                \"_\",\n", "                \"^\",\n", "                \"2\",\n", "                \"(\",\n", "                \")\",\n", "                \"=\",\n", "                \"1\",\n", "                \"-\",\n", "                \"\\\\frac\",\n", "                \"+\",\n", "                \",\",\n", "                \"i\",\n", "                \"0\",\n", "                \"x\",\n", "                \"d\",\n", "                \"a\",\n", "                \"\\\\mu\",\n", "                \"n\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": false,\n", "                \"lineStyle\": {\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"text\": \"\\u516c\\u5f0f\\u7b26\\u53f7\\u7edf\\u8ba1\"\n", "        }\n", "    ]\n", "};\n", "        chart_32e4caf85aa5491f8a9adbf39c6cf147.setOption(option_32e4caf85aa5491f8a9adbf39c6cf147);\n", "    });\n", "</script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x7f8075c952e8>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["l = Line()\n", "l.add_xaxis(x[:20])\n", "l.add_yaxis(\"数量\", y[:20])\n", "l.set_global_opts(title_opts=opts.TitleOpts(title=\"公式符号统计\"))\n", "l.render_notebook()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/html": ["<div align='center' style='width:100%'><div align='center' style='text-align:justify; border-radius: 25px;background: #fff7f7;overflow: auto; width:500px !important; padding:20px; '; text-align: center; word-wrap: break-word;> <span style='color:#a55571;font-size:1.5499999999999998em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>alpha&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>infty&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>ln&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sum&nbsp;</span> <span style='color:#B18904;font-size:1.8499999999999996em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>left&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>leq&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>tilde&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>biggl&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>ast&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>ell&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>theta&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>xi&nbsp;</span> <span style='color:#725394;font-size:1.4em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>pi&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>dagger&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>displaystyle&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>otimes&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>underline&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>beta&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>bigg&nbsp;</span> <span style='color:#725394;font-size:1.4em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>prime&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>prod&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>quad&nbsp;</span> <span style='color:#725394;font-size:1.4em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>delta&nbsp;</span> <span style='color:#00b4ff;font-size:1.25em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>gamma&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>varepsilon&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>exp&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>kappa&nbsp;</span> <span style='color:#B18904;font-size:1.8499999999999996em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>right&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>lim&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mathbf&nbsp;</span> <span style='color:#00b4ff;font-size:1.25em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mathrm&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>cdots&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>bf&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sim&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>qquad&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>cdot&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sqrt&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>chi&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>wedge&nbsp;</span> <span style='color:#725394;font-size:1.4em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>lambda&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>log&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>bigr&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>epsilon&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sinh&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>dag&nbsp;</span> <span style='color:#a55571;font-size:1.5499999999999998em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>phi&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>tau&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>begin&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>widetilde&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>int&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sin&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>stackrel&nbsp;</span> <span style='color:#bc72d0;font-size:3.4999999999999987em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>frac&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>big&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>hat&nbsp;</span> <span style='color:#FA5858;font-size:1.9999999999999996em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mu&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>rho&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>rangle&nbsp;</span> <span style='color:#00b4ff;font-size:1.25em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>nu&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>bigl&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>widehat&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>end&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>bar&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>vec&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>zeta&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mathcal&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mid&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>nabla&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>perp&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>to&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>ldots&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>matrix&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>in&nbsp;</span> <span style='color:#a55571;font-size:1.5499999999999998em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>partial&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>overline&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>dot&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>biggr&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>simeq&nbsp;</span> <span style='color:#FF0080;font-size:0.9500000000000001em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>array&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>times&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>vert&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>geq&nbsp;</span> <span style='color:#00b4ff;font-size:1.25em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>cal&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>varphi&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>psi&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>langle&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>hbar&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>det&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>dots&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>rightarrow&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>cosh&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>pm&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>eta&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>cos&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>sigma&nbsp;</span> <span style='color:#b82c2c;font-size:0.8em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>equiv&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>approx&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>star&nbsp;</span> <span style='color:#FF5733;font-size:0.65em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>mp&nbsp;</span> <span style='color:#8000FF;font-size:1.1em;white-space: normal;font-family:verdana;display: inline-block;line-height:30px'>omega&nbsp;</span></div></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["from word_cloud.word_cloud_generator import WordCloud\n", "from IPython.core.display import HTML\n", "\n", "wc=WordCloud(use_tfidf=False)\n", "\n", "embed_code=wc.get_embed_code(text=lines)\n", "HTML(embed_code)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}