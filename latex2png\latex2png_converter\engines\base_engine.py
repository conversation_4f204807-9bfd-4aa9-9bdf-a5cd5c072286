"""
渲染引擎基类
定义所有渲染引擎的通用接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from ..core import LaTeXItem

class RenderEngine(ABC):
    """渲染引擎基类"""

    def __init__(self, config):
        self.config = config
        self.engine_name = self.__class__.__name__
        self._is_initialized = False

    @abstractmethod
    def render(self, latex_item: LaTeXItem) -> str:
        """
        渲染LaTeX为PNG图像

        Args:
            latex_item: LaTeX项目对象

        Returns:
            str: 生成的图像文件路径

        Raises:
            Exception: 渲染失败时抛出异常
        """
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """
        检查引擎是否可用

        Returns:
            bool: 引擎是否可用
        """
        pass

    def initialize(self) -> bool:
        """
        初始化引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self._perform_initialization()
            self._is_initialized = True
            return True
        except Exception as e:
            self._handle_initialization_error(e)
            return False

    def _perform_initialization(self):
        """执行具体的初始化操作（子类可重写）"""
        pass

    def _handle_initialization_error(self, error: Exception):
        """处理初始化错误（子类可重写）"""
        pass

    def cleanup(self):
        """清理资源（子类可重写）"""
        pass

    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        return {
            'name': self.engine_name,
            'available': self.is_available(),
            'initialized': self._is_initialized,
            'config': self._get_config_summary()
        }

    def _get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要（子类可重写）"""
        return {
            'dpi': getattr(self.config, 'dpi', 'unknown'),
            'format': getattr(self.config, 'image_format', 'unknown')
        }

    def _generate_image_filename(self, latex_item: LaTeXItem) -> str:
        """生成图像文件名"""
        return f"{latex_item.category}_{latex_item.index:07d}.png"

    def _prepare_latex_code(self, latex_code: str) -> str:
        """准备LaTeX代码（子类可重写）"""
        # 默认实现：确保代码在数学模式中
        if not latex_code.strip():
            raise ValueError("LaTeX代码不能为空")
        
        # 如果没有数学模式标记，添加$符号
        if not (latex_code.startswith('$') or latex_code.startswith('\\[')):
            return f'${latex_code}$'
        
        return latex_code

    def _validate_output(self, image_path: str) -> bool:
        """验证输出文件（子类可重写）"""
        try:
            from pathlib import Path
            path = Path(image_path)
            return path.exists() and path.stat().st_size > 0
        except Exception:
            return False

    def test_render(self, test_latex: str = r"x^2 + y^2 = z^2") -> bool:
        """
        测试渲染功能
        
        Args:
            test_latex: 测试用的LaTeX代码
            
        Returns:
            bool: 测试是否成功
        """
        try:
            from ..core import LaTeXItem
            test_item = LaTeXItem(
                index=0,
                original_latex=test_latex,
                normalized_latex=test_latex,
                category="test"
            )
            
            # 尝试渲染
            result_path = self.render(test_item)
            
            # 验证结果
            return self._validate_output(result_path)
            
        except Exception:
            return False

    def get_supported_features(self) -> Dict[str, bool]:
        """获取支持的功能特性（子类可重写）"""
        return {
            'basic_math': True,
            'complex_expressions': True,
            'environments': True,
            'custom_fonts': False,
            'transparency': True
        }

    def estimate_complexity(self, latex_code: str) -> str:
        """
        估算LaTeX代码复杂度
        
        Returns:
            str: 'simple', 'medium', 'complex'
        """
        # 简单的复杂度估算
        if len(latex_code) < 20:
            return 'simple'
        elif len(latex_code) < 100:
            return 'medium'
        else:
            return 'complex'

    def __enter__(self):
        """上下文管理器入口"""
        self.initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()

    def __str__(self):
        return f"{self.engine_name}(available={self.is_available()})"

    def __repr__(self):
        return f"{self.__class__.__name__}(config={self.config})"
