#!/usr/bin/env python3
"""
LaTeX to PNG 转换器安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "LaTeX公式到PNG图像的批量转换工具"

# 读取requirements.txt
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-'):
                    # 移除版本约束中的注释
                    if ';' in line:
                        line = line.split(';')[0].strip()
                    requirements.append(line)
    return requirements

setup(
    name="latex2png-converter",
    version="1.0.0",
    author="LaTeX2PNG Team",
    author_email="<EMAIL>",
    description="LaTeX公式到PNG图像的批量转换工具",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/latex2png/converter",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Mathematics",
        "Topic :: Text Processing :: Markup :: LaTeX",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "latex2png=latex2png_converter.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "latex2png_converter": [
            "config/*.yaml",
            "docs/*.md",
        ],
    },
    zip_safe=False,
)
