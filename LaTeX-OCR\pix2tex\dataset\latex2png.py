# mostly taken from http://code.google.com/p/latexmath2png/
# install preview.sty

# ==================== 配置区域 ====================
# 在这里修改所有参数，无需使用命令行

# 在文件开头添加日志配置
import logging

# 配置简洁的日志输出
def setup_logging(verbose=False):
    """设置日志级别"""
    if verbose:
        logging.basicConfig(level=logging.DEBUG, format='[%(levelname)s] %(message)s')
    else:
        logging.basicConfig(level=logging.WARNING, format='[%(levelname)s] %(message)s')

# 基础配置
CONFIG = {
    # 工作模式选择 (必须选择其中一种):
    # 'batch'  - 批量处理：从文件中读取多个公式进行批量渲染
    # 'single' - 单个处理：渲染配置中指定的单个公式（测试用）
    # 'list'   - 列表处理：渲染配置中指定的公式列表
    'mode': 'batch',
    
    # 输入配置
    'input': {
        # batch模式：从文件读取公式列表，每行一个公式
        'formula_file': r"D:\formula_ge_dataset\arxiv_25\test.txt",
        
        # single模式：单个公式字符串（用于测试）
        'single_formula': r'\tilde{\bm{u}}(t) = \Phi_{0 \to t}(\tilde{\bm{u}}(0))',
        # \tilde{\bm{u}}(t) = \Phi_{0 \to t}(\tilde{\bm{u}}(0))
        # list模式：公式列表（用于小批量测试）
        'formula_list': [
            r'$x^2 + y^2 = z^2$',
            r'$\frac{d}{dx}f(x) = \lim_{h \to 0} \frac{f(x+h) - f(x)}{h}$',
            r'$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$',
        ]
    },
    
    # 输出配置
    'output': {
        # 输出目录 - 生成的PNG图像保存位置
        'output_dir': r'D:\formula_ge_dataset\arxiv_25\test',
        
        # 输出文件名格式:
        # 'index'     - 使用序号命名: 000001.png, 000002.png, ...
        # 'hash'      - 使用公式哈希命名: a1b2c3d4.png (避免重复)
        # 'formula'   - 使用公式内容命名: x2_y2_z2.png (可能有特殊字符问题)
        'filename_format': 'index',
        
        # 文件名前缀配置:
        # 'auto'      - 自动从输出目录路径生成前缀，智能识别目录特征
        # 'custom'    - 使用下面custom_prefix指定的自定义前缀字符串
        # None        - 不使用前缀，直接使用基础文件名
        'filename_prefix': 'auto',
        'custom_prefix': 'my_dataset',  # 当filename_prefix='custom'时使用此前缀
        
        # 错误日志配置
        'save_error_log': True,  # 是否保存渲染失败的公式到日志文件
        'error_log_file': r'D:\formula_ge_dataset\arxiv_25\render_errors.txt',
    },
    
    # 渲染参数
    'render': {
        # DPI设置 - 影响图像清晰度和文件大小
        # 150-200: 适合网页显示，文件较小
        # 250-300: 适合打印质量，推荐用于数据集
        # 400+: 超高清，文件很大
        'dpi': 150,
        
        # 字体设置 - 影响数学符号的显示效果
        # 'Latin Modern Math' - 默认LaTeX数学字体，兼容性最好
        # 'XITS Math'        - 更现代的数学字体
        # 'Cambria Math'     - Windows系统字体
        'font': 'Latin Modern Math',
        
        # 颜色空间设置:
        # 'gray'  - 灰度图像，适合OCR训练，文件小
        # 'RGB'   - 彩色图像，保留颜色信息，文件大
        'colorspace': 'gray',
        
        # JPEG质量 (1-100)，只在保存为JPEG时有效
        # PNG格式忽略此设置，但影响中间转换过程
        'quality': 90,
    },
    
    # 处理选项
    'processing': {
        # 是否跳过已存在的文件，避免重复渲染
        'skip_existing': True,
        
        # 错误处理策略:
        # 'skip'     - 跳过出错的公式，继续处理其他公式
        # 'stop'     - 遇到错误立即停止整个处理过程
        # 'retry'    - 尝试重新渲染出错的公式（实验性）
        'error_handling': 'skip',
        
        # 界面显示选项
        'show_progress': True,   # 是否显示进度条
        'verbose': False,        # 是否显示详细调试信息（大批量时建议False）
        
        # 批处理参数
        'batch_size': 50,        # 初始批处理大小，系统会自动调整
    }
}

# ==================== 代码实现区域 ====================

import os
import re
import sys
import io
import glob
import tempfile
import shlex
import subprocess
import traceback
import hashlib
from PIL import Image
from tqdm import tqdm
import time
from collections import deque
from tqdm.auto import tqdm


class Latex:
    # 恢复原来的LaTeX文档模板
    BASE = r'''
\documentclass[varwidth]{standalone}
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{fontspec,unicode-math}
\usepackage[active,tightpage,displaymath,textmath]{preview}
\setmathfont{%s}
\DeclareRobustCommand{\bm}[1]{\boldsymbol{#1}}
\begin{document}
\thispagestyle{empty}
%s
\end{document}
'''

    def __init__(self, math, dpi=250, font='Latin Modern Math'):
        '''初始化LaTeX渲染器
        
        Args:
            math (list): LaTeX数学公式列表，每个元素是一个公式字符串
            dpi (int): 输出图像的分辨率，默认250DPI
            font (str): 数学字体名称，默认使用Latin Modern Math
        '''
        # 按照原来render.py的逻辑处理公式
        self.math = []
        for formula in math:
            formula = formula.strip()
            if not formula:
                continue
                
            # 检查是否已经有数学环境
            if (formula.startswith('$') and formula.endswith('$')) or \
               formula.startswith('\\begin{') or \
               formula.startswith('\\[') or \
               formula.startswith('\\('):
                # 已经有数学环境，直接使用
                self.math.append(formula)
            else:
                # 没有数学环境，使用$包围（和render.py保持一致）
                self.math.append(f'$ {formula} $')  # 注意空格，防止转义
        
        self.dpi = dpi    # 图像分辨率
        self.font = font  # 数学字体
        # 计算模板中公式插入位置的行号，用于错误定位
        self.prefix_line = self.BASE.split("\n").index("%s")

    @property
    def template(self):
        return self.BASE % (self.font, "%s")

    def write(self, return_bytes=False):
        '''将数学公式渲染为PNG图像'''
        try:
            # 创建临时工作目录和LaTeX文件
            workdir = tempfile.mkdtemp()
            fd, texfile = tempfile.mkstemp(suffix='.tex', dir=workdir)
            
            # 构建完整的LaTeX文档
            document = self.template % '\n'.join(self.math)
            
            # 将数学公式插入LaTeX模板并写入文件
            with os.fdopen(fd, 'w+', encoding='utf-8') as f:
                f.write(document)

            # 调用转换函数：LaTeX → PDF → PNG
            png, error_index = self.convert_file(
                texfile, workdir, return_bytes=return_bytes)
            
            return png, error_index

        except Exception as e:
            return [], list(range(len(self.math)))
        finally:
            # 清理临时LaTeX文件
            if 'texfile' in locals() and os.path.exists(texfile):
                try:
                    os.remove(texfile)
                except PermissionError:
                    pass

    def convert_file(self, infile, workdir, return_bytes=False):
        '''执行LaTeX编译和图像转换的核心函数'''
        infile = infile.replace('\\', '/')
        workdir = workdir.replace('\\', '/')
        base_name = os.path.splitext(os.path.basename(infile))[0]
        
        try:
            # ========== 第一步：LaTeX → PDF ==========
            cmd = 'xelatex -interaction nonstopmode -file-line-error -output-directory "%s" "%s"' % (
                workdir, infile)
            
            p = subprocess.Popen(
                cmd,
                shell=True,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
                
            sout_bytes, serr_bytes = p.communicate()
            
            if p.returncode != 0:
                return [], list(range(len(self.math)))
            
            # ========== 第二步：PDF → PNG ==========
            pdffile = os.path.join(workdir, base_name + '.pdf')
            pdffile = pdffile.replace('\\', '/')
            
            if not os.path.exists(pdffile):
                return [], list(range(len(self.math)))
            
            pngfile = os.path.join(workdir, base_name + '.png')
            pngfile = pngfile.replace('\\', '/')
            
            if sys.platform == 'win32':
                cmd = 'magick -density %i -colorspace %s "%s" -quality %i "%s"' % (
                    self.dpi,
                    CONFIG['render']['colorspace'],
                    pdffile,
                    CONFIG['render']['quality'],
                    pngfile,
                )
            else:
                cmd = 'convert -density %i -colorspace %s "%s" -quality %i "%s"' % (
                    self.dpi,
                    CONFIG['render']['colorspace'],
                    pdffile,
                    CONFIG['render']['quality'],
                    pngfile,
                )
            
            p = subprocess.Popen(
                cmd,
                shell=True,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            sout_bytes, serr_bytes = p.communicate()
            
            if p.returncode != 0:
                return [], list(range(len(self.math)))
            
            # 检查生成的PNG文件
            if len(self.math) > 1:
                png_files = [pngfile.replace('.png', '')+'-%i.png' % i for i in range(len(self.math))]
            else:
                png_files = [pngfile]
            
            existing_files = [pf for pf in png_files if os.path.exists(pf)]
            
            # ========== 第三步：文件读取 ==========
            try:
                if return_bytes:
                    png = [open(pf, 'rb').read() for pf in existing_files]
                else:
                    png = existing_files
                
                return png, []
            except Exception as e:
                return [], list(range(len(self.math)))
            
        except Exception as e:
            return [], list(range(len(self.math)))


# 全局缓存字典，避免重复渲染相同的公式
__cache = {}


def tex2png(eq, **kwargs):
    '''将单个LaTeX公式转换为PNG（带缓存）
    
    Args:
        eq (str): LaTeX公式字符串
        **kwargs: 传递给Latex类的其他参数
        
    Returns:
        tuple: (PNG字节数据列表, 错误索引列表)
    '''
    if not eq in __cache:
        # 如果缓存中没有，则渲染并缓存结果
        __cache[eq] = Latex(eq, **kwargs).write(return_bytes=True)
    return __cache[eq]


def tex2pil(math, return_error_index=False, **kwargs):
    '''将LaTeX数学公式转换为PIL图像'''
    try:
        # 创建LaTeX渲染器实例
        latex = Latex(math, **kwargs)
        
        # 渲染为PNG图像
        pngs, error_index = latex.write()
        
        # 转换为PIL图像对象
        images = []
        for png_path in pngs:
            try:
                img = Image.open(png_path)
                images.append(img)
            except Exception as e:
                continue
        
        if return_error_index:
            return images, error_index
        else:
            return images
    except Exception as e:
        if return_error_index:
            return [], list(range(len(math)))
        else:
            return []



def extract(text, expression=None):
    """使用正则表达式从文本中提取信息"""
    try:
        # 添加空值检查
        if text is None or expression is None:
            return [], False
            
        pattern = re.compile(expression)
        results = re.findall(pattern, text)
        return results, True if len(results) != 0 else False
    except Exception as e:
        print(f"[调试] extract函数异常: {e}")
        return [], False


def auto_generate_prefix_from_output_dir(output_dir):
    """从输出目录路径自动生成前缀"""
    if not output_dir:
        return None
    
    # 标准化路径分隔符
    path = output_dir.replace('\\', '/').strip('/')
    
    # 分割路径
    parts = [p for p in path.split('/') if p]
    
    if not parts:
        return None
    
    # 提取有意义的部分
    meaningful_parts = []
    
    for part in reversed(parts):  # 从后往前遍历
        # 跳过常见的无意义目录名
        if part.lower() in ['images', 'img', 'pics', 'pictures', 'output', 'out', 'data', 'dataset']:
            continue
        
        # 提取数字和字母组合
        clean_part = re.sub(r'[^\w\d]', '_', part)
        if clean_part and not clean_part.isdigit():
            meaningful_parts.append(clean_part)
        
        # 最多取2个有意义的部分
        if len(meaningful_parts) >= 2:
            break
    
    if meaningful_parts:
        return '_'.join(reversed(meaningful_parts))
    else:
        # 如果没有找到有意义的部分，使用最后一个非images目录
        for part in reversed(parts):
            if part.lower() not in ['images', 'img', 'pics', 'pictures']:
                return re.sub(r'[^\w\d]', '_', part)
        
        return None


def generate_filename(formula, index, format_type='index', prefix_config=None, output_dir=None):
    """生成输出文件名（简化版本）"""
    # 生成基础文件名
    if format_type == 'index':
        base_name = f"{index:06d}"
    elif format_type == 'hash':
        base_name = hashlib.md5(formula.encode()).hexdigest()[:8]
    elif format_type == 'formula':
        # 清理公式中的特殊字符，生成安全的文件名
        safe_name = re.sub(r'[^\w\s-]', '', formula)[:50]
        base_name = re.sub(r'[-\s]+', '_', safe_name)
    else:
        base_name = f"{index:06d}"
    
    # 处理前缀（只在第一次时输出信息）
    prefix = None
    if prefix_config == 'auto':
        prefix = auto_generate_prefix_from_output_dir(output_dir)
        # 只在第一次生成时输出信息
        if index == 0:
            print(f"使用前缀: {prefix}")
    elif prefix_config == 'custom':
        prefix = CONFIG['output'].get('custom_prefix', None)
    elif prefix_config and isinstance(prefix_config, str):
        # 直接指定前缀字符串
        prefix = prefix_config
    
    # 添加前缀
    if prefix and prefix.strip():
        return f"{prefix}_{base_name}"
    else:
        return base_name


def validate_formula(formula):
    """验证公式是否包含可能导致问题的内容"""
    problematic_patterns = [
        r'\\bm\{',  # 需要bm包
        r'\\text\{', # 需要amsmath包
        r'\\boldsymbol\{', # 需要amsmath包
    ]
    
    issues = []
    for pattern in problematic_patterns:
        if re.search(pattern, formula):
            issues.append(pattern)
    
    return issues

def debug_formula(formula, index):
    """调试单个公式"""
    print(f"[调试] 公式 {index}:")
    print(f"  原始内容: {repr(formula)}")
    print(f"  长度: {len(formula)}")
    print(f"  是否包含非ASCII: {any(ord(c) > 127 for c in formula)}")
    
    # 检查是否已经有数学环境
    has_math_env = (
        (formula.startswith('$') and formula.endswith('$')) or
        formula.startswith('\\begin{') or
        formula.startswith('\\[') or
        formula.startswith('\\(')
    )
    print(f"  是否有数学环境: {has_math_env}")

def adaptive_batch_render(formulas, initial_batch_size=50, min_batch_size=1, dpi=250, font='Latin Modern Math'):
    """自适应批量渲染 - 失败时自动降级到单个处理"""
    all_results = []
    error_formulas = []
    
    # 处理所有公式
    for i in range(0, len(formulas), initial_batch_size):
        batch = formulas[i:i+initial_batch_size]
        batch_indices = list(range(i, min(i+initial_batch_size, len(formulas))))
        
        try:
            # 先尝试批量处理
            images, error_indices = tex2pil(batch, return_error_index=True, dpi=dpi, font=font)
            
            if len(error_indices) == 0 and len(images) > 0:
                # 批量处理成功
                all_results.extend([(idx, img) for idx, img in zip(batch_indices, images)])
            else:
                # 批量处理失败，降级到单个处理
                for j, (idx, formula) in enumerate(zip(batch_indices, batch)):
                    try:
                        single_images, single_errors = tex2pil([formula], return_error_index=True, dpi=dpi, font=font)
                        
                        if len(single_errors) == 0 and len(single_images) > 0:
                            all_results.append((idx, single_images[0]))
                        else:
                            error_formulas.append((idx, formula))
                    except Exception as e:
                        error_formulas.append((idx, formula))
                        
        except Exception as e:
            # 整个批次异常，降级到单个处理
            for idx, formula in zip(batch_indices, batch):
                try:
                    single_images, single_errors = tex2pil([formula], return_error_index=True, dpi=dpi, font=font)
                    if len(single_errors) == 0 and len(single_images) > 0:
                        all_results.append((idx, single_images[0]))
                    else:
                        error_formulas.append((idx, formula))
                except:
                    error_formulas.append((idx, formula))
    
    # 转换结果格式
    result_images = [None] * len(formulas)
    for idx, img in all_results:
        if idx < len(result_images):
            result_images[idx] = img
    
    return result_images


def batch_render_formulas(formulas, output_dir, initial_batch_size=50):
    """批量渲染公式，带性能监控版本"""
    verbose = CONFIG['processing'].get('verbose', False)
    
    print(f"开始渲染 {len(formulas)} 个公式...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日志级别
    setup_logging(verbose)
    
    try:
        # 使用自适应批量渲染
        images = adaptive_batch_render(
            formulas, 
            initial_batch_size=initial_batch_size,
            min_batch_size=1,
            dpi=CONFIG['render']['dpi'], 
            font=CONFIG['render']['font']
        )
        
        # 保存成功渲染的图像
        saved_count = 0
        prefix_config = CONFIG['output'].get('filename_prefix', None)
        
        # 创建保存进度条
        save_pbar = tqdm(enumerate(images), total=len(images), desc="保存图像", unit="个")
        
        for i, image in save_pbar:
            if image is not None:
                filename = generate_filename(
                    formulas[i] if i < len(formulas) else f"formula_{i}", 
                    i, 
                    CONFIG['output']['filename_format'],
                    prefix_config=prefix_config,
                    output_dir=output_dir
                )
                output_path = os.path.join(output_dir, f"{filename}.png")
                
                if CONFIG['processing']['skip_existing'] and os.path.exists(output_path):
                    continue
                    
                image.save(output_path)
                saved_count += 1
        
        # 输出最终结果
        success_rate = saved_count / len(formulas) * 100
        print(f"\n✅ 渲染完成: {saved_count}/{len(formulas)} 个公式 ({success_rate:.1f}%)")
        print(f"📁 保存位置: {output_dir}")
        
        return saved_count
        
    except Exception as e:
        print(f"❌ 批量渲染失败: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        return 0

def main_with_config():
    """使用配置区域的参数运行程序"""
    print("=== LaTeX公式渲染工具 ===")
    
    # 确保输出目录存在
    os.makedirs(CONFIG['output']['output_dir'], exist_ok=True)
    
    try:
        if CONFIG['mode'] == 'batch':
            # 模式2: 批量处理文件中的公式
            formula_file = CONFIG['input']['formula_file']
            if not os.path.exists(formula_file):
                raise FileNotFoundError(f"公式文件不存在: {formula_file}")
            
            # 读取所有公式
            with open(formula_file, 'r', encoding='utf-8') as f:
                formulas = [line.strip() for line in f if line.strip()]
            
            print(f"读取了 {len(formulas)} 个公式")
            
            # 直接开始渲染，不进行预检查
            success_count = batch_render_formulas(
                formulas, 
                CONFIG['output']['output_dir'],
                initial_batch_size=CONFIG['processing']['batch_size']
            )
            
            print(f"完成！成功: {success_count}/{len(formulas)} ({success_count/len(formulas)*100:.1f}%)")
            
        elif CONFIG['mode'] == 'single':
            # 模式1: 渲染单个公式（测试用）
            formula = CONFIG['input']['single_formula']
            print(f"渲染单个公式...")
            
            images = tex2pil([formula], 
                           dpi=CONFIG['render']['dpi'], 
                           font=CONFIG['render']['font'])
            
            if images:
                filename = generate_filename(formula, 0, CONFIG['output']['filename_format'])
                output_path = os.path.join(CONFIG['output']['output_dir'], f"{filename}.png")
                images[0].save(output_path)
                print(f"已保存: {output_path}")
            
        elif CONFIG['mode'] == 'list':
            # 模式3: 渲染配置中的公式列表
            formulas = CONFIG['input']['formula_list']
            print(f"渲染 {len(formulas)} 个配置公式...")
            
            images, error_indices = tex2pil(formulas, 
                                          return_error_index=True,
                                          dpi=CONFIG['render']['dpi'], 
                                          font=CONFIG['render']['font'])
            
            success_count = 0
            for i, image in enumerate(images):
                if i not in error_indices:
                    filename = generate_filename(formulas[i], i, CONFIG['output']['filename_format'])
                    output_path = os.path.join(CONFIG['output']['output_dir'], f"{filename}.png")
                    image.save(output_path)
                    success_count += 1
            
            print(f"成功渲染 {success_count} 个公式")
        
        else:
            raise NotImplementedError(f"不支持的模式: {CONFIG['mode']}")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        if CONFIG['processing'].get('verbose', False):
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main_with_config()
