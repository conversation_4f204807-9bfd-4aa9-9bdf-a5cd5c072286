<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="32">
            <item index="0" class="java.lang.String" itemvalue="python-Levenshtein" />
            <item index="1" class="java.lang.String" itemvalue="PyYAML" />
            <item index="2" class="java.lang.String" itemvalue="requests" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="torchvision" />
            <item index="5" class="java.lang.String" itemvalue="x_transformers" />
            <item index="6" class="java.lang.String" itemvalue="pynput" />
            <item index="7" class="java.lang.String" itemvalue="PyQt5" />
            <item index="8" class="java.lang.String" itemvalue="munch" />
            <item index="9" class="java.lang.String" itemvalue="tokenizers" />
            <item index="10" class="java.lang.String" itemvalue="transformers" />
            <item index="11" class="java.lang.String" itemvalue="torchtext" />
            <item index="12" class="java.lang.String" itemvalue="timm" />
            <item index="13" class="java.lang.String" itemvalue="opencv_python_headless" />
            <item index="14" class="java.lang.String" itemvalue="torch" />
            <item index="15" class="java.lang.String" itemvalue="imagesize" />
            <item index="16" class="java.lang.String" itemvalue="PyQtWebEngine" />
            <item index="17" class="java.lang.String" itemvalue="tqdm" />
            <item index="18" class="java.lang.String" itemvalue="chardet" />
            <item index="19" class="java.lang.String" itemvalue="albumentations" />
            <item index="20" class="java.lang.String" itemvalue="pandas" />
            <item index="21" class="java.lang.String" itemvalue="screeninfo" />
            <item index="22" class="java.lang.String" itemvalue="einops" />
            <item index="23" class="java.lang.String" itemvalue="Pillow" />
            <item index="24" class="java.lang.String" itemvalue="editdistance" />
            <item index="25" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="26" class="java.lang.String" itemvalue="pytorch_lightning" />
            <item index="27" class="java.lang.String" itemvalue="wandb" />
            <item index="28" class="java.lang.String" itemvalue="smart_open" />
            <item index="29" class="java.lang.String" itemvalue="metrics" />
            <item index="30" class="java.lang.String" itemvalue="torchsummary" />
            <item index="31" class="java.lang.String" itemvalue="base" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>