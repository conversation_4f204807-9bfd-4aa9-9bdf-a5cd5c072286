"""
批量处理管理器
负责协调各个模块，管理并发处理流程
"""

import asyncio
import time
from pathlib import Path
from typing import List, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import multiprocessing as mp

from .latex_processor import LaTeXProcessor
from .render_engine import RenderEngineManager
from .output_manager import OutputManager
from .error_handler import ErrorHandler
from ..core import LaTeXItem, RenderResult, BatchResult, ProcessStatus, ErrorType
from ..utils.file_utils import read_latex_file, create_output_dirs
from ..monitoring.logger import get_logger
from ..monitoring.progress_tracker import ProgressTracker
from ..monitoring.metrics import PerformanceMetrics

class BatchManager:
    """批量处理管理器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化各个组件
        self.latex_processor = LaTeXProcessor(config.preprocessing)
        self.render_engine = RenderEngineManager(config.render)
        self.output_manager = OutputManager(config.output)
        self.error_handler = ErrorHandler(config.output.error_log_dir)
        
        # 性能监控
        self.performance_metrics = PerformanceMetrics()
        
        self.logger.info("批量处理管理器初始化完成")

    async def process_file(self, input_file: str, output_dir: str, 
                          progress_tracker: Optional[ProgressTracker] = None) -> BatchResult:
        """处理单个输入文件"""
        start_time = time.time()
        
        try:
            # 读取LaTeX代码列表
            latex_codes = read_latex_file(input_file)
            total_count = len(latex_codes)
            
            self.logger.info(f"开始处理 {total_count} 个LaTeX公式")
            
            # 设置进度跟踪
            if progress_tracker:
                progress_tracker.set_total(total_count)
            
            # 启动性能监控
            self.performance_metrics.start_monitoring()
            
            # 创建输出目录
            create_output_dirs(output_dir, self.config.output)
            
            # 分批处理
            all_results = []
            batch_size = self.config.process.batch_size
            
            for batch_id, start_idx in enumerate(range(0, total_count, batch_size)):
                end_idx = min(start_idx + batch_size, total_count)
                batch_codes = latex_codes[start_idx:end_idx]
                
                self.logger.info(f"处理批次 {batch_id + 1}: 项目 {start_idx + 1}-{end_idx}")
                
                # 处理当前批次
                batch_result = await self._process_batch(
                    batch_id, batch_codes, start_idx, progress_tracker
                )
                all_results.extend(batch_result.results)
                
                self.logger.info(f"批次 {batch_id + 1} 完成: {batch_result.success_count}/{len(batch_codes)} 成功")
            
            # 停止性能监控
            self.performance_metrics.stop_monitoring()
            
            # 生成最终结果
            final_result = BatchResult(
                batch_id=-1,  # 表示最终结果
                total_count=total_count,
                success_count=sum(1 for r in all_results if r.status == ProcessStatus.SUCCESS),
                failed_count=sum(1 for r in all_results if r.status == ProcessStatus.FAILED),
                results=all_results,
                processing_time=time.time() - start_time
            )
            
            # 记录错误
            self.error_handler.log_batch_errors(all_results)
            
            # 生成输出文件
            await self.output_manager.generate_outputs(final_result, output_dir)
            
            # 完成进度跟踪
            if progress_tracker:
                progress_tracker.finish()
            
            # 记录最终统计
            self.logger.info(f"处理完成: {final_result.success_count}/{final_result.total_count} 成功 "
                           f"({final_result.success_count/final_result.total_count*100:.2f}%)")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            self.performance_metrics.stop_monitoring()
            raise

    async def _process_batch(self, batch_id: int, latex_codes: List[str], 
                           start_idx: int, progress_tracker: Optional[ProgressTracker]) -> BatchResult:
        """处理单个批次"""
        batch_start_time = time.time()
        results = []
        
        # 创建LaTeX项目列表
        latex_items = [
            LaTeXItem(
                index=start_idx + i, 
                original_latex=code, 
                category="math"
            )
            for i, code in enumerate(latex_codes)
        ]
        
        # 预处理LaTeX代码
        self.logger.debug(f"批次 {batch_id}: 开始预处理")
        for item in latex_items:
            try:
                item.normalized_latex = self.latex_processor.normalize(item.original_latex)
            except Exception as e:
                self.logger.warning(f"预处理失败 {item.index}: {e}")
                item.normalized_latex = item.original_latex
        
        # 并发处理
        max_workers = min(self.config.process.max_workers, len(latex_items))
        self.logger.debug(f"批次 {batch_id}: 使用 {max_workers} 个进程并发处理")
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_item = {
                executor.submit(self._process_single_item_wrapper, item, self.config): item
                for item in latex_items
            }
            
            # 收集结果
            for future in as_completed(future_to_item):
                item = future_to_item[future]
                try:
                    result = future.result(timeout=self.config.process.timeout_seconds)
                    results.append(result)
                    
                    # 记录性能指标
                    success = result.status == ProcessStatus.SUCCESS
                    self.performance_metrics.record_processing(success, result.processing_time)
                    
                    # 更新进度
                    if progress_tracker:
                        progress_tracker.update(1)
                        
                except TimeoutError:
                    # 处理超时
                    error_result = RenderResult(
                        item=item,
                        status=ProcessStatus.FAILED,
                        error_message=f"处理超时 ({self.config.process.timeout_seconds}秒)",
                        error_type=ErrorType.UNACCEPTABLE,
                        processing_time=self.config.process.timeout_seconds
                    )
                    results.append(error_result)
                    self.logger.warning(f"处理超时: {item.index}")
                    
                    if progress_tracker:
                        progress_tracker.update(1)
                        
                except Exception as e:
                    # 处理其他异常
                    error_result = RenderResult(
                        item=item,
                        status=ProcessStatus.FAILED,
                        error_message=str(e),
                        error_type=ErrorType.UNKNOWN,
                        processing_time=0.0
                    )
                    results.append(error_result)
                    self.logger.error(f"处理异常 {item.index}: {e}")
                    
                    if progress_tracker:
                        progress_tracker.update(1)
        
        return BatchResult(
            batch_id=batch_id,
            total_count=len(latex_items),
            success_count=sum(1 for r in results if r.status == ProcessStatus.SUCCESS),
            failed_count=sum(1 for r in results if r.status == ProcessStatus.FAILED),
            results=results,
            processing_time=time.time() - batch_start_time
        )

    def get_processing_statistics(self) -> dict:
        """获取处理统计信息"""
        stats = self.performance_metrics.get_current_stats()
        
        # 添加错误统计
        error_stats = self.error_handler.get_error_statistics()
        stats['errors'] = error_stats
        
        # 添加引擎状态
        engine_status = self.render_engine.get_engine_status()
        stats['engines'] = engine_status
        
        return stats

    def generate_processing_report(self) -> str:
        """生成处理报告"""
        stats = self.get_processing_statistics()
        
        # 性能报告
        performance_report = self.performance_metrics.get_summary_report()
        
        # 错误报告
        error_report = self.error_handler.generate_error_report()
        
        # 引擎报告
        engine_report = self.render_engine.create_engine_report()
        
        # 合并报告
        full_report = f"""
LaTeX2PNG 批量处理报告
=====================

{performance_report}

{error_report}

{engine_report}
        """
        
        return full_report.strip()

    async def process_with_retry(self, input_file: str, output_dir: str, 
                                max_retries: int = 2) -> BatchResult:
        """带重试的处理"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"处理尝试 {attempt + 1}/{max_retries + 1}")
                
                progress_tracker = ProgressTracker(f"尝试{attempt + 1}")
                result = await self.process_file(input_file, output_dir, progress_tracker)
                
                # 检查结果质量
                success_rate = result.success_count / result.total_count if result.total_count > 0 else 0
                if success_rate >= 0.95:  # 95%成功率认为可接受
                    self.logger.info(f"处理成功，成功率: {success_rate:.2%}")
                    return result
                elif attempt < max_retries:
                    self.logger.warning(f"成功率较低 ({success_rate:.2%})，准备重试")
                else:
                    self.logger.warning(f"最终成功率: {success_rate:.2%}")
                    return result
                    
            except Exception as e:
                last_exception = e
                self.logger.error(f"处理尝试 {attempt + 1} 失败: {e}")
                
                if attempt < max_retries:
                    self.logger.info(f"等待后重试...")
                    await asyncio.sleep(5)  # 等待5秒后重试
                else:
                    self.logger.error("所有重试都失败")
                    raise last_exception
        
        raise last_exception or Exception("未知错误")

# 全局函数，用于多进程处理
def _process_single_item_wrapper(item: LaTeXItem, config) -> RenderResult:
    """
    单个项目处理包装函数（用于多进程）
    这个函数必须在模块级别定义才能被pickle序列化
    """
    start_time = time.time()
    
    try:
        # 在子进程中重新创建渲染引擎
        render_engine = RenderEngineManager(config.render)
        
        # 渲染图像
        image_path = render_engine.render(item)
        
        return RenderResult(
            item=item,
            status=ProcessStatus.SUCCESS,
            image_path=image_path,
            processing_time=time.time() - start_time
        )
        
    except Exception as e:
        # 简单的错误分类
        error_type = ErrorType.UNKNOWN
        error_msg = str(e).lower()
        
        if any(pattern in error_msg for pattern in ['latex', 'syntax', 'undefined']):
            error_type = ErrorType.ACCEPTABLE
        elif any(pattern in error_msg for pattern in ['system', 'memory', 'file not found']):
            error_type = ErrorType.UNACCEPTABLE
        
        return RenderResult(
            item=item,
            status=ProcessStatus.FAILED,
            error_message=str(e),
            error_type=error_type,
            processing_time=time.time() - start_time
        )
