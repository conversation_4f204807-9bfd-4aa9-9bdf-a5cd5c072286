"""
核心处理模块
包含LaTeX处理、渲染引擎、批量管理等核心功能
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from enum import Enum

class ProcessStatus(Enum):
    """处理状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

class ErrorType(Enum):
    """错误类型枚举"""
    ACCEPTABLE = "acceptable"      # 可接受错误（LaTeX语法问题）
    UNACCEPTABLE = "unacceptable"  # 不可接受错误（系统问题）
    UNKNOWN = "unknown"            # 未知错误

@dataclass
class LaTeXItem:
    """单个LaTeX处理项"""
    index: int                     # 序号
    original_latex: str            # 原始LaTeX代码
    normalized_latex: str = ""     # 规范化后的LaTeX代码
    category: str = "math"         # 类别标识
    
@dataclass
class RenderResult:
    """渲染结果数据结构"""
    item: LaTeXItem               # LaTeX项目
    status: ProcessStatus         # 处理状态
    image_path: Optional[str] = None    # 生成的图像路径
    error_message: Optional[str] = None # 错误信息
    error_type: Optional[ErrorType] = None # 错误类型
    processing_time: float = 0.0  # 处理耗时（秒）
    metadata: Dict[str, Any] = None # 额外元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class BatchResult:
    """批次处理结果"""
    batch_id: int                 # 批次ID
    total_count: int              # 总数量
    success_count: int = 0        # 成功数量
    failed_count: int = 0         # 失败数量
    results: List[RenderResult] = None # 详细结果列表
    processing_time: float = 0.0  # 批次处理总耗时
    
    def __post_init__(self):
        if self.results is None:
            self.results = []

__all__ = [
    'ProcessStatus',
    'ErrorType', 
    'LaTeXItem',
    'RenderResult',
    'BatchResult'
]
