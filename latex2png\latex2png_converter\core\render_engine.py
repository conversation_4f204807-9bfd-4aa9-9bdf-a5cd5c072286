"""
渲染引擎管理器
负责协调不同的渲染引擎
"""

from typing import Optional, Dict, List, Any
from pathlib import Path
from ..engines.texlive_engine import TeXLiveEngine
from ..engines.sympy_engine import SympyEngine
from ..engines.base_engine import RenderEngine
from ..core import LaTeXItem
from ..monitoring.logger import get_logger

class RenderEngineManager:
    """渲染引擎管理器"""

    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)

        # 初始化渲染引擎
        self.engines = {}
        self.engine_order = ['texlive', 'sympy']  # 优先级顺序
        
        try:
            # 主渲染引擎：TeX Live
            self.engines['texlive'] = TeXLiveEngine(config)
            
            # 备用引擎：SymPy（占位实现）
            self.engines['sympy'] = SympyEngine(config)
            
        except Exception as e:
            self.logger.error(f"渲染引擎初始化失败: {e}")
            raise

        # 当前可用的引擎
        self._available_engines = None
        self._primary_engine = None
        self._fallback_engines = []
        
        self._update_available_engines()
        self.logger.info("渲染引擎管理器初始化完成")

    def render(self, latex_item: LaTeXItem) -> str:
        """渲染LaTeX为PNG图像"""
        last_error = None
        
        # 按优先级尝试各个引擎
        for engine_name in self.engine_order:
            engine = self.engines.get(engine_name)
            if not engine or not engine.is_available():
                continue
                
            try:
                self.logger.debug(f"尝试使用 {engine_name} 引擎渲染: {latex_item.index}")
                image_path = engine.render(latex_item)
                
                # 验证渲染结果
                if self._validate_render_result(image_path):
                    self.logger.debug(f"{engine_name} 引擎渲染成功: {latex_item.index}")
                    return image_path
                else:
                    raise RuntimeError(f"渲染结果验证失败: {image_path}")
                    
            except Exception as e:
                last_error = e
                self.logger.warning(f"{engine_name} 引擎渲染失败: {e}")
                continue

        # 所有引擎都失败
        error_msg = f"所有渲染引擎都失败，最后错误: {last_error}"
        self.logger.error(error_msg)
        raise Exception(error_msg)

    def get_engine_status(self) -> Dict[str, Dict[str, Any]]:
        """获取引擎状态"""
        status = {}
        
        for name, engine in self.engines.items():
            try:
                status[name] = {
                    'available': engine.is_available(),
                    'initialized': engine._is_initialized,
                    'info': engine.get_engine_info(),
                    'features': engine.get_supported_features()
                }
            except Exception as e:
                status[name] = {
                    'available': False,
                    'error': str(e),
                    'initialized': False
                }
        
        return status

    def get_recommended_engine(self, latex_code: str) -> Optional[str]:
        """根据LaTeX代码推荐最适合的引擎"""
        # 检查代码复杂度
        complexity = self._estimate_complexity(latex_code)
        
        # 根据复杂度推荐引擎
        if complexity == 'simple':
            # 简单表达式优先使用SymPy（如果可用）
            if self.engines.get('sympy') and self.engines['sympy'].is_available():
                return 'sympy'
        
        # 默认使用TeX Live
        if self.engines.get('texlive') and self.engines['texlive'].is_available():
            return 'texlive'
        
        # 返回第一个可用的引擎
        for name, engine in self.engines.items():
            if engine.is_available():
                return name
        
        return None

    def render_with_specific_engine(self, latex_item: LaTeXItem, engine_name: str) -> str:
        """使用指定引擎渲染"""
        engine = self.engines.get(engine_name)
        if not engine:
            raise ValueError(f"未知引擎: {engine_name}")
        
        if not engine.is_available():
            raise RuntimeError(f"引擎 {engine_name} 不可用")
        
        return engine.render(latex_item)

    def initialize_engines(self) -> Dict[str, bool]:
        """初始化所有引擎"""
        results = {}
        
        for name, engine in self.engines.items():
            try:
                success = engine.initialize()
                results[name] = success
                if success:
                    self.logger.info(f"引擎 {name} 初始化成功")
                else:
                    self.logger.warning(f"引擎 {name} 初始化失败")
            except Exception as e:
                results[name] = False
                self.logger.error(f"引擎 {name} 初始化异常: {e}")
        
        self._update_available_engines()
        return results

    def cleanup_engines(self):
        """清理所有引擎资源"""
        for name, engine in self.engines.items():
            try:
                engine.cleanup()
                self.logger.debug(f"引擎 {name} 清理完成")
            except Exception as e:
                self.logger.warning(f"引擎 {name} 清理时出现警告: {e}")

    def test_all_engines(self, test_latex: str = r"x^2 + y^2 = z^2") -> Dict[str, bool]:
        """测试所有引擎"""
        results = {}
        
        for name, engine in self.engines.items():
            try:
                if engine.is_available():
                    success = engine.test_render(test_latex)
                    results[name] = success
                    self.logger.info(f"引擎 {name} 测试: {'通过' if success else '失败'}")
                else:
                    results[name] = False
                    self.logger.info(f"引擎 {name} 不可用，跳过测试")
            except Exception as e:
                results[name] = False
                self.logger.error(f"引擎 {name} 测试异常: {e}")
        
        return results

    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = {
            'total_engines': len(self.engines),
            'available_engines': len(self._available_engines or []),
            'primary_engine': self._primary_engine,
            'fallback_engines': self._fallback_engines,
            'engine_details': {}
        }
        
        for name, engine in self.engines.items():
            stats['engine_details'][name] = {
                'available': engine.is_available(),
                'class': engine.__class__.__name__,
                'features': engine.get_supported_features()
            }
        
        return stats

    def _update_available_engines(self):
        """更新可用引擎列表"""
        available = []
        
        for name in self.engine_order:
            engine = self.engines.get(name)
            if engine and engine.is_available():
                available.append(name)
        
        self._available_engines = available
        
        # 设置主引擎和备用引擎
        if available:
            self._primary_engine = available[0]
            self._fallback_engines = available[1:]
        else:
            self._primary_engine = None
            self._fallback_engines = []
        
        self.logger.info(f"可用引擎: {available}")

    def _validate_render_result(self, image_path: str) -> bool:
        """验证渲染结果"""
        try:
            path = Path(image_path)
            if not path.exists():
                return False
            
            # 检查文件大小
            if path.stat().st_size == 0:
                return False
            
            # 可以添加更多验证逻辑，如检查图像格式等
            return True
            
        except Exception as e:
            self.logger.debug(f"渲染结果验证失败: {e}")
            return False

    def _estimate_complexity(self, latex_code: str) -> str:
        """估算LaTeX代码复杂度"""
        # 使用第一个可用引擎的复杂度估算
        for engine in self.engines.values():
            if engine.is_available():
                return engine.estimate_complexity(latex_code)
        
        # 默认复杂度估算
        if len(latex_code) < 20:
            return 'simple'
        elif len(latex_code) < 100:
            return 'medium'
        else:
            return 'complex'

    def create_engine_report(self) -> str:
        """创建引擎状态报告"""
        status = self.get_engine_status()
        stats = self.get_engine_statistics()
        
        report = f"""
渲染引擎状态报告:
================

总体统计:
- 总引擎数: {stats['total_engines']}
- 可用引擎数: {stats['available_engines']}
- 主引擎: {stats['primary_engine'] or '无'}
- 备用引擎: {', '.join(stats['fallback_engines']) or '无'}

引擎详情:
"""
        
        for name, info in status.items():
            report += f"\n{name.upper()} 引擎:\n"
            report += f"  - 可用性: {'是' if info.get('available') else '否'}\n"
            report += f"  - 初始化: {'是' if info.get('initialized') else '否'}\n"
            
            if 'error' in info:
                report += f"  - 错误: {info['error']}\n"
            
            features = info.get('features', {})
            if features:
                report += f"  - 支持特性: {', '.join(k for k, v in features.items() if v)}\n"
        
        return report.strip()

    def __enter__(self):
        """上下文管理器入口"""
        self.initialize_engines()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup_engines()
