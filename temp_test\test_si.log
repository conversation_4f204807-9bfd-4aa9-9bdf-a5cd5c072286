This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex 2025.7.25)  28 JUL 2025 22:45
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**test_si.tex
(./test_si.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/siunitx/siunitx.sty
Package: siunitx 2025-07-09 v3.4.14 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count283
\l__siunitx_number_exponent_fixed_int=\count284
\l__siunitx_number_min_decimal_int=\count285
\l__siunitx_number_min_integer_int=\count286
\l__siunitx_number_round_precision_int=\count287
\l__siunitx_number_lower_threshold_int=\count288
\l__siunitx_number_upper_threshold_int=\count289
\l__siunitx_number_group_first_int=\count290
\l__siunitx_number_group_size_int=\count291
\l__siunitx_number_group_minimum_int=\count292
\l__siunitx_angle_tmp_dim=\dimen149
\l__siunitx_angle_marker_box=\box53
\l__siunitx_angle_unit_box=\box54
\l__siunitx_compound_count_int=\count293

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/translations/translations.s
ty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)
 (d:/Program Files/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count294
)
(d:/Program Files/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
(d:/Program Files/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(d:/Program Files/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode is ignored in DVI mode.
))
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen150
))
\l__siunitx_table_tmp_box=\box55
\l__siunitx_table_tmp_dim=\dimen151
\l__siunitx_table_column_width_dim=\dimen152
\l__siunitx_table_integer_box=\box56
\l__siunitx_table_decimal_box=\box57
\l__siunitx_table_uncert_box=\box58
\l__siunitx_table_before_box=\box59
\l__siunitx_table_after_box=\box60
\l__siunitx_table_before_dim=\dimen153
\l__siunitx_table_carry_dim=\dimen154
\l__siunitx_unit_tmp_int=\count295
\l__siunitx_unit_position_int=\count296
\l__siunitx_unit_total_int=\count297

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen155
\ar@mcellbox=\box61
\extrarowheight=\dimen156
\NC@list=\toks18
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box62
))
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 3.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/color.sty
Package: color 2025/01/14 v1.3e Standard LaTeX Color (DPC)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: dvips.def on input line 149.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def
File: dvips.def 2022/09/22 v3.1e Graphics/color driver for dvips
)
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
)
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx))
(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d
ef
File: l3backend-dvips.def 2025-06-09 L3 backend support: dvips
\l__pdfannot_backend_content_box=\box63
\l__pdfannot_backend_model_box=\box64
\g__pdfannot_backend_int=\count298
\g__pdfannot_backend_link_int=\count299
\g__pdfannot_backend_link_sf_int=\count300
)
No file test_si.aux.
\openout1 = `test_si.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 3.
LaTeX Font Info:    ... okay on input line 3.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/translations/translations-b
asic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 3.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 4.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 4.


[1

] (./test_si.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
 ***********
 ) 
Here is how much of TeX's memory you used:
 5243 strings out of 467817
 143989 string characters out of 5429129
 509630 words of memory out of 5000000
 33895 multiletter control sequences out of 15000+600000
 626825 words of font info for 40 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 73i,5n,80p,224b,540s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on test_si.dvi (1 page, 372 bytes).
