#!/usr/bin/env python3
"""
基于配置文件的LaTeX2PNG启动器
读取user_config.yaml配置文件，自动设置环境并运行转换
"""

import sys
import os
import time
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config, setup_environment_from_config, validate_config, print_config_summary
from latex_preprocessor import preprocess_latex

def get_background_settings(render_config):
    """
    获取背景设置，处理向后兼容性

    Args:
        render_config: 渲染配置字典

    Returns:
        tuple: (transparent, facecolor) 用于plt.savefig
    """
    # 检查是否有新的背景配置
    background_config = render_config.get('background')

    if background_config:
        bg_type = background_config.get('type', 'transparent')

        if bg_type == "transparent":
            return True, 'none'
        elif bg_type == "solid_color":
            color = background_config.get('color', 'white')
            return False, color
        elif bg_type == "image":
            # 图片背景暂未实现，回退到白色背景
            print("警告：图片背景功能暂未实现，使用白色背景")
            return False, 'white'
        else:
            # 未知类型，使用默认透明背景
            print(f"警告：未知背景类型: {bg_type}，使用透明背景")
            return True, 'none'
    else:
        # 使用旧的配置方式（向后兼容）
        transparent = render_config.get('transparent_background', True)
        return transparent, 'none' if transparent else 'white'

def get_figure_size(render_config):
    """
    获取图形尺寸，支持自适应

    Args:
        render_config: 渲染配置字典

    Returns:
        tuple or None: 图形尺寸，None表示自适应
    """
    # 检查是否启用自适应尺寸
    if render_config.get('auto_size', True):
        return None  # 自适应模式

    # 检查是否有指定的figure_size
    figure_size = render_config.get('figure_size')
    if figure_size and isinstance(figure_size, (list, tuple)) and len(figure_size) == 2:
        return tuple(figure_size)

    # 默认尺寸（向后兼容）
    return (8, 2)

def render_single_formula(args):
    """渲染单个公式的工作函数（模块级别函数，支持多进程）"""
    i, latex_code, images_dir, render_config, full_config = args

    try:
        # 重新配置matplotlib（每个进程需要独立配置）
        import matplotlib
        import matplotlib.pyplot as plt
        from pathlib import Path
        import gc

        # 优化matplotlib配置（减少重复配置）
        matplotlib.use('Agg')  # 强制使用非交互式后端

        # 只在必要时设置matplotlib参数
        if not hasattr(matplotlib.rcParams, '_latex2png_configured'):
            matplotlib.rcParams.update({
                'figure.max_open_warning': 0,  # 禁用打开图形过多的警告
                'axes.unicode_minus': False,  # 禁用unicode minus
            })

            if render_config.get('use_external_latex', True):
                matplotlib.rcParams['text.usetex'] = True
                # 简化LaTeX preamble以提升速度
                matplotlib.rcParams['text.latex.preamble'] = r'''
                \usepackage{amsmath}
                \usepackage{amsfonts}
                \usepackage{amssymb}
                '''
            else:
                matplotlib.rcParams['text.usetex'] = False
                matplotlib.rcParams['mathtext.fontset'] = 'cm'

            # 标记已配置，避免重复配置
            matplotlib.rcParams._latex2png_configured = True

        # 每次设置的参数（可能变化的）
        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 150)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 150)
        matplotlib.rcParams['font.size'] = render_config.get('font_size', 12)

        # 高性能模式：跳过预处理器以获得最大速度
        # 只有在配置明确启用预处理时才执行
        preprocessing_enabled = full_config.get('preprocessing', {}).get('enable_preprocessing', False)
        if preprocessing_enabled:
            if i == 0:  # 只在第一个公式时输出调试信息
                print("🔧 预处理器已启用")
            processed_latex, was_modified = preprocess_latex(latex_code, full_config)
        else:
            if i == 0:  # 只在第一个公式时输出调试信息
                print("🚀 预处理器已禁用（高性能模式）")
            processed_latex = latex_code  # 直接使用原始LaTeX代码

        # 获取背景设置
        transparent, facecolor = get_background_settings(render_config)

        # 创建自适应尺寸的图形
        if render_config.get('auto_size', True):
            # 自适应模式：使用极小的初始尺寸，完全依赖bbox_inches='tight'
            fig = plt.figure(figsize=(0.1, 0.1), facecolor=facecolor if not transparent else 'none')
            ax = fig.add_subplot(111)
        else:
            # 固定尺寸模式
            figsize = get_figure_size(render_config)
            fig, ax = plt.subplots(figsize=figsize, facecolor=facecolor if not transparent else 'none')

        # 渲染LaTeX文本（自适应尺寸的关键）
        ax.text(0.5, 0.5, f'${processed_latex}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=render_config.get('font_size', 12))
        ax.axis('off')

        # 生成文件名（从配置中读取前缀和位数）
        output_config = full_config.get('output', {})
        prefix = output_config.get('image_prefix', 'formula_')
        digits = output_config.get('image_digits', 6)
        filename = f"{prefix}{i+1:0{digits}d}.png"
        filepath = Path(images_dir) / filename

        # 调试信息：只在第一个公式时输出文件命名配置
        if i == 0:
            print(f"📁 文件命名配置: 前缀='{prefix}', 位数={digits}, 示例='{filename}'")

        # 保存图像（严格贴合公式边缘）
        if render_config.get('auto_size', True):
            # 自适应模式：最小边距，严格贴合内容
            padding = 0.01  # 极小边距
            fig.savefig(filepath,
                       dpi=render_config.get('dpi', 150),
                       bbox_inches='tight',  # 自动裁剪到内容边界
                       pad_inches=padding,   # 极小边距，严格贴合
                       transparent=transparent,
                       facecolor=facecolor,
                       format='png')
        else:
            # 固定尺寸模式：使用配置的边距
            padding = render_config.get('padding_inches', 0.05)
            fig.savefig(filepath,
                       dpi=render_config.get('dpi', 150),
                       bbox_inches='tight',
                       pad_inches=padding,
                       transparent=transparent,
                       facecolor=facecolor,
                       format='png')

        # 立即关闭并清理
        plt.close(fig)
        del fig, ax
        gc.collect()  # 强制垃圾回收

        # 验证输出
        if filepath.exists() and filepath.stat().st_size > 0:
            return True, i, latex_code, None
        else:
            return False, i, latex_code, "文件未生成或为空"

    except Exception as e:
        # 确保清理所有matplotlib对象
        plt.close('all')
        gc.collect()

        # 保留详细错误信息用于调试
        error_msg = str(e)

        # 对于LaTeX编译错误，提取更多有用信息
        if "latex was not able to process" in error_msg:
            # 查找具体的LaTeX错误信息
            lines = error_msg.split('\n')
            latex_error_lines = []
            for line in lines:
                if any(keyword in line.lower() for keyword in ['error', 'undefined', 'missing', 'extra', '!']):
                    latex_error_lines.append(line.strip())

            if latex_error_lines:
                error_msg = "LaTeX编译错误: " + "; ".join(latex_error_lines[:3])  # 取前3个错误行
            else:
                error_msg = "LaTeX编译错误: " + error_msg[:200]  # 保留更多原始信息
        else:
            # 保留原始错误信息的前200个字符
            error_msg = error_msg[:200] + "..." if len(error_msg) > 200 else error_msg

        return False, i, latex_code, error_msg

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="LaTeX2PNG转换器 - 高性能版本（默认使用高性能配置）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_with_config.py input.txt                # 使用高性能配置处理文件
  python run_with_config.py input.txt -o ./output    # 指定输出目录
  python run_with_config.py input.txt --standard     # 使用标准配置
  python run_with_config.py --test-engines           # 测试引擎
  python run_with_config.py --config-test            # 测试配置

配置说明:
  默认使用高性能配置（推荐大批量处理，200+张/秒）
  使用 --standard 切换到标准配置
        """
    )

    parser.add_argument('input_file', nargs='?',
                       help='输入文件路径（可选，使用配置文件默认值）')

    parser.add_argument('-o', '--output-dir',
                       help='输出目录（覆盖配置文件设置）')

    parser.add_argument('-c', '--config',
                       help='配置文件路径（可选，默认自动选择）')

    parser.add_argument('--standard', action='store_true',
                       help='使用标准配置而非高性能配置')

    parser.add_argument('--test-engines', action='store_true',
                       help='测试渲染引擎')

    parser.add_argument('--config-test', action='store_true',
                       help='测试配置文件加载')

    parser.add_argument('--verbose', action='store_true',
                       help='详细输出')

    parser.add_argument('--dry-run', action='store_true',
                       help='试运行（不实际处理）')

    return parser.parse_args()

def test_engines_with_config(config):
    """使用配置测试引擎"""
    print("测试渲染引擎...")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        import tempfile
        
        # 从配置获取渲染设置
        render_config = config.get('render', {})
        
        # 配置matplotlib
        if render_config.get('use_external_latex', True):
            matplotlib.rcParams['text.usetex'] = True
            matplotlib.rcParams['text.latex.preamble'] = r'''
            \usepackage{amsmath}
            \usepackage{amsfonts}
            \usepackage{amssymb}
            \usepackage{mathtools}
            \usepackage{bm}
            \usepackage{siunitx}
            \usepackage{xcolor}

            % 自定义命令定义（只保留最常用的）
            \newcommand{\myRe}{\text{Re}}
            \newcommand{\hbl}{h_{\text{bl}}}
            \newcommand{\surften}{\sigma}
            \newcommand{\f}{\frac}
            \newcommand{\p}{\partial}
            \newcommand{\mr}[1]{\mathrm{#1}}
            \newcommand{\mb}[1]{\mathbf{#1}}
            \newcommand{\VEC}[1]{\mathbf{#1}}
            \newcommand{\MAT}[1]{\mathbf{#1}}
            '''
            print("✓ 配置为使用外部TeX Live")
        else:
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'
            print("✓ 配置为使用matplotlib mathtext")
        
        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 300)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 300)
        
        # 测试渲染
        test_formulas = [
            r"x^2 + y^2 = z^2",
            r"\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}",
            r"\begin{bmatrix} a & b \\ c & d \end{bmatrix}"
        ]
        
        success_count = 0
        
        for i, formula in enumerate(test_formulas):
            try:
                figsize = get_figure_size(render_config)
                fig, ax = plt.subplots(figsize=figsize)
                ax.text(0.5, 0.5, f'${formula}$',
                       horizontalalignment='center',
                       verticalalignment='center',
                       transform=ax.transAxes,
                       fontsize=render_config.get('font_size', 12))
                ax.axis('off')
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                    temp_file = f.name
                
                plt.savefig(temp_file, 
                           dpi=render_config.get('dpi', 300),
                           bbox_inches='tight',
                           transparent=render_config.get('transparent_background', True))
                plt.close(fig)
                
                if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
                    print(f"✓ 测试公式 {i+1}: 成功 ({Path(temp_file).stat().st_size} bytes)")
                    success_count += 1
                    os.unlink(temp_file)
                else:
                    print(f"✗ 测试公式 {i+1}: 失败")
                    
            except Exception as e:
                print(f"✗ 测试公式 {i+1}: 异常 - {e}")
                plt.close('all')
        
        print(f"\n引擎测试结果: {success_count}/{len(test_formulas)} 成功")
        return success_count == len(test_formulas)
        
    except Exception as e:
        print(f"引擎测试失败: {e}")
        return False

def run_conversion_with_config(config, input_file, output_dir, verbose=False, dry_run=False):
    """使用配置运行转换"""
    print("开始LaTeX2PNG转换...")

    # 系统级优化设置
    import os
    import gc

    # 设置进程优先级（降低以减少系统卡顿）
    try:
        import psutil
        current_process = psutil.Process()
        current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if os.name == 'nt' else 10)
        print("✓ 已设置进程优先级为低优先级，减少系统卡顿")
    except ImportError:
        print("⚠ 未安装psutil，无法设置进程优先级")
    except Exception as e:
        print(f"⚠ 设置进程优先级失败: {e}")

    # 优化垃圾回收
    gc.set_threshold(700, 10, 10)  # 更激进的垃圾回收

    # 获取配置参数
    io_config = config.get('io', {})
    render_config = config.get('render', {})
    perf_config = config.get('performance', {})
    
    # 确定输入文件
    if not input_file:
        input_file = io_config.get('default_input_file')
        if not input_file:
            print("错误: 未指定输入文件，且配置中无默认输入文件")
            return False
    
    # 确定输出目录
    if not output_dir:
        output_dir = io_config.get('default_output_dir', './output')
    
    # 验证输入文件
    if not Path(input_file).exists():
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    print(f"DPI: {render_config.get('dpi', 300)}")
    print(f"最大并发: {perf_config.get('max_workers', 16)}")
    print(f"批次大小: {perf_config.get('batch_size', 2000)}")
    
    if dry_run:
        print("试运行模式 - 不执行实际转换")
        return True
    
    # 调用实际的转换逻辑
    try:
        # 使用原来的matplotlib转换逻辑
        import matplotlib
        import matplotlib.pyplot as plt
        import tempfile
        import json
        import time

        print("正在处理...")

        # 读取输入文件
        with open(input_file, 'r', encoding=io_config.get('input_encoding', 'utf-8')) as f:
            lines = f.readlines()

        # 过滤有效的LaTeX行
        latex_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('%'):
                latex_lines.append(line)

        print(f"找到 {len(latex_lines)} 个LaTeX公式")

        if len(latex_lines) == 0:
            print("警告: 输入文件中没有有效的LaTeX公式")
            return False

        # 创建输出目录结构
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        images_dir = output_path / "images"
        images_dir.mkdir(exist_ok=True)

        # 配置matplotlib
        if render_config.get('use_external_latex', True):
            matplotlib.rcParams['text.usetex'] = True
            matplotlib.rcParams['text.latex.preamble'] = r'''
            \usepackage{amsmath}
            \usepackage{amsfonts}
            \usepackage{amssymb}
            \usepackage{mathtools}
            \usepackage{bm}
            \usepackage{siunitx}
            \usepackage{xcolor}

            % 自定义命令定义（只保留最常用的）
            \newcommand{\myRe}{\text{Re}}
            \newcommand{\hbl}{h_{\text{bl}}}
            \newcommand{\surften}{\sigma}
            \newcommand{\f}{\frac}
            \newcommand{\p}{\partial}
            \newcommand{\mr}[1]{\mathrm{#1}}
            \newcommand{\mb}[1]{\mathbf{#1}}
            \newcommand{\VEC}[1]{\mathbf{#1}}
            \newcommand{\MAT}[1]{\mathbf{#1}}
            '''
            print("✓ 使用TeX Live渲染")
        else:
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'
            print("✓ 使用matplotlib mathtext渲染")

        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 300)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 300)

        # 批量处理
        results = {
            'total': len(latex_lines),
            'success': 0,
            'failed': 0,
            'mapping': {},
            'errors': []
        }

        # 并发处理配置
        max_workers = perf_config.get('max_workers', 16)
        batch_size = perf_config.get('batch_size', 2000)

        print(f"使用并发处理: {max_workers} 个工作进程")

        start_time = time.time()

        # 使用优化的多进程并发处理
        from concurrent.futures import ProcessPoolExecutor, as_completed
        import gc

        # 实现优化的批次处理机制
        total_formulas = len(latex_lines)
        completed = 0

        # 优化并发参数
        optimal_workers = min(max_workers, 16)  # 限制最大进程数，避免过度竞争
        optimal_batch_size = max(batch_size, 500)  # 增大批次，减少进程池创建次数

        print(f"优化并发参数: {optimal_workers} 进程, 批次大小: {optimal_batch_size}")

        # 创建长期运行的进程池（避免频繁创建销毁）
        with ProcessPoolExecutor(max_workers=optimal_workers,
                                mp_context=None,  # 使用默认上下文
                                initializer=None) as executor:

            # 分批处理
            for batch_start in range(0, total_formulas, optimal_batch_size):
                batch_end = min(batch_start + optimal_batch_size, total_formulas)
                batch_lines = latex_lines[batch_start:batch_end]
                batch_size_actual = len(batch_lines)

                print(f"处理批次 {batch_start//optimal_batch_size + 1}: {batch_start+1}-{batch_end} ({batch_size_actual}个公式)")

                # 准备当前批次任务参数（传递完整配置）
                batch_tasks = [(batch_start + i, latex_code, str(images_dir), render_config, config)
                              for i, latex_code in enumerate(batch_lines)]

                # 提交当前批次任务（复用进程池）
                future_to_task = {executor.submit(render_single_formula, task): task
                                 for task in batch_tasks}

                # 收集当前批次结果
                batch_completed = 0
                batch_start_time = time.time()

                for future in as_completed(future_to_task):
                    success, i, latex_code, error = future.result()
                    batch_completed += 1
                    completed += 1

                    if success:
                        results['success'] += 1
                        results['mapping'][f"formula_{i+1:06d}"] = latex_code

                        # 减少输出频率，提升性能
                        if verbose or completed % 100 == 0:
                            elapsed = time.time() - start_time
                            rate = completed / elapsed if elapsed > 0 else 0
                            print(f"  ✓ 完成 {completed}/{total_formulas} ({rate:.1f}/秒): {latex_code[:40]}...")
                    else:
                        results['failed'] += 1
                        error_msg = f"公式 {i+1}: {latex_code}"
                        if error:
                            error_msg += f" - 错误: {error}"
                        results['errors'].append(error_msg)

                # 批次性能统计
                batch_time = time.time() - batch_start_time
                batch_rate = batch_completed / batch_time if batch_time > 0 else 0
                print(f"  批次完成: {batch_completed}/{batch_size_actual} 成功 ({batch_rate:.1f}/秒)")

                # 适度的内存清理（不要过于频繁）
                if batch_start % (optimal_batch_size * 3) == 0:
                    gc.collect()

        total_time = time.time() - start_time

        print(f"\n渲染统计:")
        print(f"  总公式数: {results['total']}")
        print(f"  成功: {results['success']}")
        print(f"  失败: {results['failed']}")
        print(f"  成功率: {results['success']/results['total']*100:.1f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均: {total_time/results['total']*1000:.1f}ms/公式")

        # 生成输出文件
        mapping_file = output_path / "mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(results['mapping'], f, ensure_ascii=False, indent=2)

        # 创建错误日志目录
        error_logs_dir = output_path / "error_logs"
        error_logs_dir.mkdir(exist_ok=True)

        # 保存详细错误日志
        if results['errors']:
            # 提取失败的LaTeX代码
            failed_latex_codes = []

            for error_msg in results['errors']:
                # 提取LaTeX代码（格式：公式 X: LaTeX代码 - 错误: 错误信息）
                if ': ' in error_msg:
                    parts = error_msg.split(': ', 1)
                    if len(parts) >= 2:
                        latex_part = parts[1].strip()
                        # 如果包含错误信息，只取LaTeX代码部分
                        if ' - 错误: ' in latex_part:
                            latex_code = latex_part.split(' - 错误: ')[0].strip()
                        else:
                            latex_code = latex_part
                        failed_latex_codes.append(latex_code)

            # 保存失败的LaTeX代码为txt文件（与输入格式相同）
            if failed_latex_codes:
                failed_latex_file = error_logs_dir / "failed_formulas.txt"
                with open(failed_latex_file, 'w', encoding='utf-8') as f:
                    for latex_code in failed_latex_codes:
                        f.write(f"{latex_code}\n")

                print(f"✓ 失败公式已保存到: {failed_latex_file}")
                print(f"  - 可直接用作输入文件重新测试")
                print(f"  - 失败公式数量: {len(failed_latex_codes)}")

            # 保存错误日志
            error_log_file = error_logs_dir / "error_log.txt"
            with open(error_log_file, 'w', encoding='utf-8') as f:
                f.write("LaTeX批量渲染错误日志\n")
                f.write("=" * 50 + "\n\n")
                for error_msg in results['errors']:
                    f.write(f"{error_msg}\n")

            print(f"✓ 错误日志已保存到: {error_log_file}")

            # 保存所有错误到总文件
            all_errors_file = error_logs_dir / "all_errors.txt"
            with open(all_errors_file, 'w', encoding='utf-8') as f:
                f.write("LaTeX2PNG 渲染错误详细日志\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总错误数: {len(results['errors'])}\n")
                f.write(f"错误率: {len(results['errors'])/results['total']*100:.1f}%\n\n")

                for i, error_msg in enumerate(results['errors'], 1):
                    f.write(f"错误 {i}:\n")
                    f.write(f"{error_msg}\n")
                    f.write("-" * 80 + "\n\n")

            print(f"✓ 详细错误日志已保存到: {all_errors_file}")

        # 生成处理报告
        report = f"""LaTeX2PNG 处理报告
==================

处理统计:
- 总数: {results['total']}
- 成功: {results['success']}
- 失败: {results['failed']}
- 成功率: {results['success']/results['total']*100:.1f}%

性能指标:
- 总耗时: {total_time:.2f}秒
- 处理速率: {results['success']/total_time:.1f}公式/秒

输出文件:
- 图像目录: {images_dir}
- 映射文件: {mapping_file}
- 成功生成: {results['success']} 个PNG文件

错误分析:
- 错误日志目录: {error_logs_dir if results['errors'] else '无错误'}
- 详细错误日志: {error_logs_dir / 'all_errors.txt' if results['errors'] else '无'}
"""

        report_file = output_path / "processing_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"\n处理完成!")
        print(f"成功: {results['success']}/{results['total']} ({results['success']/results['total']*100:.1f}%)")
        print(f"耗时: {total_time:.2f}秒")
        print(f"速率: {results['success']/total_time:.1f}公式/秒")

        if results['failed'] > 0:
            print(f"失败: {results['failed']} 个公式")
            if verbose:
                for error in results['errors'][:5]:  # 只显示前5个错误
                    print(f"  - {error}")

        return results['success'] > 0
        
    except Exception as e:
        print(f"转换过程出错: {e}")
        return False

def find_config_file(config_path):
    """智能查找配置文件"""
    # 尝试多个可能的路径
    possible_paths = [
        config_path,  # 用户指定的路径
        Path(__file__).parent / config_path,  # 脚本目录下的相对路径
        Path(__file__).parent / "config" / "user_config.yaml",  # 脚本目录下的config目录
        Path.cwd() / config_path,  # 当前工作目录下的相对路径
    ]

    for path in possible_paths:
        if Path(path).exists():
            return str(path)

    return config_path  # 如果都找不到，返回原路径

def main():
    """主函数"""
    args = parse_arguments()

    print("LaTeX2PNG 高性能转换器")
    print("=" * 50)

    # 确定配置文件路径
    if args.config:
        # 用户指定了配置文件
        config_file = find_config_file(args.config)
    else:
        # 自动选择配置文件
        script_dir = Path(__file__).parent
        if args.standard:
            config_file = script_dir / "config" / "user_config.yaml"
            print("🔧 使用标准配置")
        else:
            config_file = script_dir / "config" / "high_performance_config.yaml"
            print("🚀 使用高性能配置（推荐大批量处理，200+张/秒）")

        config_file = str(config_file)

    print(f"配置文件: {config_file}")

    # 加载配置
    config = load_user_config(config_file)
    
    if not config:
        print("配置加载失败，退出")
        return 1
    
    # 验证配置
    if not validate_config(config):
        print("配置验证失败，退出")
        return 1
    
    # 设置环境
    if not setup_environment_from_config(config):
        print("环境设置失败，但继续运行")
    
    # 打印配置摘要
    if args.verbose:
        print_config_summary(config)
    
    # 执行相应操作
    if args.config_test:
        print("✓ 配置测试完成")
        return 0
    
    elif args.test_engines:
        if test_engines_with_config(config):
            print("✓ 引擎测试成功")
            return 0
        else:
            print("✗ 引擎测试失败")
            return 1
    
    else:
        # 运行转换
        start_time = time.time()
        
        success = run_conversion_with_config(
            config, 
            args.input_file, 
            args.output_dir,
            args.verbose,
            args.dry_run
        )
        
        elapsed_time = time.time() - start_time
        
        if success:
            print(f"✓ 转换完成，耗时: {elapsed_time:.2f}秒")
            return 0
        else:
            print("✗ 转换失败")
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
