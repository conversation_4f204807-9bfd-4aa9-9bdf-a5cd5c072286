#!/usr/bin/env python3
"""
分析LaTeX渲染失败的原因
详细诊断为什么成功率不是100%
"""

import sys
import os
import time
import matplotlib
import matplotlib.pyplot as plt
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config, setup_environment_from_config

def test_single_formula(latex_code, index, verbose=True):
    """测试单个公式的渲染"""
    try:
        # 创建图形
        fig, ax = plt.subplots(figsize=[8, 2])
        ax.text(0.5, 0.5, f'${latex_code}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=12)
        ax.axis('off')
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name
        
        plt.savefig(temp_file, dpi=150, bbox_inches='tight', transparent=True)
        plt.close(fig)
        
        # 检查结果
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            file_size = Path(temp_file).stat().st_size
            os.unlink(temp_file)  # 清理
            return True, f"成功 ({file_size} bytes)"
        else:
            return False, "文件未生成或为空"
            
    except Exception as e:
        plt.close('all')
        return False, f"异常: {str(e)}"

def analyze_latex_file(input_file, max_test=50):
    """分析LaTeX文件中的公式"""
    print(f"分析文件: {input_file}")
    print("=" * 60)
    
    # 读取文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"文件读取失败: {e}")
        return
    
    # 过滤有效的LaTeX行
    latex_lines = []
    for i, line in enumerate(lines):
        line = line.strip()
        if line and not line.startswith('#') and not line.startswith('%'):
            latex_lines.append((i+1, line))
    
    print(f"总行数: {len(lines)}")
    print(f"有效LaTeX行数: {len(latex_lines)}")
    
    if len(latex_lines) == 0:
        print("没有找到有效的LaTeX公式")
        return
    
    # 限制测试数量
    test_lines = latex_lines[:max_test] if max_test else latex_lines
    print(f"测试数量: {len(test_lines)}")
    print()
    
    # 分类统计
    results = {
        'success': [],
        'syntax_errors': [],
        'package_errors': [],
        'timeout_errors': [],
        'unknown_errors': []
    }
    
    print("开始逐个测试...")
    print("-" * 60)
    
    for i, (line_num, latex_code) in enumerate(test_lines):
        print(f"[{i+1:3d}/{len(test_lines)}] 行{line_num:3d}: {latex_code[:50]}...")
        
        success, message = test_single_formula(latex_code, i+1)
        
        if success:
            results['success'].append((line_num, latex_code))
            print(f"    ✓ {message}")
        else:
            # 分类错误
            error_msg = message.lower()
            if 'syntax' in error_msg or 'parse' in error_msg or 'invalid' in error_msg:
                results['syntax_errors'].append((line_num, latex_code, message))
                print(f"    ✗ 语法错误: {message}")
            elif 'package' in error_msg or 'command' in error_msg or 'undefined' in error_msg:
                results['package_errors'].append((line_num, latex_code, message))
                print(f"    ✗ 包/命令错误: {message}")
            elif 'timeout' in error_msg:
                results['timeout_errors'].append((line_num, latex_code, message))
                print(f"    ✗ 超时错误: {message}")
            else:
                results['unknown_errors'].append((line_num, latex_code, message))
                print(f"    ✗ 未知错误: {message}")
    
    # 生成分析报告
    print("\n" + "=" * 60)
    print("分析报告")
    print("=" * 60)
    
    total = len(test_lines)
    success_count = len(results['success'])
    
    print(f"总测试数: {total}")
    print(f"成功数: {success_count} ({success_count/total*100:.1f}%)")
    print(f"失败数: {total-success_count} ({(total-success_count)/total*100:.1f}%)")
    print()
    
    # 错误分类统计
    print("错误分类:")
    for error_type, errors in results.items():
        if error_type != 'success' and errors:
            print(f"  {error_type}: {len(errors)} 个")
    print()
    
    # 详细错误分析
    if results['syntax_errors']:
        print("语法错误示例:")
        for line_num, latex_code, message in results['syntax_errors'][:3]:
            print(f"  行{line_num}: {latex_code[:30]}... -> {message}")
        print()
    
    if results['package_errors']:
        print("包/命令错误示例:")
        for line_num, latex_code, message in results['package_errors'][:3]:
            print(f"  行{line_num}: {latex_code[:30]}... -> {message}")
        print()
    
    if results['unknown_errors']:
        print("未知错误示例:")
        for line_num, latex_code, message in results['unknown_errors'][:3]:
            print(f"  行{line_num}: {latex_code[:30]}... -> {message}")
        print()
    
    # 保存详细错误报告
    error_report_file = "error_analysis_report.txt"
    with open(error_report_file, 'w', encoding='utf-8') as f:
        f.write("LaTeX渲染失败分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"测试文件: {input_file}\n")
        f.write(f"总测试数: {total}\n")
        f.write(f"成功率: {success_count/total*100:.1f}%\n\n")
        
        for error_type, errors in results.items():
            if error_type != 'success' and errors:
                f.write(f"\n{error_type.upper()} ({len(errors)} 个):\n")
                f.write("-" * 30 + "\n")
                for line_num, latex_code, message in errors:
                    f.write(f"行{line_num}: {latex_code}\n")
                    f.write(f"错误: {message}\n\n")
    
    print(f"详细错误报告已保存到: {error_report_file}")
    
    return results

def main():
    """主函数"""
    print("LaTeX渲染失败原因分析")
    print("=" * 60)
    
    # 加载配置
    config = load_user_config("config/user_config.yaml")
    if not config:
        print("配置加载失败")
        return 1
    
    # 设置环境
    setup_environment_from_config(config)
    
    # 配置matplotlib使用TeX Live
    matplotlib.rcParams['text.usetex'] = True
    matplotlib.rcParams['text.latex.preamble'] = r'''
    \usepackage{amsmath}
    \usepackage{amsfonts}
    \usepackage{amssymb}
    \usepackage{mathtools}
    \usepackage{bm}
    '''
    print("✓ matplotlib配置为使用TeX Live")
    
    # 获取输入文件
    io_config = config.get('io', {})
    input_file = io_config.get('default_input_file')
    
    if not input_file or not Path(input_file).exists():
        print(f"输入文件不存在: {input_file}")
        return 1
    
    # 分析文件
    results = analyze_latex_file(input_file, max_test=100)  # 测试前100个公式
    
    if results:
        total_errors = sum(len(errors) for key, errors in results.items() if key != 'success')
        if total_errors > 0:
            print(f"\n关键发现:")
            print(f"- 成功率不是100%的主要原因是输入数据质量问题")
            print(f"- 建议预处理输入文件，过滤掉有问题的LaTeX代码")
            print(f"- 或者增加错误处理和重试机制")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
