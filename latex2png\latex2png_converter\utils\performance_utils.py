"""
性能监控工具函数
"""

import time
import functools
import psutil
from typing import Callable, Any, Dict, Optional
from ..monitoring.logger import get_logger

def measure_time(func: Callable = None, *, log_result: bool = True) -> Callable:
    """
    测量函数执行时间的装饰器
    
    Args:
        func: 被装饰的函数
        log_result: 是否记录结果到日志
        
    Returns:
        Callable: 装饰后的函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            logger = get_logger(__name__)
            start_time = time.time()
            
            try:
                result = f(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if log_result:
                    logger.debug(f"函数 {f.__name__} 执行时间: {execution_time:.3f}秒")
                
                # 将执行时间添加到结果中（如果结果是字典）
                if isinstance(result, dict):
                    result['_execution_time'] = execution_time
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                if log_result:
                    logger.error(f"函数 {f.__name__} 执行失败 (耗时 {execution_time:.3f}秒): {e}")
                raise
        
        return wrapper
    
    if func is None:
        return decorator
    else:
        return decorator(func)

def memory_usage(func: Callable = None, *, log_result: bool = True) -> Callable:
    """
    监控函数内存使用的装饰器
    
    Args:
        func: 被装饰的函数
        log_result: 是否记录结果到日志
        
    Returns:
        Callable: 装饰后的函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            logger = get_logger(__name__)
            
            # 获取初始内存使用
            process = psutil.Process()
            initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
            
            try:
                result = f(*args, **kwargs)
                
                # 获取最终内存使用
                final_memory = process.memory_info().rss / (1024 * 1024)  # MB
                memory_delta = final_memory - initial_memory
                
                if log_result:
                    logger.debug(f"函数 {f.__name__} 内存变化: {memory_delta:+.2f}MB "
                               f"(初始: {initial_memory:.2f}MB, 最终: {final_memory:.2f}MB)")
                
                # 将内存信息添加到结果中（如果结果是字典）
                if isinstance(result, dict):
                    result['_memory_delta_mb'] = memory_delta
                    result['_final_memory_mb'] = final_memory
                
                return result
                
            except Exception as e:
                final_memory = process.memory_info().rss / (1024 * 1024)  # MB
                memory_delta = final_memory - initial_memory
                
                if log_result:
                    logger.error(f"函数 {f.__name__} 执行失败，内存变化: {memory_delta:+.2f}MB")
                raise
        
        return wrapper
    
    if func is None:
        return decorator
    else:
        return decorator(func)

class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, name: str = "操作", log_result: bool = True):
        self.name = name
        self.log_result = log_result
        self.logger = get_logger(__name__)
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        elapsed = self.elapsed_time
        
        if self.log_result:
            if exc_type is None:
                self.logger.debug(f"{self.name} 完成，耗时: {elapsed:.3f}秒")
            else:
                self.logger.error(f"{self.name} 失败，耗时: {elapsed:.3f}秒")
    
    @property
    def elapsed_time(self) -> float:
        """获取已经过的时间"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time or time.time()
        return end - self.start_time

class MemoryMonitor:
    """内存监控器上下文管理器"""
    
    def __init__(self, name: str = "操作", log_result: bool = True):
        self.name = name
        self.log_result = log_result
        self.logger = get_logger(__name__)
        self.process = psutil.Process()
        self.initial_memory = None
        self.final_memory = None
    
    def __enter__(self):
        self.initial_memory = self.process.memory_info().rss / (1024 * 1024)  # MB
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.final_memory = self.process.memory_info().rss / (1024 * 1024)  # MB
        memory_delta = self.memory_delta
        
        if self.log_result:
            if exc_type is None:
                self.logger.debug(f"{self.name} 完成，内存变化: {memory_delta:+.2f}MB")
            else:
                self.logger.error(f"{self.name} 失败，内存变化: {memory_delta:+.2f}MB")
    
    @property
    def memory_delta(self) -> float:
        """获取内存变化量（MB）"""
        if self.initial_memory is None:
            return 0.0
        
        final = self.final_memory or (self.process.memory_info().rss / (1024 * 1024))
        return final - self.initial_memory
    
    @property
    def current_memory(self) -> float:
        """获取当前内存使用量（MB）"""
        return self.process.memory_info().rss / (1024 * 1024)

def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        Dict[str, Any]: 系统信息
    """
    try:
        cpu_count = psutil.cpu_count()
        cpu_count_logical = psutil.cpu_count(logical=True)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'cpu_count_physical': cpu_count,
            'cpu_count_logical': cpu_count_logical,
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_total_gb': memory.total / (1024**3),
            'memory_available_gb': memory.available / (1024**3),
            'memory_percent': memory.percent,
            'disk_total_gb': disk.total / (1024**3),
            'disk_free_gb': disk.free / (1024**3),
            'disk_percent': (disk.used / disk.total) * 100
        }
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"获取系统信息失败: {e}")
        return {}

def check_system_resources(min_memory_gb: float = 1.0, min_disk_gb: float = 1.0) -> Dict[str, Any]:
    """
    检查系统资源是否充足
    
    Args:
        min_memory_gb: 最小内存要求（GB）
        min_disk_gb: 最小磁盘空间要求（GB）
        
    Returns:
        Dict[str, Any]: 检查结果
    """
    logger = get_logger(__name__)
    
    try:
        system_info = get_system_info()
        
        memory_ok = system_info.get('memory_available_gb', 0) >= min_memory_gb
        disk_ok = system_info.get('disk_free_gb', 0) >= min_disk_gb
        
        result = {
            'memory_sufficient': memory_ok,
            'disk_sufficient': disk_ok,
            'overall_ok': memory_ok and disk_ok,
            'memory_available_gb': system_info.get('memory_available_gb', 0),
            'disk_free_gb': system_info.get('disk_free_gb', 0),
            'memory_required_gb': min_memory_gb,
            'disk_required_gb': min_disk_gb
        }
        
        if not result['overall_ok']:
            logger.warning("系统资源不足:")
            if not memory_ok:
                logger.warning(f"  内存不足: 需要 {min_memory_gb}GB, 可用 {system_info.get('memory_available_gb', 0):.2f}GB")
            if not disk_ok:
                logger.warning(f"  磁盘空间不足: 需要 {min_disk_gb}GB, 可用 {system_info.get('disk_free_gb', 0):.2f}GB")
        
        return result
        
    except Exception as e:
        logger.error(f"检查系统资源失败: {e}")
        return {
            'memory_sufficient': False,
            'disk_sufficient': False,
            'overall_ok': False,
            'error': str(e)
        }

def estimate_processing_time(item_count: int, rate_per_second: float = 1.0) -> Dict[str, float]:
    """
    估算处理时间
    
    Args:
        item_count: 项目数量
        rate_per_second: 每秒处理速率
        
    Returns:
        Dict[str, float]: 时间估算
    """
    if rate_per_second <= 0:
        return {'total_seconds': float('inf')}
    
    total_seconds = item_count / rate_per_second
    
    return {
        'total_seconds': total_seconds,
        'total_minutes': total_seconds / 60,
        'total_hours': total_seconds / 3600,
        'items_per_minute': rate_per_second * 60,
        'items_per_hour': rate_per_second * 3600
    }

class BatchPerformanceTracker:
    """批量处理性能跟踪器"""
    
    def __init__(self, name: str = "批量处理"):
        self.name = name
        self.logger = get_logger(__name__)
        self.start_time = None
        self.processed_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.processing_times = []
    
    def start(self):
        """开始跟踪"""
        self.start_time = time.time()
        self.processed_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.processing_times = []
    
    def record_item(self, success: bool, processing_time: float):
        """记录单个项目处理结果"""
        self.processed_count += 1
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
        self.processing_times.append(processing_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.start_time is None:
            return {}
        
        elapsed_time = time.time() - self.start_time
        
        return {
            'name': self.name,
            'elapsed_time': elapsed_time,
            'processed_count': self.processed_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': (self.success_count / self.processed_count * 100) if self.processed_count > 0 else 0,
            'processing_rate': self.processed_count / elapsed_time if elapsed_time > 0 else 0,
            'average_item_time': sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
            'min_item_time': min(self.processing_times) if self.processing_times else 0,
            'max_item_time': max(self.processing_times) if self.processing_times else 0
        }
