<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="100px" height="100px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<circle cx="50" cy="50" r="37" stroke-width="12" stroke="#0d5df4" stroke-dasharray="58.119464091411174 58.119464091411174" fill="none" stroke-linecap="round">
  <animateTransform attributeName="transform" type="rotate" repeatCount="indefinite" dur="0.9615384615384615s" keyTimes="0;1" values="0 50 50;360 50 50"></animateTransform>
</circle>
<!-- [ldio] generated by https://loading.io/ --></svg>