"""
性能指标监控
"""

import time
import psutil
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from .logger import get_logger

@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float = 0.0
    disk_io_write_mb: float = 0.0
    
@dataclass 
class ProcessingMetrics:
    """处理指标"""
    total_processed: int = 0
    successful: int = 0
    failed: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    processing_times: List[float] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.successful / self.total_processed * 100) if self.total_processed > 0 else 0
    
    @property
    def failure_rate(self) -> float:
        """失败率"""
        return (self.failed / self.total_processed * 100) if self.total_processed > 0 else 0
    
    @property
    def elapsed_time(self) -> float:
        """总耗时"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def average_processing_time(self) -> float:
        """平均处理时间"""
        return sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
    
    @property
    def processing_rate(self) -> float:
        """处理速率（项/秒）"""
        elapsed = self.elapsed_time
        return self.total_processed / elapsed if elapsed > 0 else 0

class PerformanceMetrics:
    """性能指标监控器"""
    
    def __init__(self, monitor_interval: float = 1.0):
        self.monitor_interval = monitor_interval
        self.logger = get_logger(__name__)
        self.snapshots: List[PerformanceSnapshot] = []
        self.processing_metrics = ProcessingMetrics()
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._initial_disk_io = None
        
    def start_monitoring(self):
        """开始性能监控"""
        if self._monitoring:
            return
            
        self._monitoring = True
        self._initial_disk_io = psutil.disk_io_counters()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        self.logger.info("性能监控已启动")
        
    def stop_monitoring(self):
        """停止性能监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=2.0)
        self.processing_metrics.end_time = time.time()
        self.logger.info("性能监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                snapshot = self._take_snapshot()
                self.snapshots.append(snapshot)
                time.sleep(self.monitor_interval)
            except Exception as e:
                self.logger.error(f"性能监控错误: {e}")
                
    def _take_snapshot(self) -> PerformanceSnapshot:
        """获取性能快照"""
        # CPU和内存信息
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        # 磁盘IO信息
        disk_io_read_mb = 0.0
        disk_io_write_mb = 0.0
        
        try:
            current_disk_io = psutil.disk_io_counters()
            if self._initial_disk_io and current_disk_io:
                disk_io_read_mb = (current_disk_io.read_bytes - self._initial_disk_io.read_bytes) / (1024 * 1024)
                disk_io_write_mb = (current_disk_io.write_bytes - self._initial_disk_io.write_bytes) / (1024 * 1024)
        except Exception:
            pass  # 某些系统可能不支持磁盘IO统计
            
        return PerformanceSnapshot(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            disk_io_read_mb=disk_io_read_mb,
            disk_io_write_mb=disk_io_write_mb
        )
        
    def record_processing(self, success: bool, processing_time: float):
        """记录处理结果"""
        self.processing_metrics.total_processed += 1
        if success:
            self.processing_metrics.successful += 1
        else:
            self.processing_metrics.failed += 1
        self.processing_metrics.processing_times.append(processing_time)
        
    def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        current_snapshot = self._take_snapshot() if not self.snapshots else self.snapshots[-1]
        
        return {
            'processing': {
                'total_processed': self.processing_metrics.total_processed,
                'successful': self.processing_metrics.successful,
                'failed': self.processing_metrics.failed,
                'success_rate': self.processing_metrics.success_rate,
                'failure_rate': self.processing_metrics.failure_rate,
                'elapsed_time': self.processing_metrics.elapsed_time,
                'processing_rate': self.processing_metrics.processing_rate,
                'average_processing_time': self.processing_metrics.average_processing_time
            },
            'system': {
                'cpu_percent': current_snapshot.cpu_percent,
                'memory_percent': current_snapshot.memory_percent,
                'memory_used_mb': current_snapshot.memory_used_mb,
                'disk_io_read_mb': current_snapshot.disk_io_read_mb,
                'disk_io_write_mb': current_snapshot.disk_io_write_mb
            }
        }
        
    def get_summary_report(self) -> str:
        """生成汇总报告"""
        stats = self.get_current_stats()
        processing = stats['processing']
        system = stats['system']
        
        # 计算平均系统资源使用
        if self.snapshots:
            avg_cpu = sum(s.cpu_percent for s in self.snapshots) / len(self.snapshots)
            avg_memory = sum(s.memory_percent for s in self.snapshots) / len(self.snapshots)
            max_memory = max(s.memory_used_mb for s in self.snapshots)
        else:
            avg_cpu = system['cpu_percent']
            avg_memory = system['memory_percent'] 
            max_memory = system['memory_used_mb']
            
        report = f"""
性能监控报告:
=============

处理统计:
- 总处理数: {processing['total_processed']}
- 成功数: {processing['successful']}
- 失败数: {processing['failed']}
- 成功率: {processing['success_rate']:.2f}%
- 失败率: {processing['failure_rate']:.2f}%
- 总耗时: {processing['elapsed_time']:.2f}秒
- 处理速率: {processing['processing_rate']:.2f}项/秒
- 平均处理时间: {processing['average_processing_time']:.3f}秒

系统资源:
- 平均CPU使用率: {avg_cpu:.1f}%
- 平均内存使用率: {avg_memory:.1f}%
- 峰值内存使用: {max_memory:.1f}MB
- 磁盘读取: {system['disk_io_read_mb']:.1f}MB
- 磁盘写入: {system['disk_io_write_mb']:.1f}MB
        """
        
        return report.strip()
