# LaTeX2PNG 配置文件使用指南

## 📋 概述

LaTeX2PNG现在支持**配置文件驱动**的方式运行，您只需要：

1. **编辑配置文件** `config/user_config.yaml`
2. **运行启动器** `python run_with_config.py`

无需每次手动输入路径和参数！

## 🚀 快速开始

### 1. 检查配置文件

配置文件位置：`config/user_config.yaml`

**重要路径配置**（请根据您的实际安装路径修改）：
```yaml
system:
  # TeX Live安装路径（必须修改为您的实际路径）
  texlive_path: "D:\\Program Files\\texlive\\2025\\bin\\windows"
  
  # Python环境路径（必须修改为您的实际路径）
  python_path: "D:\\miniforge3\\envs\\formula\\python.exe"
```

### 2. 测试配置

```bash
# 测试配置文件加载
python run_with_config.py --config-test

# 测试渲染引擎
python run_with_config.py --test-engines
```

### 3. 运行转换

```bash
# 使用配置文件中的默认输入文件
python run_with_config.py

# 指定输入文件
python run_with_config.py my_formulas.txt

# 指定输出目录
python run_with_config.py my_formulas.txt -o ./my_output

# 详细输出
python run_with_config.py --verbose
```

## ⚙️ 配置文件详解

### 系统路径配置
```yaml
system:
  texlive_path: "D:\\Program Files\\texlive\\2025\\bin\\windows"  # TeX Live路径
  python_path: "D:\\miniforge3\\envs\\formula\\python.exe"       # Python路径
  auto_configure_path: true                                      # 自动配置PATH
  clean_miktex_paths: true                                       # 清理MiKTeX冲突
```

### 输入输出配置
```yaml
io:
  default_input_file: "examples/sample_input.txt"  # 默认输入文件
  default_output_dir: "./output"                   # 默认输出目录
  input_encoding: "utf-8"                          # 输入文件编码
```

### 渲染质量配置
```yaml
render:
  dpi: 300                          # 图像分辨率（150=预览，300=标准，600=高质量）
  transparent_background: true      # 背景透明
  font_size: 12                     # 字体大小
  use_external_latex: true          # 使用TeX Live（true）还是mathtext（false）
```

### 性能配置
```yaml
performance:
  max_workers: 16                   # 最大并发数（建议=CPU核心数）
  batch_size: 2000                  # 批次大小（内存足够可增大）
  timeout_seconds: 30               # 单项超时时间
```

## 🎯 常用配置预设

### 高质量预设（慢但质量最好）
```yaml
render:
  dpi: 600
  font_size: 14
performance:
  max_workers: 8
  batch_size: 1000
```

### 高速预设（快但质量一般）
```yaml
render:
  dpi: 150
  use_external_latex: false  # 使用mathtext
performance:
  max_workers: 32
  batch_size: 5000
```

### 平衡预设（推荐）
```yaml
render:
  dpi: 300
  use_external_latex: true
performance:
  max_workers: 16
  batch_size: 2000
```

## 📁 输入文件格式

创建文本文件，每行一个LaTeX公式：

```
# 这是注释行，会被忽略
x^2 + y^2 = z^2
\frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}
\begin{bmatrix} a & b \\ c & d \end{bmatrix}
```

## 📊 输出结构

```
output/
├── images/                    # PNG图像文件
│   ├── formula_000001.png
│   ├── formula_000002.png
│   └── ...
├── mapping.json              # 文件名与LaTeX代码映射
├── processing_report.txt     # 处理报告
└── error_logs/               # 错误日志（如果有）
```

## 🔧 故障排除

### 1. 配置文件问题

```bash
# 测试配置加载
python config_loader.py

# 验证配置文件语法
python -c "import yaml; yaml.safe_load(open('config/user_config.yaml'))"
```

### 2. 路径问题

**TeX Live路径错误**：
- 检查路径是否存在：`Test-Path "D:\Program Files\texlive\2025\bin\windows"`
- 查找TeX Live：`python find_texlive_simple.py`

**Python路径错误**：
- 检查Python：`& "D:\miniforge3\envs\formula\python.exe" --version`

### 3. 权限问题

确保有以下权限：
- 读取输入文件
- 写入输出目录
- 执行TeX Live命令

### 4. 性能问题

**内存不足**：
```yaml
performance:
  max_workers: 8      # 减少并发数
  batch_size: 1000    # 减少批次大小
```

**速度太慢**：
```yaml
performance:
  max_workers: 32     # 增加并发数（不超过CPU核心数×2）
  batch_size: 5000    # 增加批次大小
```

## 📝 使用示例

### 示例1：处理默认文件
```bash
python run_with_config.py
```

### 示例2：处理自定义文件
```bash
python run_with_config.py my_formulas.txt -o ./results
```

### 示例3：高质量处理
修改配置文件：
```yaml
render:
  dpi: 600
  font_size: 14
```
然后运行：
```bash
python run_with_config.py my_formulas.txt
```

### 示例4：快速预览
修改配置文件：
```yaml
render:
  dpi: 150
  use_external_latex: false
```
然后运行：
```bash
python run_with_config.py my_formulas.txt
```

## 🎉 优势

使用配置文件的优势：

1. **一次配置，多次使用** - 无需重复输入参数
2. **版本控制友好** - 配置可以纳入版本控制
3. **团队协作** - 团队成员可以共享配置
4. **参数完整** - 支持所有高级参数
5. **预设管理** - 可以保存多套配置预设

## 🔄 从命令行迁移

**之前的方式**：
```bash
python main.py input.txt --output-dir ./output --dpi 300 --max-workers 16
```

**现在的方式**：
1. 在 `config/user_config.yaml` 中设置参数
2. 运行：`python run_with_config.py input.txt`

更简单、更可靠！
