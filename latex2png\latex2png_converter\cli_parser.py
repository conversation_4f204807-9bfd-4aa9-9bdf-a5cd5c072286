"""
命令行参数解析器
"""

import argparse
import sys
from pathlib import Path
from typing import Optional
from .config.render_config import load_config, create_default_config_file
from .monitoring.logger import setup_logger

def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    
    parser = argparse.ArgumentParser(
        prog='latex2png',
        description='LaTeX公式到PNG图像的批量转换工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s input.txt                           # 基本用法
  %(prog)s input.txt --output-dir ./output     # 指定输出目录
  %(prog)s input.txt --config config.yaml     # 使用配置文件
  %(prog)s input.txt --max-workers 20          # 调整并发数
  %(prog)s input.txt --batch-size 3000         # 调整批次大小
  %(prog)s input.txt --dpi 600                 # 高分辨率输出
  %(prog)s input.txt --verbose                 # 详细日志输出
  %(prog)s --create-config                     # 创建默认配置文件

输入文件格式:
  每行一个LaTeX公式，UTF-8编码
  支持注释行（以#或%开头）
  空行将被忽略

输出结构:
  output/
  ├── images/                 # PNG图像文件
  ├── mapping.json           # 文件名与LaTeX代码映射
  ├── processing_statistics.json  # 处理统计
  └── error_logs/            # 错误日志
        """
    )

    # 位置参数
    parser.add_argument(
        'input_file',
        nargs='?',
        help='输入文件路径（每行一个LaTeX公式）'
    )

    # 输出配置
    output_group = parser.add_argument_group('输出配置')
    output_group.add_argument(
        '--output-dir', '-o',
        default='./output',
        help='输出目录路径 (默认: ./output)'
    )
    output_group.add_argument(
        '--images-subdir',
        default='images',
        help='图像子目录名称 (默认: images)'
    )

    # 渲染配置
    render_group = parser.add_argument_group('渲染配置')
    render_group.add_argument(
        '--dpi',
        type=int,
        default=300,
        help='图像DPI设置 (默认: 300)'
    )
    render_group.add_argument(
        '--font-size',
        type=int,
        default=12,
        help='字体大小 (默认: 12)'
    )
    render_group.add_argument(
        '--transparent',
        action='store_true',
        help='使用透明背景'
    )
    render_group.add_argument(
        '--no-transparent',
        action='store_true',
        help='使用白色背景'
    )

    # 处理配置
    process_group = parser.add_argument_group('处理配置')
    process_group.add_argument(
        '--max-workers',
        type=int,
        default=16,
        help='最大并发进程数 (默认: 16)'
    )
    process_group.add_argument(
        '--batch-size',
        type=int,
        default=2000,
        help='批次大小 (默认: 2000)'
    )
    process_group.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='单项处理超时时间（秒） (默认: 30)'
    )
    process_group.add_argument(
        '--retry-attempts',
        type=int,
        default=2,
        help='重试次数 (默认: 2)'
    )

    # 配置文件
    config_group = parser.add_argument_group('配置文件')
    config_group.add_argument(
        '--config', '-c',
        help='配置文件路径 (YAML格式)'
    )
    config_group.add_argument(
        '--create-config',
        action='store_true',
        help='创建默认配置文件并退出'
    )
    config_group.add_argument(
        '--save-config',
        help='保存当前配置到指定文件'
    )

    # 日志和调试
    debug_group = parser.add_argument_group('日志和调试')
    debug_group.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细日志输出'
    )
    debug_group.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，只输出错误'
    )
    debug_group.add_argument(
        '--log-file',
        help='日志文件路径'
    )
    debug_group.add_argument(
        '--debug',
        action='store_true',
        help='调试模式'
    )

    # 预处理配置
    preprocess_group = parser.add_argument_group('预处理配置')
    preprocess_group.add_argument(
        '--enable-level1',
        action='store_true',
        default=True,
        help='启用Level1基础规范化 (默认启用)'
    )
    preprocess_group.add_argument(
        '--disable-level1',
        action='store_true',
        help='禁用Level1基础规范化'
    )
    preprocess_group.add_argument(
        '--enable-level2',
        action='store_true',
        help='启用Level2结构规范化 (实验性)'
    )
    preprocess_group.add_argument(
        '--enable-level3',
        action='store_true',
        help='启用Level3语义规范化 (实验性)'
    )

    # 其他选项
    misc_group = parser.add_argument_group('其他选项')
    misc_group.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行，不实际生成图像'
    )
    misc_group.add_argument(
        '--test-engines',
        action='store_true',
        help='测试渲染引擎并退出'
    )
    misc_group.add_argument(
        '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )

    return parser

def parse_args(args: Optional[list] = None) -> argparse.Namespace:
    """解析命令行参数"""
    parser = create_parser()
    parsed_args = parser.parse_args(args)
    
    # 验证参数
    if not parsed_args.create_config and not parsed_args.test_engines and not parsed_args.input_file:
        parser.error("需要指定输入文件，或使用 --create-config 或 --test-engines 选项")
    
    # 检查输入文件存在性
    if parsed_args.input_file:
        input_path = Path(parsed_args.input_file)
        if not input_path.exists():
            parser.error(f"输入文件不存在: {parsed_args.input_file}")
        if not input_path.is_file():
            parser.error(f"输入路径不是文件: {parsed_args.input_file}")
    
    # 处理互斥选项
    if parsed_args.transparent and parsed_args.no_transparent:
        parser.error("--transparent 和 --no-transparent 不能同时使用")
    
    if parsed_args.verbose and parsed_args.quiet:
        parser.error("--verbose 和 --quiet 不能同时使用")
    
    if parsed_args.disable_level1:
        parsed_args.enable_level1 = False
    
    # 验证数值参数
    if parsed_args.max_workers <= 0:
        parser.error("--max-workers 必须大于0")
    
    if parsed_args.batch_size <= 0:
        parser.error("--batch-size 必须大于0")
    
    if parsed_args.timeout <= 0:
        parser.error("--timeout 必须大于0")
    
    if parsed_args.dpi <= 0:
        parser.error("--dpi 必须大于0")
    
    return parsed_args

def setup_logging_from_args(args: argparse.Namespace):
    """根据命令行参数设置日志"""
    if args.debug:
        log_level = "DEBUG"
    elif args.verbose:
        log_level = "INFO"
    elif args.quiet:
        log_level = "ERROR"
    else:
        log_level = "INFO"
    
    setup_logger(log_level, args.log_file)

def create_config_from_args(args: argparse.Namespace):
    """根据命令行参数创建配置对象"""
    from .config.render_config import AppConfig, RenderConfig, ProcessConfig, OutputConfig, PreprocessingConfig
    
    # 加载基础配置
    if args.config:
        config = load_config(args.config)
    else:
        config = load_config()  # 使用默认配置
    
    # 用命令行参数覆盖配置
    if hasattr(args, 'dpi') and args.dpi:
        config.render.dpi = args.dpi
    
    if hasattr(args, 'font_size') and args.font_size:
        config.render.font_size = args.font_size
    
    if args.transparent:
        config.render.transparent_background = True
    elif args.no_transparent:
        config.render.transparent_background = False
    
    if hasattr(args, 'max_workers') and args.max_workers:
        config.process.max_workers = args.max_workers
    
    if hasattr(args, 'batch_size') and args.batch_size:
        config.process.batch_size = args.batch_size
    
    if hasattr(args, 'timeout') and args.timeout:
        config.process.timeout_seconds = args.timeout
    
    if hasattr(args, 'retry_attempts') and args.retry_attempts:
        config.process.retry_attempts = args.retry_attempts
    
    if hasattr(args, 'output_dir') and args.output_dir:
        config.output.output_dir = args.output_dir
    
    if hasattr(args, 'images_subdir') and args.images_subdir:
        config.output.images_subdir = args.images_subdir
    
    # 预处理配置
    config.preprocessing.level1_enabled = args.enable_level1
    config.preprocessing.level2_enabled = args.enable_level2
    config.preprocessing.level3_enabled = args.enable_level3
    
    # 更新启用级别列表
    config.preprocessing.enabled_levels = []
    if config.preprocessing.level1_enabled:
        config.preprocessing.enabled_levels.append('level1')
    if config.preprocessing.level2_enabled:
        config.preprocessing.enabled_levels.append('level2')
    if config.preprocessing.level3_enabled:
        config.preprocessing.enabled_levels.append('level3')
    
    return config

def handle_special_commands(args: argparse.Namespace) -> bool:
    """
    处理特殊命令（如创建配置文件）
    
    Returns:
        bool: 如果处理了特殊命令返回True，否则返回False
    """
    if args.create_config:
        config_file = args.save_config or "config/default_config.yaml"
        created_file = create_default_config_file(config_file)
        print(f"默认配置文件已创建: {created_file}")
        return True
    
    if args.test_engines:
        # 这里会在main.py中处理
        return False
    
    if args.save_config:
        config = create_config_from_args(args)
        from .config.render_config import save_config
        save_config(config, args.save_config)
        print(f"配置已保存到: {args.save_config}")
        return True
    
    return False
