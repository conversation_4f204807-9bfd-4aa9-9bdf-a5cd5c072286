#!/usr/bin/env python3
"""
LaTeX2PNG 全局启动器
可以从任何位置运行，自动定位配置文件和脚本
"""

import sys
import os
from pathlib import Path

def find_script_directory():
    """查找脚本所在目录"""
    # 获取当前脚本的绝对路径
    script_path = Path(__file__).resolve()
    return script_path.parent

def main():
    """主函数"""
    # 获取脚本目录
    script_dir = find_script_directory()
    
    # 切换到脚本目录
    os.chdir(script_dir)
    
    # 添加脚本目录到Python路径
    sys.path.insert(0, str(script_dir))
    
    print(f"LaTeX2PNG 启动器")
    print(f"脚本目录: {script_dir}")
    print(f"工作目录: {Path.cwd()}")
    print("=" * 50)
    
    # 导入并运行配置启动器
    try:
        from run_with_config import main as run_main
        run_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保在正确的目录中运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
