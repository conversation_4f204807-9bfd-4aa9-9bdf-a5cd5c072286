# LaTeX2PNG 转换器快速开始指南

## 环境要求

- Python 3.8+
- TeX Live 发行版（推荐）或 MiKTeX
- 8GB+ 内存（推荐16GB+）
- 多核CPU

## 安装步骤

### 1. 安装Python依赖

```bash
cd latex2png_converter
pip install -r requirements.txt
```

### 2. 验证TeX Live安装

```bash
latex --version
pdflatex --version
```

如果命令不存在，请安装TeX Live：

**Ubuntu/Debian:**
```bash
sudo apt-get install texlive-full
```

**macOS:**
```bash
brew install --cask mactex
```

**Windows:**
下载并安装 [TeX Live](https://www.tug.org/texlive/) 或 [MiKTeX](https://miktex.org/)

### 3. 测试安装

```bash
python run_tests.py
```

## 基本使用

### 1. 测试渲染引擎

```bash
python main.py --test-engines
```

### 2. 处理示例文件

```bash
python main.py examples/sample_input.txt --output-dir ./output
```

### 3. 查看结果

```bash
ls -la output/
# 应该看到：
# - images/          # PNG图像文件
# - mapping.json     # 文件名与LaTeX代码映射
# - processing_statistics.json  # 处理统计
# - error_logs/      # 错误日志（如果有）
```

## 常用命令

### 基本转换
```bash
python main.py input.txt --output-dir ./output
```

### 高分辨率输出
```bash
python main.py input.txt --dpi 600 --output-dir ./output
```

### 调整并发数
```bash
python main.py input.txt --max-workers 20 --output-dir ./output
```

### 使用配置文件
```bash
# 创建默认配置文件
python main.py --create-config

# 使用配置文件
python main.py input.txt --config config/default_config.yaml
```

### 详细日志输出
```bash
python main.py input.txt --verbose --log-file processing.log
```

## 输入文件格式

创建一个文本文件，每行包含一个LaTeX公式：

```
# 这是注释行
x^2 + y^2 = z^2
\frac{1}{2}\pi r^2
\int_0^\infty e^{-x} dx
\sum_{i=1}^n i = \frac{n(n+1)}{2}
```

## 输出结构

```
output/
├── images/                    # PNG图像文件
│   ├── math_0000001.png
│   ├── math_0000002.png
│   └── ...
├── mapping.json              # 图像文件名与LaTeX代码的映射
├── processing_statistics.json # 处理统计信息
├── processing_summary.txt    # 处理摘要
└── error_logs/               # 错误日志
    ├── latex_syntax_errors.txt
    ├── system_render_errors.txt
    └── unknown_errors.txt
```

## 性能优化

### 1. 调整并发数
根据CPU核心数调整：
```bash
python main.py input.txt --max-workers $(nproc)
```

### 2. 调整批次大小
对于大文件，可以增加批次大小：
```bash
python main.py input.txt --batch-size 5000
```

### 3. 系统资源监控
使用详细模式查看性能指标：
```bash
python main.py input.txt --verbose
```

## 故障排除

### 1. TeX Live相关错误
```bash
# 检查TeX Live安装
which latex
latex --version

# 更新TeX Live包
sudo tlmgr update --all
```

### 2. 内存不足
- 减少并发数：`--max-workers 8`
- 减少批次大小：`--batch-size 1000`
- 增加系统内存

### 3. 权限错误
```bash
# 确保输出目录有写权限
chmod 755 output/
```

### 4. Python依赖问题
```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt
```

## 配置文件示例

创建 `my_config.yaml`：

```yaml
render:
  dpi: 600                    # 高分辨率
  transparent_background: true
  font_size: 14

process:
  max_workers: 20             # 高并发
  batch_size: 3000            # 大批次
  timeout_seconds: 60         # 长超时

output:
  output_dir: "./high_quality_output"
  images_subdir: "images"

preprocessing:
  level1_enabled: true        # 启用基础规范化
  level2_enabled: false       # 禁用高级功能
  level3_enabled: false
```

使用配置文件：
```bash
python main.py input.txt --config my_config.yaml
```

## 批量处理大文件

对于包含数万或数十万公式的大文件：

```bash
# 使用高性能配置
python main.py large_input.txt \
  --max-workers 24 \
  --batch-size 5000 \
  --timeout 60 \
  --output-dir ./large_output \
  --verbose \
  --log-file large_processing.log
```

## 质量检查

处理完成后，检查结果质量：

1. 查看处理摘要：
```bash
cat output/processing_summary.txt
```

2. 检查错误日志：
```bash
ls -la output/error_logs/
```

3. 验证图像质量：
```bash
# 检查图像文件数量
ls output/images/ | wc -l

# 检查图像文件大小
du -sh output/images/
```

## 下一步

- 查看 [README.md](README.md) 了解详细功能
- 查看 [配置文档](config/default_config.yaml) 了解所有配置选项
- 运行测试套件：`python run_tests.py`
- 查看示例输入：`examples/sample_input.txt`
