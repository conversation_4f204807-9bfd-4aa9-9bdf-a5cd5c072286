#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ArXiv下载系统性能测试工具
分析各个环节的效率瓶颈，提供优化建议
"""

import os
import sys
import time
import statistics
import urllib.request
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from arxiv import (
    get_arxiv_ids_from_url, download_paper, parse_arxiv,
    build_single_category_urls, BULK_CONFIG, CONFIG
)

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.results = {}
        
    def time_function(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time
    
    def test_api_request_speed(self, num_tests=5):
        """测试API请求速度"""
        print("=== 测试API请求效率 ===")
        
        # 构建测试URL
        test_urls = build_single_category_urls(
            'physics', [2024], 100, True, False
        )[:num_tests]
        
        api_times = []
        paper_counts = []
        
        for i, (category, time_range, url) in enumerate(test_urls):
            print(f"测试 {i+1}/{num_tests}: {category} - {time_range}")
            
            paper_ids, request_time = self.time_function(
                get_arxiv_ids_from_url, url, 1
            )
            
            api_times.append(request_time)
            paper_counts.append(len(paper_ids) if paper_ids else 0)
            
            print(f"  请求时间: {request_time:.2f}秒")
            print(f"  获得论文: {len(paper_ids) if paper_ids else 0}篇")
            
            # 遵守API限制
            time.sleep(3.5)
        
        self.results['api_request'] = {
            'avg_time': statistics.mean(api_times),
            'min_time': min(api_times),
            'max_time': max(api_times),
            'avg_papers_per_request': statistics.mean(paper_counts),
            'total_papers': sum(paper_counts),
            'requests_per_minute': 60 / (statistics.mean(api_times) + 3.5)
        }
        
        print(f"\nAPI请求性能总结:")
        print(f"  平均请求时间: {self.results['api_request']['avg_time']:.2f}秒")
        print(f"  平均每请求论文数: {self.results['api_request']['avg_papers_per_request']:.1f}篇")
        print(f"  理论每分钟请求数: {self.results['api_request']['requests_per_minute']:.1f}次")
        
    def test_download_speed(self, num_tests=3):
        """测试论文下载速度"""
        print("\n=== 测试论文下载效率 ===")
        
        # 获取一些测试论文ID
        test_url = build_single_category_urls('physics', [2024], 50, False, False)[0][2]
        paper_ids = get_arxiv_ids_from_url(test_url, 1)
        
        if not paper_ids or len(paper_ids) < num_tests:
            print("无法获取足够的测试论文ID")
            return
        
        test_ids = paper_ids[:num_tests]
        download_times = []
        file_sizes = []
        
        for i, paper_id in enumerate(test_ids):
            print(f"测试下载 {i+1}/{num_tests}: {paper_id}")
            
            # 测试下载时间
            file_path, download_time = self.time_function(
                download_paper, paper_id, './temp_test/'
            )
            
            download_times.append(download_time)
            
            # 获取文件大小
            if file_path and file_path != 0:
                try:
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    file_sizes.append(file_size)
                    print(f"  下载时间: {download_time:.2f}秒")
                    print(f"  文件大小: {file_size:.1f}KB")
                    
                    # 清理测试文件
                    os.remove(file_path)
                except:
                    file_sizes.append(0)
            else:
                file_sizes.append(0)
                print(f"  下载失败")
            
            time.sleep(3.5)  # API限制
        
        # 清理测试目录
        try:
            os.rmdir('./temp_test/')
        except:
            pass
        
        valid_downloads = [t for t in download_times if t > 0]
        valid_sizes = [s for s in file_sizes if s > 0]
        
        if valid_downloads:
            self.results['download'] = {
                'avg_time': statistics.mean(valid_downloads),
                'min_time': min(valid_downloads),
                'max_time': max(valid_downloads),
                'avg_size_kb': statistics.mean(valid_sizes) if valid_sizes else 0,
                'success_rate': len(valid_downloads) / len(download_times),
                'avg_speed_kbps': statistics.mean(valid_sizes) / statistics.mean(valid_downloads) if valid_sizes and valid_downloads else 0
            }
            
            print(f"\n下载性能总结:")
            print(f"  平均下载时间: {self.results['download']['avg_time']:.2f}秒")
            print(f"  平均文件大小: {self.results['download']['avg_size_kb']:.1f}KB")
            print(f"  下载成功率: {self.results['download']['success_rate']*100:.1f}%")
            print(f"  平均下载速度: {self.results['download']['avg_speed_kbps']:.1f}KB/s")
    
    def test_formula_extraction_speed(self, num_tests=3):
        """测试公式提取速度"""
        print("\n=== 测试公式提取效率 ===")
        
        # 获取测试论文
        test_url = build_single_category_urls('math', [2024], 50, False, False)[0][2]
        paper_ids = get_arxiv_ids_from_url(test_url, 1)
        
        if not paper_ids or len(paper_ids) < num_tests:
            print("无法获取足够的测试论文ID")
            return
        
        test_ids = paper_ids[:num_tests]
        extraction_times = []
        formula_counts = []
        
        for i, paper_id in enumerate(test_ids):
            print(f"测试提取 {i+1}/{num_tests}: {paper_id}")
            
            # 测试公式提取时间
            try:
                result, extraction_time = self.time_function(
                    parse_arxiv, paper_id, False, False
                )

                if isinstance(result, tuple) and len(result) >= 2:
                    formulas, _ = result
                elif isinstance(result, list):
                    formulas = result
                else:
                    formulas = []
            except Exception as e:
                print(f"  提取失败: {e}")
                formulas = []
                extraction_time = 0
            
            extraction_times.append(extraction_time)
            formula_counts.append(len(formulas))
            
            print(f"  提取时间: {extraction_time:.2f}秒")
            print(f"  公式数量: {len(formulas)}个")
            
            time.sleep(3.5)  # API限制
        
        if extraction_times:
            self.results['extraction'] = {
                'avg_time': statistics.mean(extraction_times),
                'min_time': min(extraction_times),
                'max_time': max(extraction_times),
                'avg_formulas': statistics.mean(formula_counts),
                'formulas_per_second': sum(formula_counts) / sum(extraction_times) if sum(extraction_times) > 0 else 0
            }
            
            print(f"\n公式提取性能总结:")
            print(f"  平均提取时间: {self.results['extraction']['avg_time']:.2f}秒")
            print(f"  平均公式数量: {self.results['extraction']['avg_formulas']:.1f}个")
            print(f"  提取速度: {self.results['extraction']['formulas_per_second']:.1f}公式/秒")
    
    def analyze_bottlenecks(self):
        """分析性能瓶颈"""
        print("\n=== 性能瓶颈分析 ===")
        
        if not self.results:
            print("没有测试数据")
            return
        
        # 计算单篇论文的总处理时间
        total_time_per_paper = 0
        bottlenecks = []
        
        if 'api_request' in self.results:
            api_time_per_paper = self.results['api_request']['avg_time'] / max(1, self.results['api_request']['avg_papers_per_request'])
            total_time_per_paper += api_time_per_paper
            bottlenecks.append(('API请求', api_time_per_paper))
        
        if 'download' in self.results:
            download_time = self.results['download']['avg_time']
            total_time_per_paper += download_time
            bottlenecks.append(('论文下载', download_time))
        
        if 'extraction' in self.results:
            extraction_time = self.results['extraction']['avg_time']
            total_time_per_paper += extraction_time
            bottlenecks.append(('公式提取', extraction_time))
        
        # 添加强制延迟
        api_delay = 3.5
        total_time_per_paper += api_delay
        bottlenecks.append(('API速率限制', api_delay))
        
        # 排序找出最大瓶颈
        bottlenecks.sort(key=lambda x: x[1], reverse=True)
        
        print(f"单篇论文总处理时间: {total_time_per_paper:.2f}秒")
        print(f"理论每小时处理论文数: {3600/total_time_per_paper:.0f}篇")
        print(f"10000篇论文预计时间: {total_time_per_paper*10000/3600:.1f}小时")
        
        print(f"\n性能瓶颈排序:")
        for i, (name, time_cost) in enumerate(bottlenecks):
            percentage = (time_cost / total_time_per_paper) * 100
            print(f"  {i+1}. {name}: {time_cost:.2f}秒 ({percentage:.1f}%)")
        
        return bottlenecks, total_time_per_paper
    
    def suggest_optimizations(self, bottlenecks, total_time):
        """提出优化建议"""
        print("\n=== 优化建议 ===")
        
        suggestions = []
        
        # 分析主要瓶颈
        if bottlenecks:
            main_bottleneck = bottlenecks[0]
            
            if main_bottleneck[0] == 'API速率限制':
                suggestions.append({
                    'issue': 'API速率限制是最大瓶颈',
                    'solutions': [
                        '并行处理：同时处理多个已下载的论文',
                        '批量下载：先批量下载论文，再批量处理',
                        '多账户：使用多个API密钥（如果允许）',
                        '本地缓存：避免重复下载相同论文'
                    ],
                    'feasibility': '高',
                    'impact': '中等'
                })
            
            if any(name == '论文下载' for name, _ in bottlenecks[:2]):
                suggestions.append({
                    'issue': '论文下载速度慢',
                    'solutions': [
                        '并行下载：使用多线程下载',
                        '连接池：复用HTTP连接',
                        '压缩：启用gzip压缩',
                        'CDN：使用镜像站点'
                    ],
                    'feasibility': '中等',
                    'impact': '高'
                })
            
            if any(name == '公式提取' for name, _ in bottlenecks[:2]):
                suggestions.append({
                    'issue': '公式提取处理慢',
                    'solutions': [
                        '并行处理：多进程处理公式提取',
                        '算法优化：改进正则表达式',
                        '缓存：缓存已处理的文件',
                        '预处理：批量预处理LaTeX文件'
                    ],
                    'feasibility': '高',
                    'impact': '中等'
                })
        
        # 通用优化建议
        suggestions.append({
            'issue': '整体系统优化',
            'solutions': [
                '流水线处理：下载和处理并行进行',
                '内存优化：减少内存占用',
                '磁盘IO优化：使用SSD，批量写入',
                '监控和重试：改进错误处理'
            ],
            'feasibility': '中等',
            'impact': '高'
        })
        
        for i, suggestion in enumerate(suggestions):
            print(f"\n{i+1}. {suggestion['issue']}")
            print(f"   可行性: {suggestion['feasibility']}")
            print(f"   影响: {suggestion['impact']}")
            print("   解决方案:")
            for solution in suggestion['solutions']:
                print(f"     - {solution}")
        
        return suggestions
    
    def save_results(self, filename='performance_results.json'):
        """保存测试结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        print(f"\n测试结果已保存到: {filename}")

def main():
    """主测试函数"""
    print("ArXiv下载系统性能测试")
    print("=" * 50)
    
    profiler = PerformanceProfiler()
    
    try:
        # 创建临时目录
        os.makedirs('./temp_test/', exist_ok=True)
        
        # 执行各项测试
        profiler.test_api_request_speed(3)
        profiler.test_download_speed(3)
        profiler.test_formula_extraction_speed(3)
        
        # 分析结果
        bottlenecks, total_time = profiler.analyze_bottlenecks()
        suggestions = profiler.suggest_optimizations(bottlenecks, total_time)
        
        # 保存结果
        profiler.save_results()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
    finally:
        # 清理
        try:
            os.rmdir('./temp_test/')
        except:
            pass

if __name__ == '__main__':
    main()
