LaTeX2PNG 渲染错误详细日志
==================================================

处理时间: 2025-07-29 10:58:58
总错误数: 539
错误率: 11.7%

错误 1:
公式 11: \tilde{\bm{u}}(t) = \Phi_{0 \to t}(\tilde{\bm{u}}(0)) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp43nng7gz 9bfc2916b8311f9492a0a2e356e34a2e2bfd98d21f8161ad66d09a5c1a5587dd.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 2:
公式 21: \| \bm{a} \|_{\mathcal{L}}^2 = \bm{a}^\dagger \mathcal{L}^{-1} \bm{a} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprjpn8isw 0cc2ec47f0ab78b2857cebe46506a7d0d963d14b9d1b09d7a02499c1661c3de0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 3:
公式 30: \hat{\bm{U}}^\top \hat{\bm{\Sigma}} \hat{\bm{U}} \ge 0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvli4g61t 7488369c6be2191513a06a97ff661267351d23ab50be91ac1ea6222e91f02d9d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 4:
公式 34: \bm{H} \in \mathbb{R}^{2M \times 2N} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzqzhi6a6 652d57d79d77e15ac5f8838b3a852b8b31d875b9628f45c5476f03d4ba2e06ae.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 5:
公式 40: T_n(\bm{u}(t), t) = \frac{(t_{k+1} - t) z_n(t_k) + (t - t_k) z_n(t_{k+1})}{t_{k+1} - t_k}, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjupyejp6 a99720667746a638c33d050777cc064ae694eaa6ad5e1c69800f0bab46518f2c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 6:
公式 42: \hat{\bm{\Sigma}} = \begin{bmatrix}\Re(\hat{\bm{\Sigma}}_{\mathbb{C}} + \hat{\bm{\Sigma}}_p) & -\Im(\hat{\bm{\Sigma}}_{\mathbb{C}} - \hat{\bm{\Sigma}}_p) \\\Im(\hat{\bm{\Sigma}}_{\mathbb{C}} + \hat{\bm{\Sigma}}_p) & \ \ \Re(\hat{\bm{\Sigma}}_{\mathbb{C}} - \hat{\bm{\Sigma}}_p)\end{bmatrix}\ , - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpviq4x4vk 40d5a6165531242a3a711f065057c6f04b28fa7bfa76f8ab783ef8ae1359f2c3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 7:
公式 37: \bm{Z}(t_{\text{obs}}) \in \mathbb{R}^{2M} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzavprdly 99fe0a4f7f4ae28989e1680c883955e8d9a5142dcdcee84b8bccb0a44a448b58.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 8:
公式 55: \tilde{\bm{u}}^{(i,j)}(t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd28j04hv 5d7f20ec16a01253351d0d06b85867bc7cb829b3a07f18e0e7eee7fded33de25.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 9:
公式 59: \hat{\bm{\Sigma}}(t_{\text{obs}}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpt0h06vmy 264b227ab0e45eb0346505ba540d2a29e228825928d66770fcfd9d356fb2eba5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 10:
公式 65: \langle (\bm{U}_n - \tilde{\bm{U}}_n)^2 \rangle_{T, L}=0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9rsrn1wx ab3d24bc583019ac1316f239618b102cba8633a27ffa76ad1a2f2bd96e5aebfc.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 11:
公式 70: \bm{U}(t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8fv1vcgk b4c27c23e15999ffa66873c612eee6525089b89d6f1c2239945f755668dbadc6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 12:
公式 73: \bm{u} = \{u_0, u_1, \ldots, u_{N-1}\} \in \mathbb{C}^N - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmplw1oq6ri 46d5ec4d8ad38b91e3fc4b1e7bbb2b39ab7190096ddd42789028460ab150e25b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 13:
公式 82: \bm{v}(t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps79i2wrt 53a15900d1186c2eaa189e7134b59c20d19455bcfd8f42cc3611cbecf9e4ea7e.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 14:
公式 84: \tilde{\bm{\Sigma}}(t_{\text{obs}}) = (\bm{I} - \bm{K}\bm{H}) \hat{\bm{\Sigma}}(t_{\text{obs}}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0knit7g5 9f3243b2e705ae137520f5bea1443840166b26e345aa5212615aa78af90e00f5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 15:
公式 95: \tilde{\bm{u}}^{(i)}(0) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsafc242o 5bfecd293a1c1c1f1930984ebc741a42bae6ca62d97578521a604b6355f3f4d7.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 16:
公式 85: \bm{U}(t_{\text{obs}}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk2r58ct_ 8fa25412a4a589ae89ea1303b7b47297b9018226a67e0dadbcf02ac8c155b99a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 17:
公式 97: \hat{\bm{\mu}} = \frac{1}{L} \sum_{j=1}^L \hat{\bm{U}}^{(j)} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpccylts28 62235777038f1d38d1eede6791985da751e8871293fcf8c5a9d006d72584349a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 18:
公式 110: \bm{y} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbzgz6769 2521fb8f8b911f7518fbcdb9a91e2e2b0cc49152364a0f3ab94b6dbd964fcc58.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 19:
公式 124: \tilde{\bm{\Sigma}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpp1vdhv_g 1f112b883d5e37a19bc6b2993032d6a24b0398052c8b4bb3f84c3c34be8771c5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 20:
公式 130: \begin{eqnarray}J^{(i)} &=& \left\| \hat{\bm{u}}(0) - \tilde{\bm{u}}^{(i)}(0) \right\|_{\mathcal{B}}^2  \\&+& \sum_{t=0}^{T_a} \left\| \bm{z}(t) - \bm{H}_{\mathbb{C}} \tilde{\bm{u}}^{(i)}(t) - \bm{v}(t) \right\|_{\bm{R}_\mathbb{C}}^2\,,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6jxyow87 33e52426ec399ed1dab8a5036aac8ddaacf7c9bcd2f70d3b46ba92406de776d2.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 21:
公式 123: \left\| \tilde{\bm{u}}^{(i-1,j)}(0) - \tilde{\bm{u}}^{(i,j)}(0) \right\|_{\mathcal{P}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpoewvs647 cf12e1555c89d3934a45bb4b0dc88a0be8e1b06c8a5ee84c3a6f32912d478eca.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 22:
公式 127: \bm{I} = \{e^{-\nu dt k_0^2/2}, e^{-\nu dt k_1^2/2}, \ldots, e^{-\nu dt k_{N-1}^2/2}\} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4my70xaz 280fbf67773f26e886ea6cc86c3e2f3ca4b913dea2ee75422820dd123a196899.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 23:
公式 131: \tilde{\bm{u}}^{(i)}(t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm4yb5hdf c468d26aac447152b1445ff0840474aa1248ec32eb70802ff736a5a30cc09fd8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 24:
公式 136: \tilde{\bm{u}}^{(i-1,j)} \approx \tilde{\bm{u}}^{(i,j)} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp__9449gw 83e3fd911d19e18347496beee9f5958375b255b2cd71d2491cb724c5df3b1300.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 25:
公式 143: \left(\frac{{\rm d}}{{\rm d}t}+\nu k_n^2\right)u_n(t) = G_n[\bm{u}] + f_n(t)\ , - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp44y3o1r_ 95ef5b07a5af8bd0fafdd3a9d98211588f61fdae95be96a0fbf1e3f144931bf3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 26:
公式 148: \left(\frac{d}{dt} + \nu k_n^2\right)\tilde{u}_n(t) = G_n[\bm{u}] + f_n + \alpha_n \delta_{nm} \left[T_n(\bm{u},t) - \tilde{u}_n(t)\right]\,, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5k1166u_ 2f779685d0058d1839f3dd28f3bf48cc6ddb9c6a70790dfe8eb101cdef002f5b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 27:
公式 154: \bm{\xi}(t_{\text{obs}}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo3itzj9m 7c20f4f5e4584b6b3ff398b7580ead1e6765a3eb532e249687ed87ab0d0de0ff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 28:
公式 156: \hat{\bm{\Sigma}}_{\mathbb{C}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqlhi1kb_ 2688fc01438d9dec5f93432a106d97b2dc85620141db0d587b6d0e7fd21664d6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 29:
公式 164: \partial\,\mathrm{tr}(\tilde{\bm{\Sigma}})/\partial \bm{K} = 0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyo9biz4n f793cac4301989dba5cf3416828eb49b837dc7e14046bd9c7fbb33a1a34b7976.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 30:
公式 166: \bm{I} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd8473y7o 28c37c711b4a8fa42bb069bfd3e1042b21618f956475035b1888c2ef4a2cdc47.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 31:
公式 170: \bm{Z}(t_{\text{obs}}) = \bm{H}\bm{U}(t_{\text{obs}}) + \bm{\xi}(t_{\text{obs}})\,,    \quad \bm{\xi}(t_{\text{obs}}) \sim \mathcal{N}(\bm{0}, \bm{R})\,. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp3goszbjv 99ae2eacc98d5debd35875535335f71711f4121c7bd2c3680ced128a98adb54b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 32:
公式 189: \bm{R} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6qabbs3r 018b6a9b433cf7c45b51a0918fe1dcbf753ab9516ec98383e190eddc6b1ed623.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 33:
公式 207: \hat{\bm{\Sigma}} = \langle(\hat{\bm{U}} - \langle \hat{\bm{U}} \rangle_L)(\hat{\bm{U}} - \langle \hat{\bm{U}} \rangle_L)^{T}\rangle_L - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmph2v3b1c4 cd3b95ad8dd99b6401c6c25ca4298ae42251181e976ab941d7a116376ff19420.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 34:
公式 200: \hat{u}_n^{(j)}(t_0)=\begin{cases}\sqrt{\langle |u_n|^2 \rangle_T} \left[ \cos(\phi_n^{(j)}) + i \sin(\phi_n^{(j)}) \right] & n \neq m \\u_n(t_0) + \epsilon_n^{(j)} & n = m\\end{cases}\ , - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwwms8876 6842f0bb2a19fa9e3f04878fdd1274af5a66099c8adf6360b20799db3d288e0f.tex; ==> First Aid for underscore.sty applied!; ! Missing \cr inserted.
--------------------------------------------------------------------------------

错误 35:
公式 213: \begin{aligned}J^{(i,j)}_{n_{\text{seq}}} =&\left\| \hat{\bm{u}}^{(j)}(n_{\text{seq}}T_a) - \tilde{\bm{u}}^{(i,j)}(n_{\text{seq}}T_a) \right\|_{\hat{\bm{\Sigma}}_{\mathbb{C}}} \\+\sum_{t = n_{\text{seq}}T_a}^{(n_{\text{seq}}+1)T_a}&\left\| \bm{z}(t) - \bm{H}_c \tilde{\bm{u}}^{(i,j)}(t) - \bm{v}^{(j)}(t) \right\|_{\bm{R}_\mathbb{C}}\,,\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpiwt3dewp 38c3cb14cfbfdc06026b142157b554d2a1ecffda007965df2d8b9e7a91909b38.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 36:
公式 216: \tilde{\bm{u}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpunax6_zy 08fee3e803392026d4782e7d0b9e52eb71940eb68e37ef749506ba157f46a207.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 37:
公式 219: \hat{\bm{u}}(0) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpx05wmeiy fd3f3a2b0315ceaec6fa600058f9c72fb879c4abf68d670e589e728d9b20b90b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 38:
公式 224: \bm{V}^{(j)}(t_{\text{obs}}) \sim \mathcal{N}(\bm{0}, \bm{R}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprihextw5 d16885e8f9494de3f21a4dd473c497a9e36ab3a1e0b9234d9469414235ba881b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 39:
公式 238: \hat{\bm{u}}^{(j)} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsmuc60zu 50a855047d6b565bc34f0cb203d5a68593a21ca9553619dbe2625d913dedd811.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 40:
公式 236: \bm{K}(t_{\text{obs}}) \in \mathbb{R}^{2N \times 2m} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_ik8d__a f5550ceeb8d69fae93eea7f3eea202ced2c526c00c147d5fb039877890da6ea2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 41:
公式 243: (\tilde{\bm{u}}(0)) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpc3bx48fn fe4c2e71f5df6c09213216b71f54ed367f35581ea169f43e83774674978aae13.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 42:
公式 250: G_n[\bm{u}] - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp527ddca0 812cfcc9a06e3e8f9cf21e262e85efedca74393f80969d35c463cd49f0cfbc10.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 43:
公式 254: \{\hat{\bm{U}}^{(1)}, \hat{\bm{U}}^{(2)}, \dots, \hat{\bm{U}}^{(L)}\} \subset \mathbb{R}^{2N} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxwhycu99 eb2d084fd9bbbe9602a88b94a3819b19962ccf6d396a9582a74761bcfdc6a3fb.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 44:
公式 277: \hat{\bm{\Sigma}}_{\mathbb{C}} = \langle(\hat{\bm{u}} - \langle \hat{\bm{u}} \rangle_L)(\hat{\bm{u}} - \langle \hat{\bm{u}} \rangle_L)^\dagger\rangle_L - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptn941ca9 84d3c6e821c378b6d5049a79309861b2604997d53e699f0f9177f36a1b43dd37.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 45:
公式 280: \hat{\bm{U}} = (\Re(\hat{\bm{u}}),\,\Im(\hat{\bm{u}}))\in\mathbb{R}^{2N} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp86ss77w8 be76c74c9ae8da9b722678e8532c66697cd2a1c9ba296d358e3a691a5d96db6d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 46:
公式 289: \begin{eqnarray}    G_n[u] &=& i(ak_{n+1}u_{n+1}^*u_{n+2} + bk_n u_{n-1}^*u_{n+1}  \\    &-& ck_{n-1}u_{n-1}u_{n-2})\,. \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbywcxxrd 07e9b9d6b10307623db0d1293bde136da403c4b0c235eeffc99b81f14e7d8082.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 47:
公式 287: \begin{cases}A_{1n} = dt(f_n + G_n[\bm{u}]) \\A_{2n} = dt(f_n + G_n[\bm{I} \circ (\bm{u} + \frac{\bm{A}_1}{2})]) \\A_{3n} = dt(f_n + G_n[\bm{I} \circ \bm{u} + \frac{\bm{A}_2}{2}]) \\A_{4n} = dt(f_n + G_n[\bm{I} \circ \bm{I} \circ \bm{u} + \bm{I} \circ \bm{A}_3])\end{cases} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpoivg5z0j 7ca93ff7d7f66e65e120426c2a42abfb82da4f2e99c0bd8fdaed843dc2f7a468.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 48:
公式 298: \hat{\bm{u}}^{(j)}(n_{seq}T_{a}) = \tilde{\bm{u}}^{(j)}(n_{seq}T_{a}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_b88uy2l a8d18eac096bb08f337cb1f38586098ddf27cb0448319edd4d7f48fee0d3302f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 49:
公式 299: \bm{z} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpmp1akciv 379585d2b001b1332b7f7d073ea2f6b3b3dc9adedb11709e443646b063aa19bb.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 50:
公式 314: \hat{\bm{u}}^{(j)}(t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_rs25z7y 7afcef6136bb240bc6fbc0757780118b7b51dc654c239d347f40681ae78a6ead.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 51:
公式 325: \bm{H}_{\mathbb{C}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuphmpn_k f34f801e775da81dfd8dc11976ce680a3a5e50477f4249a0e917fea9192a13c2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 52:
公式 324: \bm{V}^{(j)} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzc2b4idh 61f8df3acdfb5fbd3ee6d4d37c40c37175238a34565692d94ee84bb03f79ac74.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 53:
公式 328: \bm{R}_\mathbb{C} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm43nv3e5 27084465665a61b6706245fc00aab1d956c5b216c412982bf0dd6194531d744c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 54:
公式 329: \hat{\bm{\Sigma}}(t_{\text{obs}}) = \frac{1}{L-1} \sum_{j=1}^L \left[\hat{\bm{U}}^{(j)} - \hat{\bm{\mu}}\right] \left[\hat{\bm{U}}^{(j)} - \hat{\bm{\mu}}\right]^{T}\ , - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp348437nb 596bc60ff4edaba235ee24105aeebc37cf7400fea219b0d4c9be2feb1fc947f5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 55:
公式 334: K_{nm}\left[\bm{Z} - \bm{H}\hat{\bm{U}}^{(j)} - \bm{V}^{(j)}\right]_m - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptldia6yg 9689950f51a74be7cc7d4b764e578e0fb284104adf4933c8abad797362e7caed.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 56:
公式 339: \begin{eqnarray}        \Pi_n = \Im\left[a\, k_{n+1} u^*_n u^*_{n+1} u_{n+2} - c\, k_n u^*_{n-1} u^*_n u_{n+1}\right]\,.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6ycpmkhn 0c747992af7b6cc75295dd492a1140ee293fa2af58eaad74c807af9a554d395e.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 57:
公式 343: I_n\frac{dy_n}{dt} + y_n\frac{dI_n}{dt} + \nu k_n^2 I_n y_n = G_n[\bm{I} \circ \bm{y}] + f_n(t)\ . - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp76197eb4 a7d5e168f7c6ab5fbb83036d07d24dd26b6312dd2160a19e94caa62c827182d5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 58:
公式 350: \bm{H} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu1n_fn54 0073612a2f86a6a02270db39ad65fa79ccdada06df17ed5877630f800f02b920.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 59:
公式 353: \{\hat{\bm{u}}^{(1)}, \hat{\bm{u}}^{(2)}, \ldots, \hat{\bm{u}}^{(L)}\} \in \mathbb{C}^{N \times L} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfc_ukko5 61c4aca16e7ef672427a1c1cb5ae915d71a5fa7f84672b229aa9682d33abf912.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 60:
公式 363: \bm{u} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd0iz0ylq 5f59e7e480f3003906051230f0cc9d5649cb173520d3491841488daacb8bb5b6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 61:
公式 364: T_n(\bm{u}, t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpa08ht0bx 099fada1e0759a62f1c49f495ec6113e6fd67ee04387c834127ddd13514b3d86.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 62:
公式 369: \bm{K}(t_{\text{obs}}) = \hat{\bm{\Sigma}}(t_{\text{obs}})\,\bm{H}^{T} \left(\bm{H} \hat{\bm{\Sigma}}(t_{\text{obs}}) \bm{H}^{T} + \bm{R}\right)^{-1}\ , - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpoxmegz3w 9ecc9d060f45fda1ef9a956af787ca13b53ba72f8d398a066381637d4f8c541a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 63:
公式 371: \hat{\bm{\Sigma}}_p = \langle(\hat{\bm{u}} - \langle \hat{\bm{u}} \rangle_L)(\hat{\bm{u}} - \langle \hat{\bm{u}} \rangle_L)^{T}\rangle_L - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcg8lghuo ad7c70d7a8a080269fce4785ad214070aa0993c8309e844b7f661b54dba976c6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 64:
公式 384: \begin{eqnarray}\tilde{\bm{U}}^{(j)}(t_{\text{obs}}) &=& \hat{\bm{U}}^{(j)}(t_{\text{obs}})  \\&&\hspace{-5em} +\!\bm{K}(t_{\text{obs}})\! \left[ \bm{Z}(t_{\text{obs}})\! -\!\bm{H} \hat{\bm{U}}^{(j)}(t_{\text{obs}}) \!-\! \bm{V}^{(j)}(t_{\text{obs}}) \right]\,,\end{eqnarray} - 错误: LaTeX编译错误: b'$\\\\begin{eqnarray}\\\\tilde{\\\\bm{U}}^{(j)}(t_{\\\\text{obs}}) &=& \\\\hat{\\\\bm{U}}^{(j)}(t_{\\\\text{obs}})  \\\\\\\\&&\\\\hspace{-5em} +\\\\!\\\\bm{K}(t_{\\\\text{obs}})\\\\! \\\\left[ \\\\bm{Z}(t_{\\\\text{obs}})\\\\! -\\\\!\\\\bm{H} \\\\hat{\\\\bm{U}}^{(j)}(t_{\\\\text{obs}}) \\\\!-\\\\! \\\\bm{V}^{(j)}(t_{\\\\text{obs}}) \\\\right]\\\\,,\\\\end{eqnarray}$'; latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqrsdv5p5 f78d29edfedd97d765c328f2811d9a5ded2f281f534438bccf523bb6885dfb78.tex; ==> First Aid for underscore.sty applied!
--------------------------------------------------------------------------------

错误 65:
公式 387: \begin{align}   & \tau'-\tau'_0 = \tau - \tau_0 + \frac{[v(E)-v_0]\theta_{\text{prop}}}{v_0},  \\    &E'-E_0' = E - E_0, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo5yjlpn7 ace4b26dd8796f7e0b81b9de09ed347f787deed41b975b46deabd248d2d7aa8e.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 66:
公式 399: \begin{split}    \tilde{\mathcal{E}}(\omega)\propto\\ \exp\left[-\frac{(\omega - \omega_0)^2}{2\sigma^2_\omega} - i\frac{\varphi''(\omega - \omega_0)^2}{2}-i\frac{\varphi'''(\omega-\omega_0)^3}{6}\right]\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkdb6wl9o fbe5fd0ab6ad379e4d6147f5eb191c1d78aa6ebd61dc235a919eb4ab6d5090aa.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 67:
公式 435: \begin{split}    U(z,t) =\\ \exp{\left\{-i2 \text{Im}\int_0^\infty \mathrm{d}\omega\, g(\omega)\sqrt{I(\omega)}    e^ {i \varphi(\omega) }e^{-i\omega(t-z/v_0)} \right\}},\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppkdeccdf 52bb02219aa983534d79238c4946b743b06740b88563eb64640b33eaca2eb5a6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 68:
公式 488: \begin{split}    \tilde{\mathcal{E}}(\omega)\propto \sum_{k=-1}^{k=1} \sum_{n=-\infty}^{\infty} (1/2)^{|k|}i^nJ_n \left(\frac{m\omega_0}{\Omega}\right)\times \\  \exp\left[-\frac{(\omega - \omega_{0,kn})^2}{2\sigma^2_\omega} - i\frac{\varphi''(\omega - \omega_{0,kn})^2}{2}\right] ,\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpho8iukab c9d4624eb5db3cee9bc10ff3a5f6cd925c0a13c90448ea9ff51b007c9aed01cb.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 69:
公式 513: \begin{split}    \tilde{\mathcal{E}}(\omega)\propto\sum_{k=-1}^{k=1} \sum_{n=-\infty}^{\infty} (1/2)^{|k|}i^nJ_n \left(\frac{m\omega_0}{\Omega}\right)\times \exp\left[-\frac{(\omega - \omega_{0,kn})^2}{2\sigma^2_\omega} - i\frac{\varphi''(\omega - \omega_{0,kn})^2}{2}\right]\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdzf9ezz9 042e73deedd535148737acafc2cd510b5029b2f69643f3fb1f960c828b78d39d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 70:
公式 661: \begin{align}    \tau_i(t^n) &= \frac{\mathrm{d} Q_i^u}{\mathrm{d} t} \Big/ \int_\Omega \bar{P} \mathbf{V}_i \cdot R_i \mathbf{O}_i \mathrm{d} \boldsymbol{x}, \\    \frac{\mathrm{d} Q_i^u}{\mathrm{d} t} &= \frac{1}{\Delta t} \left(Q^\text{ref}_i(t^n) - Q_i(\mathbf{v}^{n^*})\right). \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8c1v9zdo 669617771a2f105194af0872c07b92065c963e06ea031c1bb919f0ca70c2df21.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 71:
公式 646: \begin{align}    E &=  \frac{1}{2} \int_\Omega {\mathbf{u} \cdot \mathbf{u}} \, \mathrm{d} \boldsymbol{x} ,\\    &\approx \frac{1}{2} \int_\Omega \frac{1}{(N_x N_y N_z)^2}     \sum_{\mathbf{k}} \sum_{\mathbf{l}}    \exp \left[2 \pi i \left(\frac{x_1(k_1+l_1)}{L_xN_x}+\frac{x_2(k_2+l_2)}{L_yN_y}+\frac{x_3(k_3+l_3)}{L_zN_z}\right)\right] \hat{\mathbf{u}}_\mathbf{k}\cdot     \hat{\mathbf{u}}_\mathbf{l} \, \mathrm{d} \boldsymbol{x} ,\\    & = \frac{1}{2}  \frac{\lvert \Omega \rvert}{(N_x N_y N_z)^2} \sum_{\mathbf{k}} \hat{\mathbf{u}}_\mathbf{k}\cdot     \hat{\mathbf{u}}_\mathbf{-k},\\    & = \frac{1}{2}  \frac{\lvert \Omega \rvert}{(N_x N_y N_z)^2} \sum_{\mathbf{k}} \hat{\mathbf{u}}_\mathbf{k}\cdot     \text{conj}(\hat{\mathbf{u}}_\mathbf{k}),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpymedodqs 6fc3585817ea28e50221aa71e7b36ccc5d7ec1aff2354de99a324329205432b9.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 72:
公式 655: \begin{align}    R_{[l,m]} &= \mathcal{F}^{-1} \hat{R}_{[l,m]} \mathcal{F}, \\    \hat{R}_{[l, m]} \,\hat{\mathbf{u}}_{\bf k} &=    \begin{cases}        \hat{\mathbf{u}}_{\mathbf{k}} & \mathrm{if} \quad l-\frac{1}{2} \leq \lVert{\bf k}\rVert_2 < m + \frac{1}{2} \\        0 & \mathrm{otherwise}    \end{cases}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0r7tl0od 956245eecdc6fa5da638d5d48ebf4778c99948643440c1ee85797215cec87e0d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 73:
公式 644: \begin{align}    \frac{\mathrm{d} Z_{[l,m]}}{\mathrm{d} t} &= C \int_\Omega \sum_{\alpha=1}^3 \frac{\partial (R_{[l,m]} {\omega}_\alpha  R_{[l,m]} {\omega}_\alpha)}{\partial R_{[l,m]} {\omega}_\alpha}  \frac{\partial R_{[l,m]} {\omega}_\alpha}{\partial t} \mathrm{d} \boldsymbol{x} \\    &= C \int_\Omega 2 R_{[l,m]} \boldsymbol{\omega} \cdot  \frac{\partial R_{[l,m]} ( \nabla \times  \mathbf{v})}{\partial t} \mathrm{d} \boldsymbol{x} \\     &= C \int_\Omega (2 \nabla \times R_{[l,m]}\boldsymbol{\omega}) \cdot \frac{\partial R_{[l,m]} \mathbf{v}}{\partial t} \mathrm{d} \boldsymbol{x},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2ywowqh9 327a937bdcec2889a6935ddb1a6b66db7e944e32117da8b94c6890909b319be7.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 74:
公式 685: \begin{align}    \frac{\mathrm{d} E_{[l,m]}}{\mathrm{d} t} &= \frac{1}{2}C\int_\Omega \sum_{\alpha=1}^3 \frac{\partial (R_{[l,m]} {v}_\alpha  R_{[l,m]} {v}_\alpha)}{\partial R_{[l,m]} {v}_\alpha}  \frac{\partial R_{[l,m]} {v}_\alpha}{\partial t} \mathrm{d} \boldsymbol{x} \\    &= C\int_\Omega R_{[l,m]} \mathbf{v} \cdot \frac{\partial R_{[l,m]} \mathbf{v}}{\partial t} \mathrm{d} \boldsymbol{x},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpldi249s_ 592f2a4e5220f083c6600dd0bca316c1aa6653a80bfc9abb5c09e2d8fab94a9d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 75:
公式 706: C = \argmin_{X \in \mathbb{R}^{ len(\underline{\mathbf{q}}_h) \times N_Q}} \frac{1}{2} \lVert \mathbf{Q}_h X - \mathbf{Q} \rVert_F^2 + \lambda \lVert X \rVert_F, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5njs3fo4 ee383ee7c9a9225829dcd23ce43c1883f81f076c999939ec210b9cc8a6984e1a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 76:
公式 712: \begin{align}    \mathbf{v}^{n^*} &= S(\mathbf{v}^{n-1}), \\    \mathbf{v}^{n} &= \mathbf{v}^{n^*} + \int_{t^n-1}^{t^{n}} \mathbf{m}(\mathbf{v}^{n^*}) \mathrm{d} t, \\    &= \mathbf{v}^{n^*} + \mathbf{M}(\mathbf{v}^{n^*}),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1nhs7gbm 8e30dd579fa2ab4ec6b312dc2169ac4c8e0ce4e829076c42d1ed5e513ba34326.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 77:
公式 717: \begin{align}    \mathbf{m}(\mathbf{v}, C_w) &= \nabla \cdot (2 \nu_t S_{ij}), \\    \nu_t &=  C_w^2 \Delta^2 \frac{(\mathfrak{S}^d_{ij} \mathfrak{S}^d_{ij})^{3/2}}{(S_{ij} S_{ij})^{5/2}+(\mathfrak{S}^d_{ij} \mathfrak{S}^d_{ij})^{5/4}},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxtct_x0d 1eeb0a4dde917c8ffee74a5eacc5a933b6b72afc80bbc074dba4f1d078a29556.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 78:
公式 726: \begin{align}    \mathbf{m}(\mathbf{v}, C_s) &= \nabla \cdot (2 \nu_t S_{ij}), \\    \nu_t &= C_s^2 \Delta^2 \sqrt{2 S_{ij}S_{ij}}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppbig6zh2 324f01d7fe3e902d25886e21738b10c0ec09da1721e8e69e096f7455c5057b19.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 79:
公式 735: \begin{align}        \frac{d\mathbf{u}}{dt} &= P \mathbf{F}(\mathbf{u}), \\    \mathbf{F}(\mathbf{u})&=-\mathbf{G} (\mathbf{u} \mathbf{u}^T) + \nu \mathbf{D} \mathbf{u} + \mathbf{f},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgalr2u33 f85f03cde8936c75ee84c022807978fff10f3ea53e66d2fd8400e8444e7807d5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 80:
公式 738: \begin{align}        \mathbf{LRS}(\underline{\mathbf{q}}_h) = \underline{\mathbf{q}}_h C + \boldsymbol{\eta}, \quad \boldsymbol{\eta} \sim \mathcal{N}(\boldsymbol{\mu}, \Sigma), \\        \underline{\mathbf{q}}_h = [\mathbf{q}^{n-1}, \dots, \mathbf{q}^{n-h}, \mathbf{q}^{n^*}, \mathbf{q}^{n-1^*}, \dots, \mathbf{q}^{n-h^*}, 1],     \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpql0ml_lf f7f6479149eb4c0e91eb06df8f001f9adf6a3b393aab698e6791e9c02730856e.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 81:
公式 763: \begin{eqnarray} \frac{\bar{m}\lambda'R_3}{d_2}=\frac{\left(\bar{m}+1\right)\lambda R_3}{d_2},   \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6t9zsu02 03c1dacec2d77a13225af7ccc2b89fbebce708c0131f61efa8fff6e778f4a32b.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 82:
公式 775: \begin{eqnarray}&&V'_3(x_3) = Ae^{-\left(\frac{\pi w_2 x_3}{\lambda R_3}\right)^2}\sum_{n=-(N-1)/2}^{(N-1)/2}e^{-\left(\frac{\pi w_0 n d_1}{\lambda R_1}\right)^2}\\&&\cdot \sum_{m=-M/2}^{M/2}e^{-\left(\frac{\pi w_1 m d_2}{\lambda R_2}\right)^2}  e^{-i \frac{2\pi m d_2 }{\lambda R_3}\left(x_3 - \frac{n d_1 R_3}{  R_2}-\frac{ \bar{m} \lambda R_3}{d_2}\right)}. \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpc83w9rn3 dbe0a675cdf3fb5b5271019088b7a4454da7387de0d83a314919ecc19e2f6d36.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 83:
公式 795: \begin{eqnarray}  FSR=\lambda' -\lambda=\frac{\lambda}{\bar{m}}.   \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk2uk_n8z a2e6a2886d04cee06b6c3a6572131c51587d21a90edac4d56d486f8007b6e1ff.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 84:
公式 800: \begin{eqnarray}V_3(x_3) = A b_1\left(\frac{x_3 R_2}{R_3}\right)  * \sum_{m=-M/2}^{M/2} e^{-i \frac{2\pi m d_2 }{\lambda R_3}\left(x_3 - \frac{ \bar{m} \lambda R_3}{d_2}\right)}\\ =A b_1\left(\frac{x_3 R_2}{R_3}\right)   * \frac{\sin\left[ \left(M+1\right)\frac{\pi d_2 }{\lambda R_3}\left(x_3 - \frac{ \bar{m} \lambda R_3}{d_2}\right)\right]}{\sin\left[ \frac{\pi d_2 }{\lambda R_3}\left(x_3 - \frac{ \bar{m} \lambda R_3}{d_2}\right)\right]},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkaaap_ko 0fe8105b8086b732f52f9296ef492049fb90800c8202b21946aa108dc2fca6f6.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 85:
公式 810: \begin{eqnarray}    \tau'_2(x_2)= \sum_{m=-M/2}^{M/2} \delta (x_2 - m d_2) e^{i 2\pi m \Delta L/\lambda} \\    =\sum_{m=-M/2}^{M/2} \delta (x_2 - m d_2)\cdot \tau_2(x_2).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpospcokt2 40d1e49b4e746cbf212114be258b60fa6ee60f15554071ff68cc3becc0862844.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 86:
公式 824: \begin{eqnarray}&&V'_3(x_3) = A  b_1\left(\frac{x_3 R_2}{R_3}\right) \\&&*  \sum_{n=-(N-1)/2}^{(N-1)/2}\sum_{m=-M/2}^{M/2} e^{-i \frac{2\pi m d_2 }{\lambda R_3}\left(x_3 - \frac{n d_1 R_3}{  R_2}-\frac{ \bar{m} \lambda R_3}{d_2}\right)}\\&& =A b_1\left(\frac{x_3 R_2}{R_3}\right) \\&& *  \sum_{n=-(N-1)/2}^{(N-1)/2}\frac{\sin\left[ \left(M+1\right)\frac{\pi d_2 }{\lambda R_3}\left(x_3 - \frac{n d_1 R_3}{  R_2}-\frac{ \bar{m} \lambda R_3}{d_2}\right)\right]}{\sin\left[ \frac{\pi d_2 }{\lambda R_3}\left(x_3 - \frac{n d_1 R_3}{  R_2}-\frac{ \bar{m} \lambda R_3}{d_2}\right)\right]}.\\\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp03urteuf d3dcd99d2f3e4f212f0f46866375022e710daa7f2fabf8225790cbaf899652b7.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 87:
公式 842: \begin{eqnarray}V'_2 (x_2) =A   \sum_{n=-(N-1)/2}^{(N-1)/2}\sum_{m=-M/2}^{M/2}\tilde{b}_0 \left( \frac{n d_1}{\lambda R_1} \right) \\\cdot \tilde{b}_1 \left( \frac{m d_2 }{\lambda R_2} \right) b_2\left(x_2 -m d_2\right) e^{-i 2\pi  x_2 \left(\frac{n d_1}{\lambda R_2} -\frac{\bar{m}}{d_2}\right)}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2roknz9g 4344a3b7dca682be8e59e235ae165a2c931ff9ebc21b63f0422dd773f1c7dc9d.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 88:
公式 829: \begin{eqnarray}&&V'_2 (x_2) =A  \tilde{b}_1 \left( \frac{x_2 }{\lambda R_2} \right) \\&&\cdot \sum_{n=-(N-1)/2}^{(N-1)/2} \sum_{m=-M/2}^{M/2} \delta\left(x_2 -m d_2\right) e^{i 2\pi  x_2 \left(\frac{n d_1}{\lambda R_2} +\frac{\bar{m}}{d_2}\right)} \\\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphcvuzdl4 ce21a3cb3fec0415eb847575092d28d47740c7b65ecbeeac0cf41ee2ab2b9f0c.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 89:
公式 836: \begin{eqnarray}b_0(x_0) = \frac{1}{\sqrt{\pi}w_0} e^{-x_0^2/w_0^2}\\b_1(x_1) = \frac{1}{\sqrt{\pi}w_1} e^{-x_1^2/w_1^2}\\b_2(x_2) = \frac{1}{\sqrt{\pi}w_2} e^{-x_2^2/w_2^2} \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpplsffr2y e88cf416e1128f42ccb5865f160fbde2c85ea84b8f93cf89adeeeed07176ef87.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 90:
公式 848: \begin{eqnarray}&&V'_3(x_3) = Ab_1\left(\frac{x_3 R_2}{R_3}\right) \\&& *  \sum_{n=-(N-1)/2}^{(N-1)/2}\frac{\sin\left[\pi \left(M+1\right)\left(\frac{ x_3d_2 }{\lambda R_3} - \frac{n }{N }-\bar{m}\right)\right]}{\sin\left[\pi \left(\frac{ x_3d_2 }{\lambda R_3} - \frac{n }{N  }-\bar{m}\right)\right]}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyicrn0_6 e78006d44edd68e6b7101a60e519dd4d9c4b7ec45637793bbdbce268fe5b051e.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 91:
公式 859: \begin{eqnarray}&&V'_3(x_3) = A\tilde{b}_2\left(\frac{x_3}{\lambda R_3}\right)\cdot\sum_{n=-(N-1)/2}^{(N-1)/2}\sum_{m=-M/2}^{M/2}\tilde{b}_0 \left( \frac{n d_1}{\lambda R_1} \right)\\&&\cdot \tilde{b}_1 \left( \frac{m d_2 }{\lambda R_2} \right)  e^{-i \frac{2\pi m d_2 }{\lambda R_3}\left(x_3 - \frac{n d_1 R_3}{  R_2}-\frac{ \bar{m} \lambda R_3}{d_2}\right)}. \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg2x4i3n1 008c73677986e6ca56aff32b03662e3bd30fcecd167b3bbd729044dece3e5397.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 92:
公式 881: \SI{1}{\micro\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd_qne3pq 8bb3e727d3cf83bcd87e7fcabf4af48cf6878d2abdf107445cfc2b1082f0d1e3.tex; ==> First Aid for underscore.sty applied!; ! LaTeX Error: Unicode character μ (U+03BC)
--------------------------------------------------------------------------------

错误 93:
公式 887: \SI{0.096}{\steradian} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsmh1x06c 4193d8ed2c38dfbcd1fca7af91be660f4a9b655e94eb1c8e8ed7b81f11256bbb.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 94:
公式 890: 1\,\si{\micro\metre} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbypg8e_c 8bb3e727d3cf83bcd87e7fcabf4af48cf6878d2abdf107445cfc2b1082f0d1e3.tex; ==> First Aid for underscore.sty applied!; ! LaTeX Error: Unicode character μ (U+03BC)
--------------------------------------------------------------------------------

错误 95:
公式 899: 1.5\, \si{\micro\litre} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppkgb3cui 3293a0e5e74900e9d31b4c7e9cb3d91a413a5690bc1d4ad48338b1c4863ac161.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 96:
公式 905: 30\, \si{\micro\litre} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqpualf3m c4d3938688e5790b9fc555002fd6a36817db71c7205e9fd6c72fa1a694ae6355.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 97:
公式 925: \SI {892}{\nano\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp004exshs b18545081b3b424496c1e9fd6beb73611368f8fdeb62c78855c5eb2656886052.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 98:
公式 1145: \begin{eqnarray}\left[\frac{\partial^2}{\partial t^2}+\gamma\frac{\partial}{\partial t}+\omega_0^2\right]\mathbf{P}_\mathrm{dyn}(\mathbf{r},t)=\frac{N(t)e^2}{m_\mathrm{e}}\mathbf{E}(\mathbf{r},t)\,.    \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvikf4k6e e6ce166f472114c0fb4a6d64d905febfbfb05bd90db196d3a97d3e9b9897e394.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 99:
公式 1305: \begin{align}    \partial_t H + \nabla \cdot (H \mathbf{u}) + \frac{\dot{m}}{\rho_c} \delta_\Sigma = 0. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6patm0j_ 82612871ff9efebd7fc067ae099e318ebb73ae879123a32440868001c8049953.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 100:
公式 1312: \begin{align}    \Vert \rho^1 (\mathbf{u^1}-\mathbf{u_\Sigma})\cdot \mathbf{n_\Sigma}  \vert \vert = \vert\vert \dot{m}^1 \vert\vert  =0. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxp1axo8y 8293fbf98d81844b194e2057ecd9c932c774fe2b94d30c031aa80eee9e9d92e7.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 101:
公式 1328: \begin{align}    \mathrm{Sc}=\frac{\nu_c}{D},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpazuv2zis 8f5474c9cfa76d7043948a7028e9395e00afadb97afb0680c567d4191d1b41bd.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 102:
公式 1317: \begin{align}    \rho_c\mathbf{u}=\rho^1 \mathbf{u^1}+\rho^2 \mathbf{u^2},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpddd3n_58 5a9ab67c79bf42161b17c078d118592894d2ec80833eda9576222f1f2c60e093.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 103:
公式 1332: \begin{align}    R_{det}=0.6 \theta \sqrt{\frac{\sigma} {(\rho_c-\rho_d)g}},    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpr50qy15v cd532784080e5801b3d590f98f8252d857ea8a898a0b34641ee27d7e6500c5d4.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 104:
公式 1324: \begin{align}    \frac{\partial c}{\partial z} &= \frac{J}{D}  \qquad \text{for } r < \frac{D_e}{2},    \\    \frac{\partial c}{\partial z} &= 0    \qquad \text{for } r > \frac{D_e}{2}.    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpma29q3r1 689ce9efe0165542625a6e53bc2dfd3ad3b21acb26c2a654e25758f15e58ce08.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 105:
公式 1339: 90 \degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_ri5jct5 395710e9abf0b9108c468136d0825c2bc7274cae7f710f686059a9a8a0fb1622.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 106:
公式 1343: \begin{align}    \mathrm{Bo}=\frac{\rho_cgD_b^2}{\sigma}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvqdcdz0b 210b2b623eb608f89e216ffc521b22bdda820162e6ed3cf2dcbd3a5fed943798.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 107:
公式 1355: \theta=90\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo05uzeqk ca654d8d957cce5d5e25f7822c5e97ba394bb987e2559abaed5f134fe998cb29.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 108:
公式 1364: \begin{align}    \overline{\mathrm{Sh_{e}}}=\frac{L_{ref} \int_{\Sigma} \dot{m}\,ds}{A_{\Sigma} M D(\bar{c_e}-c_{\Sigma})},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5qiw0mic 67132acb47c052c0b764813bce919055f2d865685cc741fcc3332147e0be4d7a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 109:
公式 1365: \begin{align}    (c_c)_\Sigma=\frac{(c_d)_\Sigma}{\mathrm{He}},    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9df0tzi5 eb3cde6625f15215662937dce8544fdc30cc23422a525f9e945ce637bdd09139.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 110:
公式 1368: \begin{align}f_c = \begin{cases} 0 & \text{if the cell is pure gas}, \\1 & \text{if the cell is pure liquid}, \\[0,1] & \text{if the cell is mixed}.\end{cases}\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpke48_mlw d4fb760791ed4dd03405972b5d81375e30feadc0fa4a9d4859f5893918aecd1a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 111:
公式 1372: \begin{align}  \nabla \cdot \mathbf{u} &= 0 \quad \text{in } \Omega \backslash \Sigma,   \\  \begin{split}      \partial_t (\rho \mathbf{u}) + \nabla \cdot (\rho \mathbf{u} \otimes \mathbf{u})       &= -\nabla p + \nabla \cdot (2\mu \mathbf{D}) \\      &\quad + \rho \mathbf{g} \quad \text{in } \Omega \backslash \Sigma.  \end{split} \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpp2f8xulx b0c4b014e644b9a347992ea6a03656d14fd939299e681c75de6f71aa86306852.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 112:
公式 1373: \begin{align}    \mathrm{Ga}=\sqrt{\frac{\rho_cgD_b^2}{\nu_c^2}}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxrfc1mui 5ddfe3489e1263610eaef9c3c05a79f1c0a7c14ac237174bb724984bfe2dc4f6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 113:
公式 1382: \begin{align}    \mathbf{u} \otimes (\mathbf{u}-\mathbf{u_\Sigma}) + p\mathbf{I} -2\mu \mathbf{D} \cdot \mathbf{n_\Sigma} = \sigma k \mathbf{n_\Sigma} + \nabla_\Sigma \sigma. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv8jxgtp8 a4c4e886823b0fbeb8e8677189ce51c7ff67ae6f2ea6ff9658c58c2e6258135b.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 114:
公式 1385: \begin{align}  \begin{split}      \int_V \partial_t c \, dV       &+ \oint_{\partial V} (c \mathbf{u} - D \nabla c) \cdot \mathbf{n} \, dS \\      &+ \int_\Sigma c (\mathbf{u} - \mathbf{u}_\Sigma) \cdot \mathbf{n}_\Sigma \, dS \\      &= 0  \end{split}  \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps1hlmk71 2a15a93bd3373dcb34e5f12634e1eaabf3ba902ce4666df46af11823a2174d25.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 115:
公式 1394: \begin{align}  \rho (\mathbf{u}-\mathbf{u_\Sigma})\cdot \mathbf{n_\Sigma} = 0 ,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmplmg94oyq d668fd2ed8be0fa93c4e28a9e0dad964fc9ee3ff1e95beffb9da2cf788e28d2b.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 116:
公式 1404: \begin{align}    \partial_t c + \mathbf{u}\cdot\nabla c = \nabla\cdot\left(D\nabla c\right) - \frac{\dot{m}}{M}\delta_\Sigma.    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk9_0lykh 6fc4e7bbace81be08bb6b3fb7e09d13ddc8fda5869e193bfe2ea8728883ed731.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 117:
公式 1398: \theta=35 \degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxfn86lmo 734e48036c4c8c323d0858351d97df6473d90543f04580a55797239f5b0b987d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 118:
公式 1412: \begin{align}  \begin{split}      \dot{m}^1       &= \rho^1(\mathbf{u} - \mathbf{u}_\Sigma) \cdot \mathbf{n}_\Sigma       + \rho^1(\mathbf{u}^1 - \mathbf{u}) \cdot \mathbf{n}_\Sigma \\      &= \frac{\rho^1}{\rho_c} \cdot \dot{m} + J^1 \cdot \mathbf{n}_\Sigma  \end{split} \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpj3d11c23 0217c441e0303852a7bfcebb7f61716255aa40539672e23ea781dcf5b629672a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 119:
公式 1418: \begin{align}    J^1=-D^1 \nabla \rho^1, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8704nxfm 5597b1096d9df8ddd49bd2a052fe63eba8e185a1dc861463230c951b7f0f28e2.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 120:
公式 1419: \theta=35\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp38qz6cdz c3d92f6ea7783bf83b9a1b68c66732f5fd66315306a9e582d6d4fcef2912e9e6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 121:
公式 1423: \theta\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpw9iylo83 9b12e53b9e94d7d360174f06fa4284d484adca3e95fd9e8518d7c761ae48f4ed.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 122:
公式 1425: \begin{align}    \overline{\mathrm{Sh_{e}}}=\frac{J L_{ref}}{D(\bar{c_e}-c_{sat})}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcnhwrfzr 97ee03d524716628b8c48b1eafea14f6b6f7b51c68f6f992c9eb6f517989c7e5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 123:
公式 1429: \begin{align}    \mathrm{2H_2O} + \mathrm{2e^-} \rightarrow  \mathrm{2H_2+2OH^-}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyilhrr8i e5da88a2bf9f2b0e4d045b7cc452d99020474e2197e193b377c96726c30a326d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 124:
公式 1431: \begin{align}    \partial_t\rho^1+\nabla \cdot (\rho^1 \mathbf{u^1})=R, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6me3m00e 461c2a83b33c2f6403173cb84dc0a11314a178f6671fdfc195a9315d6929bbbf.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 125:
公式 1448: \theta=90 \degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp936dvulq 801bf09e2e43b920c06d56685f3484ca76f18c29fd8197f35a7e633b50c519fe.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 126:
公式 1446: \begin{align}f_c &= \frac{1}{V} \int_V H \, dV ,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_2hvfnik f5b8b148592eaa731d5e7661da9250926c6ac2194243333dd6c9ef03144ec74d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 127:
公式 1450: \theta =90 \degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvo97if08 974e4a7a7db5b59938fb61a4f96c74fa249a2fed7e43b3ab1c29a9f399408077.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 128:
公式 1453: \begin{align}    \int_{\Sigma} \dot{m}\,ds = \left(\frac{P_0}{\mathcal{R} T_0}\right) 4\pi R^2 \frac{dR}{dt} M\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjz8pqkvm 4b605cd3e4713dc898889edab62cd7c0dad865e0468304e786a1094512a14a35.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 129:
公式 1455: \begin{align}    \nabla \cdot \mathbf{u} &= \dot{m}\left(\frac{1}{\rho_d}-\frac{1}{\rho_c}\right) \delta_\Sigma,   \\    \partial_t \mathbf{u} + \nabla \cdot ( \mathbf{u} \otimes \mathbf{u}) &= \frac{1}{\rho}\left[-\nabla p + \nabla \cdot (2\mu \mathbf{D})\right] + \frac{\sigma k \mathbf{n_\Sigma}}{\rho}\delta_\Sigma, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg6kqg73j 38519c9255b18e031e250e7895fb2dc10d48336a8744d2d9e94a437130537bf3.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 130:
公式 1457: \begin{align}    H(x, t) =\begin{cases} 1, & \text{if } x \in \Omega_c , \\0, & \text{if } x \in \Omega_d .\end{cases}\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp467pnmvl 1fa5b72090653d6dafae0f81f945f2b51bf642a7f196cb4806ae6404cc97f2cb.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 131:
公式 1462: \begin{align}    \overline{\mathrm{Sh_{e}}}=\frac{P_0}{\mathcal{R} T_0} \frac{2R}{D(\bar{c_e}-c_{\Sigma})}\frac{dR}{dt}    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk3qi2w3r 2ecf18888019e13bbeb6e121dc95bb5c46a920d12266ba3672f53d1339171a94.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 132:
公式 1459: \begin{align}  \begin{split}      \frac{\partial}{\partial t} \int_V H \, dV       &+ \frac{1}{V} \int_V \nabla \cdot (H \mathbf{u}) \, dV \\      &+ \frac{1}{V} \int_V \frac{\dot{m}}{\rho_c} \delta_\Sigma \, dV       = 0  \end{split}   \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1vrve6_l 582811d100d42140ffcb29f95ec3c3b5cedef85f0b22f0cdd3e68dc41feed56e.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 133:
公式 1465: \begin{align}    J = \frac{I}{2F},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd5imecut a00b0f9fdef4bf2ed8bfae25ab1bf74d9363576dd6e47323390f38abfbf3f3b3.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 134:
公式 1471: \begin{align}    \partial_t\rho^1+\mathbf{u}\cdot \nabla \rho^1 +\nabla \cdot J^1=R, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpe26b1ke1 c03201c81485e3bb78609c025768dfea8c9aa219d2b6aa237370b92070d97085.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 135:
公式 1476: \begin{align}    \rho_c=\rho^1+\rho^2,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpleiyx8u9 b2b3c42fd1cf5574afe7f4a34641a4dbadfa14b55dcceb6dc1e64c5b37c70ec7.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 136:
公式 1478: \begin{align}    \dot{m}=-\frac{M^1D^1}{1-\frac{\rho^1}{\rho_c}}\frac{\partial c^1}{\partial \mathbf{n_\Sigma}}, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp156skn7x d7334d19e94723fd6f5966c9d9d99ba986755fe8c8d373282493404ed5c95c0a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 137:
公式 1483: \begin{align}    J^1=\rho^1(\mathbf{u^1}-\mathbf{u}).\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvi1p7s_j bb9cb6fefc60bcc7b8d1b612dcbe429f8656e5ec3e2e784cf51cf74fd38ff1ef.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 138:
公式 1481: \begin{align}  \begin{split}      \int_V \partial_t H \, dV       + \oint_{\partial V} H \mathbf{u} \cdot \mathbf{n} \, dS \\      + \int_\Sigma (\mathbf{u}_c - \mathbf{u}_\Sigma) \cdot \mathbf{n}_\Sigma \, dS       &= 0  \end{split}   \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_72vsj_v 6c293d14e3129bf077040fe3a5c512a5c82554fac73a5b283739e874bf6a3ffa.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 139:
公式 1488: \begin{align}    \left(p\mathbf{I} -2\mu \mathbf{D} \Vert \cdot \mathbf{n_\Sigma}\right)= \sigma k \mathbf{n_\Sigma}  \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjyi34u1s 3bb90c5528bf129ca5a03273a8ee7f41302eaefa518ae30066f0f81132173a45.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 140:
公式 1511: \frac{\partial \alpha}{\partial t} + \nabla \cdot (\alpha \bm{u}) + \nabla \cdot [\alpha(1 - \alpha)\bm{u}_r] = 0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqjkh6ido 53b047d5ebe519754427fa1b8e78bce9079cb586d5d5b6c250923dca75c7452f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 141:
公式 1518: \frac{\pi}{2}(1-b)\approx \frac{2a}{\sqrt{\myRe}}\beta_{max}\sqrt{\beta_{max}-1}, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp52kuq3i5 115c72c07c0675ad6b8f120ad3effa92b63fd0e658ac8747fd0132e6e980895d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 142:
公式 1528: {\color{red}{2a}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpmcsmm28b 62bacf2c82bbf81a7c3a1d8a4cab3402726c72e304ff312d10a7481ca246890a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 143:
公式 1545: \frac{\mathrm{d}\Delta}{\mathrm{d} t}+\frac{\Delta}{t+t_0}\left(1-\frac{h_{bl}}{h}\right)=-2(\Delta^2-c^2)(h/V)-\frac{u_0}{t+t_0}\frac{h_{bl}}{h}\underbrace{\left[2\left(1-\frac{\hbl}{h}\right)+\tfrac{1}{2}\frac{t+t_0}{t+t_1}\right]}_{=\Phi(t)\geq 0}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprb7fo6tv 4ac47999ab18a8ca53b5832a868f6fd64cad5c48978e32af06b4dbe371e4e3f4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 144:
公式 1543: \left(\frac{V_{tot}}{2h_{init}}\right)^2\frac{\cancel{V_{tot}}}{2\cancel{h_{init}}} \cancel{h_{init}}+\frac{\cancel{V_{tot}}}{2\cancel{h_{init}}}\cancel{h_{init}}h_{init}^2\sim \tfrac{3}{2}\cancel{(\pi R_0^2)}h_{init}^2,\qquad \mathrm{We}\gg 1, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1bsr0oqj 153e2e3c869aea3243baa1f7b244d861abc96c5c384e49dc5ff8ab25da6540ab.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 145:
公式 1576: {\color{red}{h}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu39oxto9 0af410566f6517e91443e974b16bd183187bf12e0cacebb9a22d172c206ddf86.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 146:
公式 1584: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\left(u_0-U\right)h,\\\frac{\mathrm{d} R}{\mathrm{d} t}&=&U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2\left(u_0-U\right)^2h-2\gamma\left(1-\cos\vartheta_a\right),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6cawc0xw 8f231ac51d5e3c58ca5fb0dbec68ace238e691493813aa5e413ce6a7d2288261.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 147:
公式 1604: \bm{f}_\gamma - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsikp5_j9 e6e1d942d17f3c16cc6d35bbc68be5afb7c20a2d5892b72974f5f3007a86f0ca.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 148:
公式 1617: c= \sqrt{ \frac{\surften}{\rho h}(1-\cos\vartheta_a)} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6l616c2g 5caf6f33f0f9a2d0a18994a50f4e44c5284dca2ee1001941d0cd04825ffe3153.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 149:
公式 1618: -\lambda\gamma(1-\cos\vartheta_a)-\frac{R}{t+t_0}\rho\lambda\left[h(R,t)-h_{bl}(t)\right]\left(\dot R-\frac{R}{t+t_0}\right)+\tfrac{1}{4}\frac{\rho\lambdah_{bl} R^2}{(t+t_0)(t+t_1)}=\frac{\mathrm{d} P}{\mathrm{d} t}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp665vqtjv 4aec057e6104eeeba1a55536706537078069dca045db66b9a4a12238fe9d981c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 150:
公式 1637: \nabla\cdot\bm{u}=0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6a806e36 c93131cc49a18552913c1b64ecce8d420edbd4975bbd39897e17380bc15faf92.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 151:
公式 1641: \bm{u} = (0,0,-1) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpj097tl3y 76339b5e07c90d318184ab5b2b96ba6f6bc7b4293424a24243e3c28289abbfbf.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 152:
公式 1647: \rho\left(\frac{\partial\bm{u}}{\partial t} + \bm{u}\cdot\nabla\bm{u} \right)= -\nabla p + \nabla \cdot \left[\mu\left( \nabla \bm{u}+\nabla\bm{u}^T\right)\right] +  \bm{f}_\gamma, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8g651xa8 d9593a0cb94324fecb97704fa486d20d21f5bb9e520fc76576ed90d8d485749d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 153:
公式 1706: \bm{u}=(0,0,-1)\,\mathrm{m}\cdot\mathrm{s}^{-1} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsk3h5fi1 4cf0563b335752e21e041f85709070440b9f3fc76b8f4e36e74f60c9af13a477.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 154:
公式 1704: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\left(\overline{u}-U\right)h,\\\frac{\mathrm{d} R}{\mathrm{d} t}&=&U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2\left[ \left(\overline{u}-U\right)^2 -c^2\right]h.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8sps74_h fb091d9b6048448abd71185a3e109f962c817dae9332893a930d20e678b2c5f0.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 155:
公式 1708: \begin{eqnarray}X&=& \frac{t+t_0}{\tau+t_0},\\Y&=&\frac{R}{V_{tot}/(2h_{init})},\\A&=&\frac{c_*^2(\tau+t_0)^2}{V_{tot}},\\c_*^2&=&4(h_{init}/V_{tot})(\gamma/\rho)(1-\cos\vartheta_a),\\B&=&\frac{4\Delta_{init}(\tau+t_0)h_{init}}{V_{tot}}\\\epsilon&=&\frac{V_{init}}{V_{tot}}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpifk7ir0u d8a7211a3689a515e180f86d0ea85e861685ea35049253bba8bcab4e9413e6ad.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 156:
公式 1722: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\left[ u_0\left(h-h_{bl}\right)-U h\right],\\\frac{\mathrm{d} R}{\mathrm{d} t}&=&U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2\left[(u_0-U)^2\left(h-h_{bl}\right)+U^2h_{bl}\right]\\&\phantom{=}&\phantom{aaaaa}+\tfrac{1}{2}\frac{h_{bl} R^2}{(t+t_0)(t+t_1)}-2\frac{\gamma}{\rho}(1-\cos\vartheta_a),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkin5c1km d2211e85f61153df126aede1b6a8d612d62fb111df5cc6debf81668a85350e0b.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 157:
公式 1725: \bm{u}_r - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8nct3u0v 8dd963f8cba99953578b720cdacfb7ffe5467e5956dbe37e9bf073785596fd16.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 158:
公式 1731: V\frac{\mathrm{d} U}{\mathrm{d} t}=2\left(\overline{u}-U\right)^2h+\underbrace{u_0^2 (h_{bl}/h)\left(1-\frac{\hbl}{h}\right)+\tfrac{1}{2}\frac{h_{bl} R^2}{(t+t_0)(t+t_1)}}-2\frac{\gamma}{\rho}(1-\cos\vartheta_a). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdwirxdyl 40e4d43c6b832770f4eab2ec0b04176fb8c649b5867efa9344036bcf73bbb7a0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 159:
公式 1727: {\color{red}{\hbl}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm7vbuggi 187eadb66224ff5b92e02578e80db34088ae21df73520bc4cffbcd87eceb4895.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 160:
公式 1763: \begin{eqnarray}\frac{R_{max}}{R_0}&=& Y_{max}\left(\frac{V_{tot}}{2h_{init}}\right),\\                                      									 									 									 &\stackrel{\text{Eq.~}}{\sim }&  \tfrac{4}{27}\frac{V_{tot}^2}{4 h_{init} (\gamma/\rho)(1-\cos\vartheta_a)(\tau+t_0)^2}\left(\frac{V_{tot}}{2h_{init}}\right),\qquad X\gg 1.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5leokz6u 614400e31f285fd966713e92fa4b421b078a27a1304b3d44833a8d46c96760c9.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 161:
公式 1773: \begin{eqnarray*}V_{tot}^2+4h_{init}^2\eta_{init}^2-4h_{init}V_{tot}\eta_{init}&=&\left(V_{tot}-2h_{init}\eta_{init}\right)^2,\\                                                    &=&\left(V_{tot}-2h_{init}R_{init}\right)^2,\\																										&=&V_{init}^2.\end{eqnarray*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5pabqdug cd863dc0b07a28a6f8fad308d06bf8ae5b507918cec829fc04fb81980819fa46.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 162:
公式 1789: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\Delta h,\\\frac{\mathrm{d} R}{\mathrm{d} t}&=&U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2(\Delta^2-c^2)h.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpq8s8dma2 55e8372eb5053dbd6104565f8da32648462ee2a35f8bf68b961d69a08b6f801f.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 163:
公式 1804: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=& -2U h_*,\\\frac{\mathrm{d} R}{\mathrm{d} t}&=& U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2\left[U^2h_* -\frac{\gamma}{\rho}(1-\cos\vartheta_a)\right].\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgzb_fa8g 52b63c2fc26b284bab9c544632007528750dcbb4f9f16f26881f6d0e7c789628.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 164:
公式 1814: {\color{red}{R}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmperzsxxso 5b946b7948a983238cd9ce2f03388ee41edcecf040d8e5f20d84fbdbcb3c4321.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 165:
公式 1824: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\Delta h,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2(\Delta^2-c^2)h.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpe7x0pz66 1810674af4050329dbbee63e5a73869acfec856df87ea26871236ec55743a29e.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 166:
公式 1845: \bm{u} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbxxdl0w4 5f59e7e480f3003906051230f0cc9d5649cb173520d3491841488daacb8bb5b6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 167:
公式 1851: K=\mathrm{We}\sqrt{\mathrm{Re}}\apprle 10^3-10^4 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpst9x55of 1afa8147284fe2862348d95e58fd165de3ef7b3ca6b338e31a1a05b786654f9e.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 168:
公式 1857: \begin{eqnarray}\frac{\mathrm{d} V}{\mathrm{d} t}&=&2\left(u_0-U\right)h,\\\frac{\mathrm{d} R}{\mathrm{d} t}&=&U,\\V\frac{\mathrm{d} U}{\mathrm{d} t}&=&2\left(u_0-U\right)^2h-2\gamma\left(1-\cos\vartheta_a\right).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmparg63_5e 572366b6126f6a015dac58ad8cada5daeb31db0c974892b71eb929ee9b1e2e12.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 169:
公式 1883: \begin{align}    J & = J_{guess} + J_{obs}  \\    & = \dfrac{1}{2} \left( \vec{x}_0 - \vec{\chi} \right)^T \boldsymbol{P}^{-1} \left( \vec{x}_0 - \vec{\chi} \right)  \\    & + \dfrac{1}{2} \left( \vec{T}^{(1)} - \vec{T}^{(obs)} \right)^T \boldsymbol{\Sigma}_T^{-1} \left( \vec{T}^{(1)} - \vec{T}^{(obs)} \right)  \\    & + \dfrac{1}{2} \left( \vec{Q} - \vec{Q}^{(obs)} \right)^T \boldsymbol{\Sigma}_Q^{-1} \left( \vec{Q} - \vec{Q}^{(obs)} \right). \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp99dnaq94 6205e0385d743cf035b2776cf6c0327daad066eb01d268db557888829f17c8d0.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 170:
公式 1943: \begin{align}    \mathcal{F}_t & = \sum_{c \in \mathcal{C}} f_{1}^{(c)} \ln\left( \dfrac{C^{(c)}_t}{C_{0}^{(c)}}\right) + f_{2}^{(c)} \left( C^{(c)}_t - C^{(c)}_0 \right) + f_{3}^{(c)} \left( \sqrt{C^{(c)}_t} - \sqrt{C^{(c)}_0} \right)  \\    & + \sum_{e \in \mathcal{E}} f_{1}^{(e)} \ln\left(1 + \dfrac{E^{(e)}_t}{C_{0}^{(e)}} \right) + f_{2}^{(e)} E^{(e)}_t \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5v20wp0l f8bc63b8c3c16d5a160b7dcaa86f6c732899de4f9feec8444459bc4038453ed1.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 171:
公式 1957: \begin{align}    \dfrac{d}{dt} \left( \boldsymbol{X}_t + \delta \boldsymbol{X}_t \right) & = \boldsymbol{M}_t\left( \boldsymbol{X}_t  \right) + \dfrac{\partial \boldsymbol{M}_t}{\partial \boldsymbol{X}_t}\bigg|_{\boldsymbol{X}_t} \delta \boldsymbol{X}_t + \mathcal{O}\left( || \delta \boldsymbol{X}_t ||^2 \right) \\    & \approx \boldsymbol{M}_t\left( \boldsymbol{X}_t  \right) + \boldsymbol{L}_t \delta \boldsymbol{X}_t \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmph3rdd58m 70f7ded60546b76fb10890b2972ecb9c8fa9749552e789a696a38505a377caba.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 172:
公式 1960: \begin{align}    C_1 \dfrac{dT_{t}^{(1)}}{dt} & = \mathcal{F}_t - \lambda T_{t}^{(1)} + \varepsilon \gamma \left(T_{t}^{(2)} - T_{t}^{(1)}\right) + q_t  \\    C_2 \dfrac{dT_{t}^{(2)}}{dt} & = \gamma \left( T_{t}^{(1)} - T_{t}^{(2)} \right)  \\    \dfrac{dQ}{dt} & = \mathcal{F}_t - \lambda T_{t}^{(1)} + (\varepsilon - 1) \gamma \left( T^{(2)}_t - T^{(1)}_t \right) + q_t. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpy544kb5v e8b3a17616dfadeaf3c94da75826f55d42d8122ee710a7cdee0c9676c89ab24d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 173:
公式 1973: \begin{align}    J_{obs} & = \dfrac{1}{2}          \left( \vec{T}^{(1)} - \vec{T}^{(obs)} \right)^T \boldsymbol{\Sigma}_T^{-1} \left( \vec{T}^{(1)} - \vec{T}^{(obs)} \right)  \\        & + \dfrac{1}{2} \left( \vec{Q} - \vec{Q}^{(obs)} \right)^T \boldsymbol{\Sigma}_Q^{-1} \left( \vec{Q} - \vec{Q}^{(obs)} \right) \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2cx6rsi1 fac5cdc00a9581f677fdae816956b111f98796929c5c3e404f20dff9027a6699.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 174:
公式 1977: \begin{align}    \Delta T_{slow} & \approx ECS - TCR = \dfrac{F_{2\times}}{\lambda} - \dfrac{F_{2\times}}{\lambda + \gamma}\\    & = \dfrac{\gamma F_{2\times}}{\lambda (\gamma + \lambda)}. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzrxjkasa bd404a49c06cdfca8cfae5de7cdc47c2f59069086bface7b6ad2df19221b8207.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 175:
公式 1995: \begin{align}    C_1 \dfrac{dT_{t}^{(1)}}{dt} & = \mathcal{F}_t - \lambda T_{t}^{(1)} + \varepsilon \gamma \left(T_{t}^{(2)} - T_{t}^{(1)}\right) + q_{t}  \\    C_2 \dfrac{dT_{t}^{(2)}}{dt} & = \gamma \left( T_{t}^{(1)} - T_{t}^{(2)} \right) \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzq5yucyq 0a47e6fb10ca237848ad92a48f0ad7279813ac89e6fd121237ac2a4012cbe002.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 176:
公式 2067: \begin{aligned}P'_{\text{eddy}} = \sum_{k=1}^M  \Weightk{k} \, L'_0 \, \Irevkdt{k} \, \Ieddyk{k} = \sum_{k=1}^M \Weightk{k}\, L'_0\, \Timeck{k}\, \Irevkdt{k}^2,\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpiuptep7i a8730be773cffa263919586707c001d33a2cca7dd33c39a7b1bfff09192c0071.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 177:
公式 2066: I\leqI_{\text{c}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp433bfwbq a43b1aff8f8b7aff3f5765a20a7092c816b14a9aa6c27db4c001588035dac2a2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 178:
公式 2073: \begin{aligned}P'_{\text{irr}} = \sum_{k=1}^M  \Weightk{k} \ L'_0 \ \Irevkdt{k} \ \Iirrk{k},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbqr9etf3 6633d816473fd10bd8fa7f1eedb92c198d1800d067da77ba9368dae86a60627d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 179:
公式 2079: \begin{align}\Phi'_{\text{int}} &= \int_{\Omega_{\text{c}}} \frac{1}{2\pi r} \ \mu_0\,\mathbf{h} \cdot \hat{\vec e}_{\varphi} \ \mathrm{d}\Omega_{\text{c}},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk47wzhm3 a851578b9b31c241dae97b15b60b845a0ab956b088f787131130719a392ca414.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 180:
公式 2072: (\si{\watt/ \meter}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk1zyuxkb 6de6bdaca73d6504f73a57e8259367982970640f0f638835d9c42e6d9a502169.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 181:
公式 2080: \begin{align}I_{\text{rev}} &= \left\{\begin{aligned}& I_{\text{rev}}^\text{prev} \quad &&\text{if } \left| I - I_{\text{rev}} \right| < \xi,\\& I  - \xi \ \dot I /|\dot I |\quad &&\text{if } \left|I  - I_{\text{rev}}\right| \geq \xi,\\\end{aligned}\right.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8k5hxy6f f4d5515728d5c4cb8a6f487bdc182838e6cf417a844cf8ccceacb49cc1e1fdd5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 182:
公式 2086: \begin{aligned}\Phi'_{\text{int}} = \Weightk \ L'_0 \ I_{\text{rev}},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpn3aoaplh abe84d10a8246d97982952748b2c98fc6a6979440b6c801d2111dbc4810a7a23.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 183:
公式 2088: \Fluxintk{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuxom9dgz 08165bc7a1fcb053da332603c1408b9010ccde9f6f9162be890b651e047a35e8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 184:
公式 2103: \Coerck{1} = 0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0n9a1jc7 f1aa423e444fbd781ca19086b80c37ee3665d582c7c02274cba530c3ef0ddc1d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 185:
公式 2097: \begin{aligned}P'_{\text{irr}} = \Dot{\Flux}'_{\text{int}}\ I_{\text{irr}} = \beta \ L'_0 \ \Dot{\I}_{\text{rev}} \ I_{\text{irr}},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpunpu_bp2 c02b42cad141955157019f8bff6b30b353db037f09819c774ea2587f9215f76f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 186:
公式 2116: \begin{align}G &= \left\{\begin{aligned}& G^{\text{prev}} &&\text{if } \left|I - G^{\text{prev}}\right| < \xi,\\& I - \xi \ \dot I/|\dot I|\quad &&\text{if } \left|I - G^{\text{prev}}\right| \geq \xi,\\\end{aligned}\right.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf39hxbpm 86fc30656a864376b92308699d907fca85ca60b117053a08e137db98836e3e30.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 187:
公式 2121: \Timeck{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqg67acsv 75443b9281b201306fc79db5e7aa8fe5dc97ff1ed76a6421a8012a609bdd104a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 188:
公式 2128: \begin{aligned} I_{\text{eddy}} &= \frac{\Timec}{\Delta t+\Timec} \left(G -  I_{\text{rev}}^{\textrm{prev}} \right).\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpoolvg3cy 4a34d19b0d4826d4fd8792b67f51b85d63ff1a25c20e9221edecfc74341a23b5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 189:
公式 2130: \Coerck{M+1} = \max(I) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7tar0uk8 28ebe0e5ccac48d3a06c26d8e57bd099cd7c55694b37988ededad9fdb7bc2ce9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 190:
公式 2133: \begin{aligned}R'_{\text{f}}(I_{\text{f}}) = \frac{\Vc}{\Ic} \left(  \frac{|I_{\text{f}}|}{\Ic}\right)^{n-1},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5fasj4ii 0bb7897e0e71b610b55995d3b7ad0151aee7d9d8fe51fdcddc7e521cba85e4c2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 191:
公式 2139: I = \Coerck{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpza2im0lz b4d55aa9be447fae1fb51a11535f253a153113aa1ac6fcc9190a5a45c83b9214.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 192:
公式 2141: \begin{aligned} I_{\text{rev}} &= \frac{1}{\Delta t+\Timec} \left( I_{\text{rev}}^{\textrm{prev}}\eta + G \ \Delta t  \right).\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfd66cqne 5b9e9bfed312f4f151f8d05b110af3dc4f3cd31c7e5f6806f183867f9dd8fefe.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 193:
公式 2142: \si{\volt/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpva7ny8pa a86235da1c0763d8505fb3c6a0b6ebde5098f5077cb0330e24d056903ea53eb8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 194:
公式 2143: \begin{aligned}R'_{\text{eq}} = \left(  \frac{1}{\Rmat} + \frac{1}{\Rfil}\right)^{-1}.\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp57td3ydo 47785923da51052e71565e47f58c1245a4cfb118d0375351769b11cbdfb02e1f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 195:
公式 2144: \begin{aligned}I_{\text{eddy}} &= \eta \ \Dot{\I}_{\text{rev}},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzp3gcmwr fa02199418875bf196b84691b12bb6eedb8f0b9d9867e9e54a150358eb6b2a02.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 196:
公式 2147: \Coerck{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6u4c0fy3 a9cfc7abfa8ae48479f0ae08350d2d430afb2813750992b3a2841a6a9cdf356c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 197:
公式 2148: \begin{aligned}P'_{\text{eddy}} = \Dot{\Flux}'_{\text{int}}\, I_{\text{eddy}} =  \beta\, L'_0\, \Dot{\I}_{\text{rev}}\, I_{\text{eddy}} = \beta\, L'_0\, \eta\, \Dot{\I}_{\text{rev}}^2.\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxkhi_jf7 736fc8f1281cbba88e0dfbf6f6cb4e2a7298fbe8a217d4874cdd4b5bb14efbb9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 198:
公式 2150: \si{\ampere/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwwie5pe8 3c0c47de75e6c8e49de8da558014855fa66882a0dfb966af35cf1b8258e2dfa4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 199:
公式 2151: \Ieddyk{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5vxs8es3 930dbb8ecefc57a91b8bd3bd2a9d10247bf9c130105eb38a0b067f3c2f24e2d9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 200:
公式 2155: \si{\milli\second} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu7e3al8a eaf2dd01848205a9ee6101fea208559688dfc46c9389e6825d3ceb0694dfd7b2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 201:
公式 2157: \Irevk{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2qhie4wx 7f9909708b112e23e7b3e75b1a27f0086664bf8766c539843305baafc0ebea7d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 202:
公式 2158: 0.1\,\si{\micro\second} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5jm5rc0x c4ff410c95300330ae7a30af962f006cc0e7a6cffc68527043ab5549381f0c8f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 203:
公式 2159: \si{\henry/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps4qf1zvn 49c01f9e59d619684abcd7cfdb15deed75816f4891f283412a4650536bb433d2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 204:
公式 2160: \si{\watt/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg62d98p8 7f29089268caaa32d525856dedb96789261bdad5f001e84dc9761ad7ec4ac447.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 205:
公式 2161: \begin{aligned}\Weightk{1} = \frac{\Fluxintk{2} - \Fluxintk{1}}{L'_0(\Coerck{2} - \Coerck{1})}, \quad \Weightk{k} = \frac{\Fluxintk{k+1} - \Fluxintk{k}}{L'_0(\Coerck{k+1} - \Coerck{k})} - \sum_{i=1}^{k-1} \Weightk{i},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd1jxpmfd 2aba13ec8c8bd0a45e1fbf985ebfcb88c5bd30ca672c567871b3d6d7c840e02a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 206:
公式 2162: \si{\weber/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpel4ypva2 de51dfeb4ddf2807d1f0b85a67be97ec93bc0c16771f6422169e8f846a6e8cb2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 207:
公式 2167: \Weightk{k} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkp5bcy8t 334903f4f1f29b5b144f91eeaafa0b16242ff8eb21773dbcfb101a01dd2b6b9e.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 208:
公式 2172: \begin{aligned}\Phi'_{\text{int}} = \sum_{k=1}^M  \Weightk{k} \ L'_0 \ \Irevk{k},\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxv6a_cr6 a76942d0870cddb887a4f2d7ee036c2c4c6743f21ef3c3cbb03dc8462ab2d2ed.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 209:
公式 2171: V'_{\text{c}}=10^{-4}\,\si{\volt/\meter} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgeyyurtd 14effe138238c1aa0691f10a7d63d4906226778cae1cc799514437e91b1f8fb4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 210:
公式 2176: \begin{align}&\hat{\delta f}_{k_x} = -\frac{q}{\bar{T}} \Bigg\{ \hat{\delta \Phi}_{k_x} - \left( \sum_{k_x'} \hat{\omega}_{d, k_x - k_x'} \hat{\delta \Phi}_{k_x'} - \omega \hat{\delta \Phi}_{k_x} \right)  \\&\sum_{\substack{m, m'\\n,'n} = -\infty}^{\infty}i^{(m'-m)}\f{ J_m\left(-\frac{k_x v_{\perp}}{\Omega}\right) J_{m'}\left(\frac{k_x v_{\perp}}{\Omega}\right) J_n\left(\frac{k_y v_{\perp}}{\Omega}\right) J_{n'}\left(\frac{k_y v_{\perp}}{\Omega}\right) } {k_z v_z + (n - m) \Omega - \omega}e^{i(m' - m + n - n')\theta}\Bigg\} f_0\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpho3ukv43 dae335529f39a2c7489d8d5991223de62fa108ec39d0c94e4e784e2de5e24793.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 211:
公式 2187: \begin{align}\delta \hat{N}_{k_x} = -\frac{N q}{T} \left\{ \hat{\delta \Phi}_{k_x} - \left( \sum_{k'_x} \hat{\omega}_{d,k_x - k'_x} \hat{\delta \Phi}_{k'_x} - \omega \hat{\delta \Phi}_{k_x} \right) \frac{1}{\omega} \left[ W\left( \frac{\omega}{|k_z| v_{th}} \right) - 1 \right]Y(k_x\rho,k_y\rho),\right\}\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbo5optpf de142890453cb09f5c34f6428e651fb18a5b7cd459dc4574aa12bba42ff2a204.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 212:
公式 2200: \begin{align}\begin{bmatrix}A_{-1,-1} & A_{-1,0} & 0\\A_{0,-1} & A_{0,0} & A_{0,1}\\0 & A_{1,0} & A_{1,1}\end{bmatrix}\begin{bmatrix}\hat{\delta \Phi}_{-1}\\\hat{\delta \Phi}_0\\\hat{\delta \Phi}_1\end{bmatrix}=0,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpb8a1j5g1 15df06e8d2386db5520e63d3f9bfd59a1ebfaf1781d6d0e51d8235c02b92085e.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 213:
公式 2217: \begin{align*}\frac{D}{Dt}\bigg\vert_{u.t.}\delta f= \bigg[ \frac{\p}{\partial t}+ \vec{v}\cdot \frac{\p}{\partial \vec{r}}+ \frac{q}{m}(\vec{v} \times \vec{B}) \cdot \frac{\p}{\partial \vec{v}}\bigg] \delta f=\frac{q}{m} \frac{\partial \delta \Phi}{\partial\vec{r}} \cdot\frac{\partial f_0}{\partial \vec{v}},\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpryrjapab 74f93ba222545cd014cb746ff2ab49f96729e3f67d8ee70240eb683d0199de92.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 214:
公式 2228: \begin{align}e^{iz\,{\rm sin}\theta}&=\sum_{n=-\infty}^{\infty}J_n(z)e^{in\theta},\\e^{iz\,{\rm cos}\theta}&=\sum_{n=-\infty}^{\infty} i^n J_n(z)e^{in\theta}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptyawxn3f 34a2bc8e89768b40124252932819fc47d69b26126c09c366ffef70d40989f412.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 215:
公式 2252: \begin{align}\delta f &=\sum_{k_x}\hat{\delta f} e^{i(k_xx+k_yy+k_zz - \omega t)},\\\delta \Phi &=\sum_{k_x}\hat{\delta \Phi} e^{i(k_xx+k_yy+k_zz - \omega t)}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprv9hdnhk 9b21ef937fc839e2d1c6f41f8a6b14ac3c22d47f882c8251cec4155e00bf29a7.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 216:
公式 2243: \begin{align*}&Y(k_x\rho,k_y\rho)= \\&\int \f{v_\perp dv_\perp}{v_{th}^2} \sum_{n,n'=-\infty}^{\infty}i^{n'-n}J_{-n}\left(-\frac{k_x v_{\perp}}{\Omega}\right) J_{n'}\left(\frac{k_x v_{\perp}}{\Omega}\right) J_n\left(\frac{k_y V_{\perp}}{\Omega}\right) J_{n'}\left(\frac{k_y v_{\perp}}{\Omega}\right)e^{-\f{1}{2}\f{v_\perp^2}{v_{th}^2}},\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpn84pj39v c603a8ba3f4395c093a193eddb0bccf3864153ef4f457537b8ea7146b7db6c0a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 217:
公式 2260: \begin{align}\omega_d(x)=\sum_{k_x}\hat{\omega}_{d,k_x} e^{ik_xx}=\frac{Tk_y}{qB}\left(\frac{d{\rm ln} N}{dx} + \frac{dT}{dx}\frac{\p}{\partial T}\right).\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpr_glj8wm 852a69254f66f7c924a303dd9bc3c00a19c29833741dfa9877c522b9a5b2dc2d.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 218:
公式 2271: \begin{align}\delta \Phi=\hat{\delta \Phi}_{-1}e^{-ik_{x}^{\rm ext}x} + \hat{\delta \Phi}_{0} + \hat{\delta \Phi}_{1}e^{ik_{x}^{\rm ext}x}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqehv1tk_ c4c515f74a687212f639a942ee4a79af9f0fd4d4b1c6e51b1f4bd03c941d624c.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 219:
公式 2269: \begin{align}k^2\hat{\delta\Phi} - \f{1}{\epsilon_0}\sum_{\rm species}q\hat{\delta N}_{k_x}=0,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqprj4prt 434dd8a56fa09e2d352a6ee3c1bfb4123f5922c82c31b5c450a38b333ffcafc7.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 220:
公式 2275: \begin{align}f_0(X,\mathcal{E}) &= f_0(x,\mathcal{E}) + \frac{\partial f_0(x,\mathcal{E})}{\partial x}\frac{v_y}{\Omega} + \mathcal{O}(\epsilon^2), {\rm where},\\\frac{\partial f_0}{\partial x}&=\bigg( \frac{d {\rm ln} N}{dx} + \frac{dT}{dx}\frac{\p}{\partial T}\bigg) f_0. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsrjmr6wl 65eb89b13b4d92e42fbb38df4d2412d3730a6e0513291d4ced8f8b0a2168dc93.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 221:
公式 2288: \begin{align}i\vec{k}\cdot\vec{v}'e^{i(\vec{k}\cdot\vec{r}'-\omega t')}= \left(i\omega + \f{d}{dt'} \right) e^{i(\vec{k}\cdot\vec{r}'-\omega t')}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo1vgn0dq 711baed98dbf818fab5687c79f67c88c280ab0a145f5eaea6cfc6d6bb802bf7e.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 222:
公式 2304: \begin{align*}{\bf Q}(\tau) =\left(\begin{matrix}{\rm sin}(\Omega\tau)  &  -[{\rm cos}(\Omega\tau) - 1]  &  0\\{\rm cos}(\Omega\tau) - 1  &  {\rm sin}(\Omega\tau)  &  0\\0  &  0  &  \Omega\tau\end{matrix}\right)\\{\bf R}(\tau) =\frac{1}{\Omega}\frac{d}{d\tau} {\bf Q}=\left(\begin{matrix}{\rm cos}(\Omega\tau)  &  {\rm sin}(\Omega\tau)  &  0\\-{\rm sin}(\Omega\tau)  &  {\rm cos}(\Omega\tau)  &  0\\0  &  0  &  1\end{matrix}\right).\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5e9yfsbv b1293def4448603cd8d9047d516da23f76f81c80df9f67f1b2bacb89861060e3.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 223:
公式 2309: \begin{align*}\frac{\partial \delta f}{\partial t}+ \vec{v}_E \cdot \left( \nabla f_{0} - \frac{\mu}{m v_{\|}} \nabla B_0 \frac{\partial f_{0}}{\partial v_{\|}} \right)+ (\vec{v}_E + \vec{v}_{\nabla B} + \vec{v}_c) \cdot \vec{\Gamma}+ v_{\|} \hat{b}_0 \cdot \vec{\Gamma}- \frac{\mu}{m} \left( \hat{b}_0 + \frac{\vec{v}_c}{v_{\|} } \right) \cdot \nabla B_0 \frac{\partial \delta f}{\partial v_{\|}} = 0;\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdc6xgp78 ebc3bafa7fca26f254e81efee58a1d4d122312a49298781e1084565944218925.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 224:
公式 2331: \begin{align}\frac{\partial f_0}{\partial\vec{v}}= \bigg[ \frac{\hat{e}_y}{\Omega}\bigg( \frac{d {\rm ln} N}{dx}+ \frac{d T}{dx}\frac{\p}{\partial T}\bigg) - \frac{\vec{v}}{v_{th}^2}\bigg] f_0,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgmbew741 794511754545b7aa1f3808fbbbbcb02983522390643165c27d1455ff7551ba45.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 225:
公式 2333: \begin{align}\omega_T \longrightarrow \underbrace{\omega_T + \omega_T^{\rm ext}{\rm sin}[(2\pi/L_x)k_{x,{\rm ind}}x + \varphi^{\rm ext}]}_{\omega_T^{\rm tot}},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwfejrzau 1b854d97ac3cf5eb3061f86dc2c816cc19bfc14d4cf0f85060619dede548204a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 226:
公式 2339: \begin{align}\hat{\delta f}_{k_x}= \frac{q}{m}\sum_{k'_x}\f{i}{v_{th}^2}\left[\hat{\omega}_{d,k_x-k'_x} - \vec{k}\cdot \vec{v}'\delta_{k_x,k'_x}\right]\hat{\delta \Phi}_{k'_x} \int_{-\infty}^{t} dt' e^{i(\vec{k}[\vec{r}'-\vec{r}] - \omega[t'-t])}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8lffimn3 f00646029ef6d78fda848410d1f4ffb85d5e1b59009a9041a328b68c13324856.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 227:
公式 2342: \begin{align*}\frac{1}{\omega} \left[ W\left( \frac{\omega}{|k_z| v_{th}} \right) - 1 \right]= \f{1}{\sqrt{2\pi}}\int \f{dv_z}{v_{th}}\f{e^{-\f{1}{2}\f{v_z^2}{v_{th}^2}}}{k_zv_z - \omega},\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0xshj5xy d61b18726679af1e3204ffdb0642e9afcebb55e2e17747dadf0634003fa26cb6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 228:
公式 2351: \begin{align}\nabla_x f_0=\left[\frac{\omega_n}{L_{\rm ref}} + \left(\f{mv_\parallel^2}{2T_0} + \f{\mu B_0}{T} - \f{3}{2}\right) \frac{\omega_T}{L_{\rm ref}}-\f{\mu}{T_0}\f{\partial B_0}{\partial x}\right]f_0,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_y31deog bb0fd54dce17572a1836d0657e959254cb13ca4712b110b89795aa8efca80ff9.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 229:
公式 2368: \begin{align*}f_0(X,\mathcal{E})=\frac{N(X)}{[2\pi T(X)/m]^{3/2}}e^{-\mathcal{E}/T(X)},\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpj9_fj_c0 41aa4f465194daca3561095f803d09390abf587c5b01f7ad56e79c3a9780bf46.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 230:
公式 2373: \begin{align}\omega_d(x)&=\hat{\omega}_{d,1}e^{-ik_{x}^{\rm ext}x} + \hat{\omega}_{d,0} + \hat{\omega}_{d,1}e^{ik_{x}^{\rm ext}x} \\&=\hat{\omega}_{d,0} + \hat{\omega}_d^{\rm ext}~{\rm cos}(k_{x}^{\rm ext}x)\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpeclgunkl 4d8125e98ab8f83a92fe67bbdb42e3b3aaf4fd3216ebe1ca13c395195710e253.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 231:
公式 2383: \begin{align}\delta f(\vec{r},\vec{v},t)= \frac{q}{m} \int_{-\infty}^{t} dt' \frac{\partial \delta \Phi}{\partial\vec{r}}\cdot\frac{\partial f_0}{\partial \vec{v}}\bigg\vert_{\vec{r}',\vec{v}',t'},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppx5897u_ c106ad4d51c74bb36263c3c6c22c6c894f5a0d06b0e7636e921594e759019176.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 232:
公式 2400: \begin{align}\vec{r}'(t')=& \vec{r} + \frac{1}{\Omega}{\bf Q}(\tau)\vec{v},\\\vec{v}'(t')=& {\bf R}(\tau)\vec{v},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp3_qp3ebr 00af87ebc6f33a6a38c92eeffc7434eca6f4d0df70f5765c6849a9e73543bbc4.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 233:
公式 2409: \begin{align}&\int_{-\infty}^tdt'e^{i(\vec{k}\cdot [\vec{r}'-\vec{r}] - \omega [t' - t])}=  \\&\sum_{\substack{m, m'\\n,'n}=-\infty}^\infty i^{(m'-m)}\f{J_m\left(\f{-k_xv_\perp}{\Omega}\right) J_{m'}\left(\f{k_xv_\perp}{\Omega}\right)J_n\left(\f{k_yv_\perp}{\Omega}\right) J_{n'}\left(\f{k_yv_\perp}{\Omega}\right)}{i(k_zv_z + [n-m]\Omega - \omega)}e^{i(m'-m+n-n')\theta}.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpi7dfiina a9cc519ea2d5834b8c42018eb6d49f80d7cedeb1458744cba84bf0bfe444dca6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 234:
公式 2429: \begin{align*}&A_{-1,-1}=  1 + \sum_{s} \frac{1}{(k_1\lambda_D)^2}\left\{1 + \frac{\omega - \hat{\omega}_{d,0}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(-k_x^{\rm ext}\rho,k_y\rho)\right\},\\&A_{-1,0} =  -\sum_{s} \frac{1}{(k_1\lambda_D)^2}\left\{\frac{\hat{\omega}_{d,1}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(-k_x^{\rm ext}\rho,k_y\rho)\right\},\\&A_{0,-1} =  -\sum_{s} \frac{1}{(k_0\lambda_D)^2}\left\{\frac{\hat{\omega}_{d,1}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(0,k_y\rho)\right\},\\&A_{0,0} =  1 + \sum_{s} \frac{1}{(k_0\lambda_D)^2}\left\{1 + \frac{\omega - \hat{\omega}_{d,0}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(0,k_y\rho)\right\},\\&A_{0,1} =  -\sum_{s} \frac{1}{(k_0\lambda_D)^2}\left\{\frac{\hat{\omega}_{d,1}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(0,k_y\rho)\right\},\\&A_{1,0} =  -\sum_{s} \frac{1}{(k_1\lambda_D)^2}\left\{\frac{\hat{\omega}_{d,1}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(k_x^{\rm ext}\rho,k_y\rho)\right\},\\&A_{1,1} =  1 + \sum_{s} \frac{1}{(k_1\lambda_D)^2}\left\{1 + \frac{\omega - \hat{\omega}_{d,0}}{\omega}\bigg[W\bigg(\frac{\omega}{|k_z|v_{th}}\bigg) - 1\bigg]Y(k_x^{\rm ext}\rho,k_y\rho)\right\},\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvx16p61o 6a447e44670e46a2440df72f0290a78d2fb73e5b48ffe1f486561b932d84ec2b.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 235:
公式 2441: \begin{align} \mathcal{C}^{(3)}_{i+j+k} &= \frac{1}{\pi} \sqrt{\frac{g}{2 \omega_{i+j+k}}} \left[ B^{(1)}_{i+j+k,i,j,k} - B^{(4)}_{-i-j-k,i,j,k} \right] - \frac{1}{4 \pi^2} \left[ \sqrt{\frac{\omega_k}{\omega_{i+j}}} |k_i+k_j| \left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \right) \right.\\& \left. + \sqrt{\frac{\omega_{i+j}}{\omega_k}} |k_k| \left( A^{(1)}_{i+j,i,j} + A^{(3)}_{-i-j,i,j} \right) \right] - \frac{1}{32 \pi^3} \sqrt{\frac{2 \omega_j \omega_k}{g \omega_i}} \left[ |k_i| \left( |k_i| - |k_i+k_j| - |k_i+k_k| \right) \right], \\  \mathcal{C}^{(3)}_{i-j-k} &= -\frac{1}{\pi} \sqrt{\frac{g}{2 \omega_{i-j-k}}} B^{(2)}_{j+k-i,i,j,k} + \frac{1}{4 \pi^2} A^{(2)}_{j-i,i,j} \left[ \sqrt{\frac{\omega_{j-i}}{\omega_k}} |k_k| + \sqrt{\frac{\omega_k}{\omega_{j-i}}}|k_i - k_j| \right] \\& - \frac{1}{32 \pi^3} \sqrt{\frac{2 \omega_j \omega_k}{g \omega_i}} \left[ |k_i| \left( |k_i| - |k_i-k_j| - |k_i-k_k| \right) \right], \\ \mathcal{C}^{(3)}_{i+j-k} &= - \frac{1}{\pi} \sqrt{\frac{g}{2 \omega_{i+j-k}}} B^{(3)}_{k-i-j,i,j,k} - \frac{1}{4 \pi^2} \left[ \sqrt{\frac{\omega_k}{\omega_{i+j}}}|k_i+k_j| \left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \right) \right. \\& \left. - \sqrt{\frac{\omega_{i+j}}{\omega_k}} |k_k| \left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \right) \right] + \frac{1}{32 \pi^3} \sqrt{\frac{2 \omega_j \omega_k}{g \omega_i}} \left[ |k_i| \left( |k_i| - |k_i+k_j| - |k_i-k_k| \right) \right], \\\mathcal{C}^{(3)}_{i-j+k} &= - \frac{1}{4 \pi^2} A^{(2)}_{j-i,i,j} \left[ \sqrt{\frac{\omega_{j-i}}{\omega_k}} |k_k| - \sqrt{\frac{\omega_k}{\omega_{j-i}}} |k_i - k_j| \right] - \frac{1}{32 \pi^3} \sqrt{\frac{2 \omega_j \omega_k}{g \omega_i}} \left[ |k_i| \left( |k_i| - |k_i-k_j| - |k_i+k_k| \right) \right]. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2j67khw8 e5139ded01fc0a48b4c6e56b6395c9c54f39487695b607b7b6cbd32f6636fc74.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 236:
公式 2461: \begin{align}  \zeta_3(x,t) = & \frac{1}{2 \pi} \sum_{i,j,k} A_i A_j A_k \left\lbrace \sqrt{\frac{\omega_{i+j+k}}{2g}} \left( B^{(1)}_{i+j+k,i,j,k} + B^{(4)}_{-i-j-k,i,j,k} \right) \left[ e^{i(\xi_i + \xi_j + \xi_k)}  \right. \right. \\  & \left. + e^{-i(\xi_i + \xi_j + \xi_k)}  \right] + \sqrt{\frac{\omega_{i-j-k}}{2g}} B^{(2)}_{j+k-i,i,j,k} \left[ e^{i(\xi_j+\xi_k-\xi_i)}  \right.   \left. \left. + e^{i(\xi_i -\xi_j - \xi_k)} \right] \right. \\& \left. + \sqrt{\frac{\omega_{i+j-k}}{2g}} B^{(3)}_{k-i-j,i,j,k} \left[ e^{i(\xi_k-\xi_i-\xi_j)}  + e^{i(\xi_i+\xi_j-\xi_k)}  \right] \right\rbrace.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpihifnc78 0d2b33b821533998ebef5c1edeeaf59d105e0e3de0a8edcc0f58d816754cd410.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 237:
公式 2471: \begin{align} x'(t) &= a \omega e^{kz_0} \cos(\xi_0) - a^2 k \omega e^{2kz_0} \cos(\omega t) + a^2 k \omega e^{2kz_0},\\ z'(t) &= a \omega e^{kz_0} \sin(\xi_0) + a^2 k \omega e^{2kz_0} \sin(\omega t). \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpi_7yhled fb27e333c873f9e6bd027e269e53e4400a743033d6dd312f217e65085dd5f8ce.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 238:
公式 2482: \begin{align} x'(t) = a \omega e^{kz} \cos(\xi),\\ z'(t) = a \omega e^{kz} \sin(\xi).\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsimy8tx0 00330fd69d0cec30cff349f6e0cc81205d51d325c1bc6316940a6083d2e9caec.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 239:
公式 2480: \begin{align} & \zeta_1 = a \cos(\xi), \quad \phi_1 = \frac{a\omega}{k}  e^{kz} \sin(\xi),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv8zulk13 c067b33ee29f4e48a7717afd18f95335e5eee5fb3db289a550cf8e28ad64e4cc.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 240:
公式 2505: \begin{align} \phi &= \frac{ag}{\omega} \left(1-\frac{a^2 k^2}{4}\right) e^{kz} \sin(\xi) \\ \zeta &= a \left( 1- \frac{a^2 k^2}{8} \right) \cos (\xi) + \frac{a^2 k}{2} \cos(2\xi) + \frac{3}{8} a^3 k^2 \cos(3\xi) \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvhha6z1l f19d9179c30b1147b88e608c060ae444915e411f9feebc27800b21042e663d55.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 241:
公式 2517: \begin{align}\phi(x,z,t) = \frac{a_1 g}{\omega_1} e^{k_1 z} \sin(\xi_1) + \frac{a_2 g}{\omega_2} e^{k_2 z} \sin(\xi_2) - \omega_1 a_1 a_2 e^{(k_1-k_2)z} \sin(\xi_1 - \xi_2),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1fx_7876 19266b36578db693c09f9a70b7ff6cdb5d7e16ca1dd585e311d73a03931ec355.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 242:
公式 2509: \begin{align}\phi(x,z,t) = \frac{a_1 g}{\omega_1} e^{k_1 z} \sin(\xi_1) + \frac{a_2 g}{\omega_2} e^{k_2 z} \sin(\xi_2). \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzx5zy50j 50d47a3fd444f8cc719f00d8421f2616fbc3783df15ea5191f52d27ba2c7d053.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 243:
公式 2531: \begin{align}  \zeta_2(x,t) = & \frac{1}{2\pi} \sum_{i,j} A_i A_j \left\lbrace \sqrt{\frac{\omega_{i+j}}{2g}} \left( A^{(1)}_{i+j,i,j} + A^{(3)}_{-i-j,i,j} \right) \left[ e^{i(\xi_i + \xi_j)}  + e^{-i(\xi_i + \xi_j)}  \right]  \right. \\ & \left. + \sqrt{\frac{\omega_{i-j}}{2g}} A^{(2)}_{j-i,i,j} \left[  e^{i(\xi_j-\xi_i)} + e^{i(\xi_i-\xi_j)} \right] \right\rbrace. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv6fkjrre 785e1fba8597d660faa150458ec9c6a3cff971dac09007e771a40e2efe40a42a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 244:
公式 2544: \begin{align*} \phi_3(x,z,t) &= \sum_{ijk} A_i A_j A_k \left\lbrace \mathcal{C}^{(3)}_{i+j+k} \sin(\xi_i+\xi_j+\xi_k) e^{|k_i+k_j+k_k|z} \right. \\ &+ \mathcal{C}^{(3)}_{i-j-k} \sin(\xi_i-\xi_j-\xi_k) e^{|k_i-k_j-k_k|z}  \left.  + \mathcal{C}^{(3)}_{i+j-k} \sin(\xi_i + \xi_j - \xi_k) e^{|k_i+k_j-k_k|z} \right. \\ &+ \left. \mathcal{C}^{(3)}_{i-j+k} \sin(\xi_i-\xi_j+\xi_k) e^{|k_i-k_j+k_k|z} \right\rbrace. \numberthis\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptfa6c5z3 55acafde4ccab67a25efad14a682b211e0b230252839b55518d43502f87afcac.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 245:
公式 2554: \begin{align} \Omega_1 &= \omega_1 \left[ 1+ \frac{1}{2} \epsilon_1^2 + \epsilon_2^2 \left( \frac{k_1}{k_2} \right)^{1/2} \right],\\ \Omega_2 &= \omega_2 \left[ 1+ \frac{1}{2} \epsilon_2^2 + \epsilon_1^2 \left( \frac{k_2}{k_1} \right)^{3/2} \right],\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyja1zafj ed803466ac2d79f9f40d278f16347465d78316b2ad641ca36fe9aae4bbf1aea1.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 246:
公式 2555: \begin{align}  \phi &= \frac{ag}{\omega} e^{kz} \sin(\xi) \\ \zeta &= a \left( 1+ \frac{a^2 k^2}{8} \right) \cos (\xi) + \frac{a^2 k}{2} \cos(2\xi) + \frac{3}{8} a^3 k^2 \cos(3\xi) \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4bt9xa1r 0ef0348a164496e72a2a36194b8cc03bfc9f1ec5c9a2e7de4cdb62a93fea3f0b.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 247:
公式 2559: \begin{align*} \phi_2(x,z,t) & =  \sum_{i,j} A_i A_j  \left[ \mathcal{C}^{(2)}_{i+j} \sin(\xi_i + \xi_j) e^{|k_i+k_j|z} + \mathcal{C}^{(2)}_{i-j} \sin(\xi_i - \xi_j) e^{|k_i-k_j|z} \right] \end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1sjd6p_y c9d432dbb61f3b60a4c87b90d6478eb9bad2060f45169fb5ca1542ef4cba2bc6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 248:
公式 2573: \begin{align}\mathcal{C}^{(2)}_{i+j} &= \frac{1}{\pi} \sqrt{\frac{g}{2 \omega_{i+j}}} \left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \right) - \frac{1}{4 \pi^2} \sqrt{\frac{\omega_i}{\omega_j}}|k_j|,\\\mathcal{C}^{(2)}_{i-j} &= -\frac{1}{\pi} \sqrt{\frac{g}{2 \omega_{i-j}}} A^{(2)}_{j-i,i,j} + \frac{1}{4 \pi^2} \sqrt{\frac{\omega_i}{\omega_j}}|k_j|.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpptt3i4iu 08e3cc9a983323c4cd187c2358583c0d387ae1a317040887f856fc5b046f8718.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 249:
公式 2580: \begin{align}&\Delta \phi = 0, \\&\phi = \psi \text{ on } z = \zeta, \\&\phi_z \rightarrow 0 \text{ as } z \rightarrow -\infty,\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgapl6wnm 8f081aabd1c2988cf6208061a13de6bd7c2b03b6baff304212dcd4176e85c340.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 250:
公式 2596: \begin{align} \zeta(x) = \frac{1}{2\pi} \int \left( \frac{q(k)}{2 \omega(k)}\right)^{1/2} \left[ a(k) + a^*(-k)\right] e^{ikx} dk, \\ \psi(x) = \frac{-i}{2\pi} \int \left( \frac{\omega(k)}{2 q(k)}\right)^{1/2} \left[ a(k) - a^*(-k)\right] e^{ikx} dk, \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg7yl0kuv 7849d3f2756f0cb2f21dcc42aec793de2e0e21f106ef1a90ef5f23f761f1b53b.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 251:
公式 2609: \begin{align}x'(t) = a_1 \omega_1 e^{k_1z_0} \cos(k_1x_0-\omega_1 t) + a_2 \omega_2 e^{k_2 z_0} \cos(k_2 x_0-\omega_2 t) ,\\z'(t) = a_1 \omega_1 e^{k_1 z_0} \sin(k_1 x_0 - \omega_1 t) +  a_2 \omega_2 e^{k_2 z_0} \sin(k_2 x_0 - \omega_2 t),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk17doa56 983513a9f28a55054be64e9897e52db5eba53f8a73c78a3e0c2bb9990cbc37cc.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 252:
公式 2605: \begin{align} &\Delta \phi = 0 \text{ on } -h < z < \zeta, \\ &\zeta_t + \nabla_x \phi \cdot \nabla_x \zeta = \phi_z \text{ on } z = \zeta, \\ &\phi_t + \frac{1}{2}(\nabla \phi)^2 + g\zeta = 0 \text{ on } z= \zeta, \\ &\phi_z = 0 \text{ on } z= -h.\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppcb6z1qp 206996cc822c3f08cde168aef06c49cefa78cdfb9a8f458216df4c44b168f3f1.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 253:
公式 2613: \begin{align}x'(t) = a \omega e^{kz_0} \cos(\xi_0),\\z'(t) = a \omega e^{kz_0} \sin(\xi_0),\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptglqq4_a a841fcbd867d1e7beed02b8c09abf856500350fde9fb4638fc304ce30af699d6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 254:
公式 2624: \tag{'} \phi_z \rightarrow 0 \text{ as } z \rightarrow \infty - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbe6ok6i0 6489017e140f7174e1bbdd3ec61a959cd38a059307e4a61314fbee5ed8affb53.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \tag not allowed here.
--------------------------------------------------------------------------------

错误 255:
公式 2630: \begin{align}  a_0 = b_0 &+ \int A^{(1)}_{012} b_1 b_2 \delta_{0-1-2} dk_{12} + \int A^{(2)}_{012} b_1^* b_2 \delta_{0+1-2} dk_{12} + \int A^{(3)}_{012} b_1^* b_2^* \delta_{0+1+2} dk_{12} \\  &+ \int B^{(1)}_{0123} b_1 b_2 b_3 \delta_{0-1-2-3} dk_{123} + \int B^{(2)}_{0123} b_1^* b_2 b_3 \delta_{0+1-2-3} dk_{123} \\  &+ \int B^{(3)}_{0123} b_1^* b_2^* b_3 \delta_{0+1+2-3} dk_{123} + \int B^{(4)}_{0123} b_1^* b_2^* b_3^* \delta_{0+1+2+3} dk_{123} + \ldots\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptgcfjru7 8f7ed11d03602f85b586bddfd954d218e57ed10dec243dc9781c86dfd180a1c5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 256:
公式 2655: \begin{align} \mathbf{x}'(t) = \nabla \phi(x,z,t).\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpe7_eou9s 47bce507168128adf9c8c3931626617f3b3611d4369933bc34f89a3329dd3520.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 257:
公式 2695: \begin{align*}f_{x}=&x\cdot (M+F_1) +y\cdot M + z\cdot (M-F_2-Q)\\=&M+xF_1-zF_2-zQ.\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_id9v72g c01a91b8e4d7cb214156356704415ec257e5e19714a3395e756d85eab0c385c4.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 258:
公式 2849: \begin{align*}f_{z}=&x\cdot (R+F_1+P) +y\cdot R + z\cdot (R-F_2)\\=&R+xF_1-zF_2+xP.\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpz65a7lz2 2c5d7d8cf08fb3b193256e4eb4c85006c28769171a7cb08b639b50c181d59625.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 259:
公式 2894: \begin{align*}f_{y}=&x\cdot (N+F_1) +y\cdot N + z\cdot (N-F_2)\\=&N+xF_1-zF_2.\end{align*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps2_nch8n 49ea3a5c67a8c733e02f2579fccef29bd739f4c38a22df161b3432d0cb80a1fc.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align*} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 260:
公式 2955: \begin{align}{\delta} &= \frac{\lambda}{2 \pi} \sin ^{ -1 }{ \frac { \sqrt { { ( { I }_{ 3 }-{ I }_{ 1 } ) }^{ 2 }+{ ( { I }_{ 2 }{-I }_{ 4 } ) }^{ 2 } } }{ ({ I }_{ 1 }+{ I }_{ 2 }+{ I }_{ 3 }+{ I }_{ 4 } )/2} },\\[6pt]\varphi &=\frac{1}{2}\tan ^{ -1 }{ \frac { { I }_{ 3 }-{ I }_{ 1 } }{{ I }_{ 2 }-{ I }_{ 4 }}},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu62r5hvk 74c13b062440ec4c4120467b7f4a798818c63a182c0fae01cac7ef92d22477d5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 261:
公式 2966: \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdh62iezg 72a42d5635c6e2bf6798eda9b6b90328529d6ae0fb0e7b15b54d59d10a9e60b4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 262:
公式 2979: \bm{n} \neq C \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7daont45 068896abf15d8f42af74a8e0470623bc86f52dac2d75c9670f35a778055a82ff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 263:
公式 2996: \bm{n} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpa71ud_pr 0850f86756af672298034ef4267a5c9aae8b16cef2a2c9008807ae86a5ed9c55.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 264:
公式 3026: \bm{n} = C \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpnf_27lox e3acf93618aa02ddc260fd6429a96a1737334eaabd5e0fbccc77b63c47dc55b5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 265:
公式 3071: \begin{eqnarray} \Delta E_a^{(-)} = \frac{e^2}{\pi} \sum_{n^{(-)}} \left[  \frac{1-\bm{\alpha}_1\bm{\alpha}_2}{r_{12}} I^{\beta}_{n^{(-)} a}(r_{12})\right]_{an^{(-)} n^{(-)} a},\,\,\,\, \\ I^{\beta}_{n^{(-)} a}(r_{12}) = \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega) \sum\limits_{\pm}\frac{\sin\omega r_{12}}{\varepsilon_a-\varepsilon_{n^{(-)}} \pm \omega + \mathrm{i}\,0},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphxybi0kv ef295cc192649e8c33ce6e4c888a7edad0d1ff47dbab209fcf7c9f1c23b273f3.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 266:
公式 3066: \begin{eqnarray}    \Delta E_a = \frac{e^2}{\pi} \sum\limits_{L} (2L + 1)\int\limits_{0}^{\infty}d\omega\,\omega\,n_{\beta}(\omega)\\\times\sum\limits_{n}\sum\limits_{\pm}\frac{\langle a |\alpha_{\mu}j_{L}(\omega r)\bm{C}_{L}|n\rangle \langle n| \alpha^{\mu}j_{L}(\omega r)\bm{C}_{L}|a \rangle}{\varepsilon_a-\varepsilon_n \pm \omega + \mathrm{i}\,0}\\=\frac{e^2}{\pi} \sum\limits_{L} (2L + 1)\int\limits_{0}^{\infty}d\omega\,\omega\,n_{\beta}(\omega)\alpha_{L}(\omega).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpti6nqjuk 45953acd2202173ecd70ced92652994f5adb6f3f0b903e689b952938aa71c729.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 267:
公式 3070: \begin{eqnarray}\langle \mathcal{E}^2(t)\rangle = \frac{1}{2}\int\limits_0^\infty \mathcal{E}^2(\omega)d\omega = (831.9 \text{ V/m})^2\left[\frac{\text{T}(\text{K})}{300}\right]^4,\\\langle \mathcal{B}^2(t)\rangle = (2.775 \times 10^{-6} \text{ Tesla})^2\left[\frac{\text{T}(\text{K})}{300}\right]^4.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpl9b3jbe9 8427c3eeb8f19ecdb69a08230cb34e3958d0d463feb862d5ba20803e1beab793.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 268:
公式 3086: \begin{eqnarray}\varphi_{a}= \left[  1 - \frac{(\bm{\sigma} \bm{p})^2}{8 m_e^2c^2}  \right] \phi_{a},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprgcs27nh b50fbf9502c96d679701b8bff08a0c089bde71817199cb303a2df01ddadef17b.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 269:
公式 3088: [\bm{r}\times \bm{\alpha}]_i = \epsilon_{ijk} r_j\alpha_k - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpunih81_s 04c11c6362a46dc654bfa8ea48ead240bccde0c515dbe478ec6e34c9dd9ff9b3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 270:
公式 3091: \begin{eqnarray}    \Delta E_{a}  = \sum \limits_{\substack{n \ne a}} \frac{|( a|\hat{V}| n )|^2}{E_a-E_n}     = e^2 \sum_{\substack{i,\,n \ne a}} \frac{\mathcal{E}^2_{i} |( a|r_{i}| n  )  |^2}{E_a-E_n},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkdrgxkhy 5111522bc4b5a510b70cbb0c0eb748b0642122f5cea3a33f4c02e5f2bee5eccc.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 271:
公式 3101: \psi_{a}(x)= \psi_{a} (\bm{r}) e^{-i \varepsilon_{a} t} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpehajiwkn adac0c45a731c3338f834a7f999b77c6d86fe892846380223d67c8c87446412d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 272:
公式 3106: \langle a n| (\bm{r}_1 \bm{\alpha}_2) (\bm{r}_2 \bm{\alpha}_1) + (\bm{\alpha}_1 \bm{\alpha}_2) (\bm{r}_1 \bm{r}_2) | n a \rangle - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpluhwdme2 2edd04b1d37e635798cc1d024ef2775ff7587b4b90b1c5555f59502cfd8f0382.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 273:
公式 3108: \bm{p} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2zzsr7xj ac365e428b7eee7ef6246027a8ab163a923250099e026db2ca98d673d77f55ae.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 274:
公式 3110: \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpike_8jyu 72a42d5635c6e2bf6798eda9b6b90328529d6ae0fb0e7b15b54d59d10a9e60b4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 275:
公式 3123: \begin{eqnarray}J_{5} = -\frac{e^2}{15\pi} \sum\limits_n \int\limits_0^\infty d\omega\, n_\beta(\omega)\omega^5\frac{\omega_{an} }{\omega_{an}^2 - \omega^2}\\\times (a n | (\bm{r}_1 \bm{r}_2)(r_1^2 + r_2^2) | n a).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppqqwhlks cf9735127dc5ad16c1b5247a8a3bfd495cea5076873396de46e97bde18db0972.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 276:
公式 3127: \begin{eqnarray} \langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2)r^2_{12}  | n a \rangle =  \\  \langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2) (r^2_1+r^2_2-2(\bm{r}_1\bm{r}_2)) | n a \rangle. \,\,\,\,\,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7fgpnjud 1351dae894f7901423009fc3c96cdf0cc267c5a602171c99cbe7f98a3bd87db6.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 277:
公式 3155: \begin{eqnarray}    \frac{\sin \omega r_{12}}{\omega r_{12}} = \sum\limits_{L=0}^{\infty}(2L+1)j_{L}(\omega r_{1})j_{L}(\omega r_{2})    \\\times    \bm{C}_{L}(\bm{n}_1)\bm{C}_{L}(\bm{n}_2),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps_titaps 0e40f51968976192c7492e69bb41ce1a9f816919a5fbdfbaab05481c7952c086.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 278:
公式 3174: \bm{C}_{L} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmper28kfik b9a8227e2edfb4fa01fd14493dce53a9fbca33559dca2f60a977a6ab5e6cb91c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 279:
公式 3163: \begin{eqnarray}\Gamma_a^{\mathrm{ind}+\mathrm{FL}} = \frac{2e^2}{3\pi}\sum\limits_n |(a|\bm{r}|n)|^2 \int\limits_0^\infty d\omega\,\omega^3\,n_\beta(\omega)\times\\\left[\frac{\Gamma_{na}}{(E_{na}-\omega)^2+\frac{1}{4}\Gamma_{na}^2}+\frac{\Gamma_{na}}{(E_{na}+\omega)^2+\frac{1}{4}\Gamma_{na}^2}\right],\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpziyu1kmf defd441af52ec5c9469c940a5e1eb81fd47e3178406b22f36a84361083d7b3d4.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 280:
公式 3177: (\tilde{a}|\bm{r}|\tilde{n}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsm1bxeq4 56a56ef9b92a6f1e2e348b99d7e823c39d87701ee21fc619977b3485c8064712.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 281:
公式 3182: \begin{eqnarray}|(\tilde{a}|\bm{r}|\tilde{n})|^2\approx |(a|\bm{r}|n)|^2 - \frac{E_a+E_n}{2m_e c^2}|(a|\bm{r}|n)|^2.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuuaxgbpk 96db39113b23735bccc9adeedf20d82e75e98b449db429dcb621194bcf4eb14b.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 282:
公式 3197: \begin{eqnarray}\Delta E_a^{\rm rel} = \frac{e^2}{6\pi}\sum\limits_n \int\limits^{\infty}_0 d\omega\, \omega^3\,n_{\beta}(\omega) \langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2)(r_1^2+r_2^2) \\- 2([\bm{r}_1\times\bm{\alpha}_1]\cdot[\bm{r}_2\times\bm{\alpha}_2]) - 2(\bm{r}_1\bm{\alpha}_2)(\bm{r}_2\bm{\alpha}_1)| na \rangle \qquad\\\times\left( \frac{1}{\varepsilon_a - \varepsilon_n + \omega + \mathrm{i}\,0} +  \frac{1}{\varepsilon_a - \varepsilon_n - \omega + \mathrm{i}\, 0}   \right).\qquad\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1j7gz38m 1848671fed5443ae0d0968ca985794adf7a631027334073c1a651c67644bdc66.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 283:
公式 3196: \begin{eqnarray}\Delta E_a^{\rm dia} = \frac{\pi^3 e^2}{45\beta^4}\langle a | r^2| a\rangle,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpprtx3sbz c1a4d8b3aab5ecba9a912a6a8ef7fe8c772515135b831584b71cad0c6aa84f57.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 284:
公式 3198: \begin{eqnarray}W_{aa'}^{\mathrm{ind}}(\mathrm{M1}) = \frac{4}{3}n_\beta(|\omega_{aa'}|)\omega_{aa'}^3 |(  a| \bm{\mu} |a' ) |^2.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgvfie1qr b8e6cc1b74c5302f8a732aa8239fb81da5f33dae9eadb2077a84d18aa845c600.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 285:
公式 3203: r_{12}^2=r_1^2+r_2^2-2\bm{r}_1\bm{r}_2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvo_eod8n 67ef2a913fff22a7b6618b89b683211696acd347f092e52ab6a7258c3d52c7b6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 286:
公式 3213: \begin{eqnarray}  \Delta E_a^{\mathrm{}} = -\frac{2}{3\pi}\sum\limits_n ( a n|   (\bm{\mu}_1\bm{\mu}_2) |n a ) \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\,\omega^3   \\    \times  \sum\limits_{\pm} \frac{1}{E_a-E_n \pm \omega + i 0} .\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp08rci2w7 149f83ba10e7c746a03b3965ecca6c1200e448d6451262a8855bd9e24e42fb21.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 287:
公式 3215: \bm{A} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7crowpzy fe68fdbeaf23199e73018380a1374347e923a5a8e65c73c51541599153ee5659.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 288:
公式 3218: \begin{eqnarray}\Delta E_a^{\rm rel} \approx   \Delta E_a^{\mathcal{B}} + \Delta E_a^{\mathcal{Q}} + \frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \frac{2\omega_{an}}{\omega_{an}^2-\omega^2}( a n| (\bm{\alpha}_1 \bm{\alpha}_2)(r_1^2+r_2^2) | n a) - \qquad\\\frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega)\omega_{an} (a n|(\bm{r}_1 \bm{r}_2)^2|n a) + \frac{e^2}{60\pi}\sum\limits_n\int\limits_0^\infty d\omega\,  \omega^5 n_\beta(\omega)\frac{\omega_{an}}{\omega_{an}^2-\omega^2}(a n|r_1^4+r_2^4-4(\bm{r}_1 \bm{r}_2)(r_1^2+r_2^2)|n a)\\=\Delta E_a^{\mathcal{B}} + \Delta E_a^{\mathcal{Q}} +\Delta E_a^{\rm rem}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprsnzketx 7a8e17c7f59ca37c5e40fd06bdba3b52a1b89d77e23fc8917fa0bdbdf7537816.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 289:
公式 3229: \begin{eqnarray}\langle a| \alpha_{1i}r_{1j} + \alpha_{1j}r_{1i}|n\rangle \approx \\\frac{1}{m_e}(a| r_{1i}p_{1j}+r_{1j}p_{1i}-\mathrm{i}\delta_{ij}|n),\\\langle n| \alpha_{1i}r_{1j} + \alpha_{1j}r_{1i}|a\rangle \approx \\\frac{1}{m_e}(n| r_{2i}p_{2j}+r_{2j}p_{2i}-\mathrm{i}\delta_{ij}|a).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgpxoq7lc 457961ec606a84d4d54a7008f41da5599af5d74088baa8854e3ed64902865ebd.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 290:
公式 3224: \bm{r}V - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpmjjx52n7 335ce3260e76f3636583943e65c74482c68f81ca93b19a19175ca98e0a29e623.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 291:
公式 3239: \begin{eqnarray} I^{\beta}_{na}(r_{12}) = \mathrm{P.V.}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega) \sum\limits_{\pm}\frac{\sin\omega r_{12}}{\varepsilon_a-\varepsilon_n \pm \omega} +  \\   + \mathrm{i} \pi \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega) \sin(\omega r_{12}) \sum\limits_{\pm} \delta(\varepsilon_n -\varepsilon_a \pm \omega).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu5pserxh a6d6f4ee3fd5fef2f5b83da7189d1a9d6fdd23a24d2da8a84a90c90b8b6f0ea6.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 292:
公式 3240: \begin{eqnarray}   \mathcal{E}^2(\omega) = \mathcal{B}^2(\omega) =  \frac{8}{\pi}\frac{\omega^3}{e^{\beta \omega}-1},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpva5642_2 ef30944105d515bd55730592f058c8c917a306c2836a6243f36e558d2a65978d.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 293:
公式 3247: \bm{\mu}^{\mathrm{nr}} = \mu_{\rm B}(\bm{l}+2\bm{s}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp85m9993w 0d27721bfe03c5e702c08173d1e6404d917042395b82355a382b3afe5318e78b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 294:
公式 3257: \bm{\mathcal{E}}(t) = \bm{\mathcal{E}}_0 \cos(\omega t) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpalp0phwc e7ac6fc1b577c0962fede36d6b9ee5c577ed8fa2d68c73c777714ab1a8b6775a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 295:
公式 3251: \begin{eqnarray}\langle a n| (\bm{\alpha}_1 \bm{\alpha}_2) (\bm{r}_1 \bm{r}_2) + (\bm{r}_1 \bm{\alpha}_2) (\bm{r}_2 \bm{\alpha}_1) | n a \rangle =\langle a n| \alpha_{1i}\alpha_{2i} r_{1j}r_{2j} + \alpha_{1i}\alpha_{2j} r_{1j}r_{2i} | n a \rangle\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf8u784vq e1e0b65c9a86e873c9e888adf04f57c6397ad2ffb23b3ff0bb2ec9cf2278aff7.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 296:
公式 3262: r_{12}\equiv|\bm{r}_1-\bm{r}_2| - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpt_heuthk 4cbf3254ea72ce52108bb3d77bf510e474d3e46143dec0439700d457dbb123f3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 297:
公式 3261: \begin{eqnarray}\Delta E_a^{\rm rem} =  \frac{e^2}{6\pi}\sum\limits_{n}\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \omega_{an} \\\times( a n | 4 (\bm{r}_1 \bm{r}_2) r_1^2 - (\bm{r}_1 \bm{r}_2)^2| n a).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpaqkk3go4 12a0d694d0b5af404897862e569c8077a38a9f2506efe9a8369aaacb167a0a41.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 298:
公式 3272: \begin{eqnarray}D_{\mu_1 \mu_2}(x_1,x_2) = \frac{g_{\mu_1 \mu_2}}{2\pi \mathrm{i} r_{12}}\int\limits_{-\infty}^{+\infty}d\omega e^{\mathrm{i}|\omega|r_{12} - \mathrm{i}\omega(t_1-t_2)} \\- \frac{g_{\mu_1 \mu_2}}{\pi r_{12}}\int\limits_{-\infty}^{+\infty}d\omega\, n_{\beta}(|\omega|) \sin{|\omega|r_{12}} \, e^{-\mathrm{i}\omega(t_1-t_2)}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpojv75jfl 00aaa90bbf1188785ad550c3fa843858b94be61f45d182c3c38fff7781740925.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 299:
公式 3271: \bm{\mu} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6fy8jqoc 93831f2272d50ecab1289db3db32a6280cdc56fafdfcb35aa7fdfd03110f9d3d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 300:
公式 3274: \begin{eqnarray}\Delta E_a^{\rm rel-} = \text{first} -\frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega)\omega_{an} (a n|(\bm{r}_1 \bm{r}_2)^2|n a) + \\-\frac{e^2}{6\pi}\frac{3}{5}\sum\limits_n\int\limits_0^\infty d\omega\, n_\beta(\omega)\frac{\omega_{an}\omega^5}{\omega_{an}^2-\omega^2}( a n| (\bm{r}_1\bm{r}_2)^2 |n a) +\frac{e^2}{6\pi}\frac{1}{5}\sum\limits_n\int\limits_0^\infty d\omega\, n_\beta(\omega)\frac{\omega_{an}\omega^5}{\omega_{an}^2-\omega^2}(a n| r_1^2r_2^2 |n a)\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0wwy1wk3 5d65a9b8bdcd2530f7f51b42cb33a12da2c645d1ce9424bb2b89bd9e466a8849.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 301:
公式 3278: \bm{l} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyhc1kh6n fe2f7c92e7ec3228329f0cab00811adecceaa1230f251b8668a10e816ec4ea8d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 302:
公式 3287: \bm{r} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0ixflt1c c260fdf16976f1a9775fedb1d6180b225da94db311d745499f77077482b65659.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 303:
公式 3288: \begin{eqnarray}  \Delta E^{\rm BBRZ}_a = -\frac{4}{3\pi}  |(  a| \bm{\mu} |a' ) |^2  \int\limits^{\infty}_0 d\omega  \frac{n_{\beta}(\omega)\omega^3 \omega_{aa'}}{\omega_{aa'}^2-\omega^2}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjg8s858x 2344b6c3777a4440bbe7e7037f9eaf352b8e3fad341d9ab7222ef9fc3ef2c0af.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 304:
公式 3292: \begin{eqnarray}\Gamma_a =  -2{\rm Im} \Delta E_a\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4i4zx7r4 3c0ab83e954e6ad77aa62b56244b2cec6edd8ae4a2bb96d2e1b5d2746d6b78eb.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 305:
公式 3310: \begin{eqnarray}    \Delta E_{a}  = \sum_{\substack{i,\,n \ne a}}\sum\limits_{\pm} \frac{e^2 \mathcal{E}_{0i}^2}{4} \frac{|( a|r_{i}| n )|^2}{E_a-E_n \pm \omega},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7e99vxka 93ee37c623fbe4bb82c2158b1d352f830a29c9ae5f512e2419dd4521d1091071.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 306:
公式 3316: \begin{eqnarray}\Delta E_a^{\rm wf} = -\frac{2e^2}{3 \pi} \sum_{n} | ( a |\bm{r} | n )|^2 \\\times\int\limits^{\infty}_0 d\omega\,\omega^3 n_{\beta}(\omega) \frac{E_{n}^2-E_a^2}{\omega^2_{na} - \omega^2}.\qquad\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfk6594jl a063a514daa4e349cd57a5009d5c80fd2ca46540e9ef26479f0bfdd057daafef.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 307:
公式 3325: \begin{eqnarray} \Delta E_a^{(-)}\approx \frac{e^2}{\pi m_e c^2}\sum_{n^{(-)}}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\omega\, \delta_{a n^{(-)}} - \qquad\\\frac{e^2}{\pi m_e c^2}\sum_{n^{(-)}}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\langle an^{(-)}|\omega \bm{\alpha}_1\bm{\alpha}_2+ \frac{\omega^3}{3}\bm{r}_1\bm{r}_2|n^{(-)} a\rangle \\-\frac{e^2}{3\pi m_e c^2}\sum_{n^{(-)}}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\omega^3\langle a|\bm{r}^2|n^{(-)} \rangle\, \delta_{a n^{(-)}}+\dots\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0jr3vdxe f49ce473ebc4ddfe9708f3e4ee26d7131180511ed33029dfa4c430320f37fdce.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 308:
公式 3322: \bm{s}=\bm{\sigma}/2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0jiga3f5 a4479a738ba76b51368f853ed5d7fb8e5abdd914f5c328761a0e10ed3fcf6bba.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 309:
公式 3327: \bm{n}=\bm{r}/|\bm{r}| - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps3lmjtev 646b079556ee1b9e53c857601fbdf0a9be0dede286e54039a6d9d3fee798e940.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 310:
公式 3339: \bm{j}=\bm{l}+\bm{s} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphapb6wk6 cab83aff3820cb8fe0b0f4b672f74e372527bb8f28fc1cc19a7aa438896fcfd7.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 311:
公式 3348: \begin{eqnarray}J_{3} = \frac{2e^2}{3\pi} \sum\limits_{nk} \int\limits_0^\infty d\omega\,     \omega^3 n_\beta(\omega) \omega_{ak}     ( a | r_i | k) ( k | r^2 | n) ( n | r_i | a) \\     + \frac{2e^2}{3\pi} \sum\limits_{nk} \int\limits_0^\infty d\omega\,     \frac{\omega_{ak} \omega^5 n_\beta(\omega) }{\omega_{an}^2 - \omega^2} ( a | r_i | k) ( k | r^2 | n) ( n | r_i | a).\,\,\,\,\,\,\,    \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpltqnom3k b1a48c44a81efb00fad3e955b1c889dee40202a7809a385408525fa6799cac85.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 312:
公式 3351: \begin{eqnarray} \Delta E_a^{(-)}\approx - \frac{e^2}{3\pi}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\omega^3\langle a|r^2| a\rangle\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo78py5pq 744acd21fe8231da4dd525624830133cd9fd29548adc61a3926b163d98a79dd0.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 313:
公式 3352: \bm{s} = \bm{\sigma}/2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpl89lpxi9 75dc4d9f9de80a05617d6057645a031e4fbe14852f8cd62d0953357949b0849b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 314:
公式 3355: \bm{r}V\sim \bm{n} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdofhd0te fb890ea76cdccb78c382d56a39b1f88e125b670822ccab635e5211cb4849c65a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 315:
公式 3363: \begin{eqnarray}   \bm{\mu}= - \mu_{B} (\bm{l}+2\bm{s}),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpaiz2jl50 169405fddefa6275e958afdd73393e9bd5b1945d7ba01d1b83c5abb5c7ea3a5d.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 316:
公式 3366: \begin{eqnarray}\Delta E_a^{\mathcal{Q}} = - \frac{e^2}{90\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \frac{\omega_{an} \omega^5 n_\beta}{\omega_{an}^2-\omega^2}(an| \mathcal{Q}^{(1)}_{ij} \mathcal{Q}^{(2)}_{ij} |na)\,\,\,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprpqwpddu 869a7fb72c1fa403f1788ce272e523e148199871b587efb8797b5db1be58cf10.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 317:
公式 3372: \begin{eqnarray}([\bm{r}_1\times\bm{\alpha}_1]\cdot[\bm{r}_2\times\bm{\alpha}_2]) =  \\(\bm{\alpha}_1 \bm{\alpha}_2) (\bm{r}_1\bm{r}_2) - (\bm{r}_1\bm{\alpha}_2)(\bm{r}_2\bm{\alpha}_1),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpiwj78n_z 580aa976019bc61dc84158d5c84c48abc438947edf6b7a2d7a8f396ecbb6b094.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 318:
公式 3374: \begin{eqnarray} \Delta E_a^{(-)}\approx - \frac{e^2}{\pi m_e c^2}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\omega\,\langle a| \bm{\alpha}^2+ \frac{\omega^2}{3}\bm{r}^2| a\rangle\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp73zfrzh2 62ce364a64adc23f8a72034825254de72de907d4a4f7c258fc370c3f9605c837.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 319:
公式 3377: \begin{eqnarray}\Delta E_a^{\rm dia} = \frac{\pi^3 e^2}{45\beta^4}\langle a | r^2| a\rangle.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwhg1iwdh 6e43ccdb5acd345c2874552f193fcc694efeb9e209b9b07a04bf689656deb446.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 320:
公式 3384: \bm{\pi}= \bm{p}+\frac{e}{c}\bm{A} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpx7eytyq1 a7e458d6d89c61d2e7c3f7c75f55f3f79d5622228591e6f6afffd5794fb00a06.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 321:
公式 3387: \begin{eqnarray}J_{3}=\frac{2e^2}{3\pi}\sum\limits_{nk}\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \frac{\omega_{an}^2\omega_{ak}}{\omega_{an}^2-\omega^2}\\\times( a | r_i| k) ( k | r^2 | n) ( n | r_i | a),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp18bfrsql c88e2374e0fcfa976caecc42bd0b15f805958804e976a89ee4c0eba80132ffdc.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 322:
公式 3409: \mathcal{B}^2 \bm{r}^2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqrb56s_p dba3be5900bd0eabfeb68d3c1f9892e32b650134ebd09b451dc507e518fcbc41.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 323:
公式 3414: e\bm{r} \bm{\mathcal{E}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpflzu2eoq fe58cb76fb3d39d0b82e245021b2e70146682a3561315d1cfd867b9deb0472f5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 324:
公式 3413: \begin{eqnarray}  \Delta E^{\rm BBRZr}_a = -\frac{2}{3\pi}  |( a| \bm{\mu} |a' ) |^2  \int\limits^{\infty}_0 d\omega\,\omega^3\,n_{\beta}(\omega) \times\\\left( \frac{\omega_{aa'}+\omega}{(\omega_{aa'}+\omega)^2+\frac{1}{4}\Gamma^2} + \frac{\omega_{aa'}-\omega}{(\omega_{aa'}-\omega)^2+\frac{1}{4}\Gamma^2}\right),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0ui8x4w4 28a883a696834e3a654419a00cdc68cdc5d17109a855c9b13be1fc12eadb0310.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 325:
公式 3420: \bm{\alpha} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpygwg754v c83a6f3a8135a5f1061b4ee6e59571255a5dd1a9335ff6b6d5abd5714be7a6dd.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 326:
公式 3419: \begin{eqnarray}\langle  a| \mu_i  | n \rangle \approx \frac{|e|}{2m_e}( a|[\bm{r}\times \bm{p}]_i + \sigma_i|n),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpoyk42hzm 96796a2c011ae8cde0e57dd4d92c19b109dfbefae7c0d72e62f582cabe6acc4a.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 327:
公式 3424: (3r_{1i} r_{1j}-r^2_1\delta_{ij})(3r_{2i} r_{2j}-r^2_1\delta_{ij}) \equiv \mathcal{Q}^{(1)}_{ij} \mathcal{Q}^{(2)}_{ij} = 9(\bm{r}_1\bm{r}_2)^2-3r_1^2r_2^2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg7pqns95 cf9579b30cfe55f8b97600bee150947c9fb78d1db70b18816a6af5ac98bad456.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 328:
公式 3431: r^2_{12}=r^2_1+r^2_2-2\bm{r}_1\bm{r}_2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_7gb61iw b9af82110fa8ead56f1986e9b24c735335c0865f2b1a6b2a7b349d1998ef318c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 329:
公式 3433: [\bm{r}\times\bm{\alpha}]_i - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxyva365u 4cd9284596fd007d502daa03bd14150ce32c35ff1fd070bda83170d520873c67.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 330:
公式 3437: \begin{eqnarray}\begin{pmatrix}\varphi_a^* & \chi_a^*\end{pmatrix} \begin{pmatrix}0 & r_{j}\sigma_{k}\\r_{j}\sigma_{k} & 0\end{pmatrix} \begin{pmatrix}\varphi_n \\ \chi_n\end{pmatrix} \approx \qquad\\\frac{1}{2m_e}  \varphi_a^* \left( r_jp_k + ir_j[\bm{p} \times \bm{\sigma}]_k + p_k r_j + i[\bm{\sigma} \times \bm{p}]_k r_j     \right)\varphi_n.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkjaln5pb 74e65ea93c98f9662007ef668f8be7fa0c6665e9ee658ca84a8e07a56c456816.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 331:
公式 3441: \begin{eqnarray}\Delta E_a^{\rm rem} =   \frac{e^2}{3\pi}\frac{\pi^4(k_{\rm B} T)^4}{15}( a |  r^2(\bm{r}\cdot \bm{\nabla})| a),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7ggsh7t1 41b7a74313a819a4f46decaffb0ce3efb463ba2aff487ffeebee20129e18edb3.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 332:
公式 3442: \begin{eqnarray}\Delta E_a^{\mathrm{BBRZ}} = -\frac{2}{3\pi}\sum\limits_n\langle a n|   (\bm{\mu}_1\bm{\mu}_2) |n a \rangle \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\,\omega^3 \,\,\,\,\  \\    \times  \left( \frac{1}{\varepsilon_a-\varepsilon_n + \omega + i 0} +  \frac{1}{\varepsilon_a-\varepsilon_n - \omega + i 0}   \right),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdgyiksby 45db8a16d8c131718b444fd6bf8c679ef743818dd945b3d09b7c1d5fde4df8b8.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 333:
公式 3449: \bm{\alpha}_i\bm{\alpha}_j = \delta_{ij}+\mathrm{i}\varepsilon_{ijk}\bm{\Sigma}_k - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzb8d73a4 ccb6104ab471f5adb6ec9821ba1115838cfb9efb230b1b42dca0a24161f84f93.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 334:
公式 3470: \begin{eqnarray}\Delta E_a = \frac{4e^2}{3 \pi} \sum_{n} | ( a |\bm{r} | n )|^2 \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega) \frac{\omega_{na}\omega^3}{\omega^2_{na} - \omega^2}  \\- \frac{2  e^2}{3} \mathrm{i} \sum_{n } n_{\beta}(|\omega_{an}|)\,|\omega_{an}|^3\, | ( a |\bm{r} | n )|^2,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzu76s82y 07cdcf0cc64a8b61250e858903bdf652e8d7dc1bf255007cd03129ea02dfcd29.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 335:
公式 3477: x=(t,\bm{r}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdhtoyaez 2f45a2029748976e223d2c101d03e0d92036e5300241e598ddbd9326360a795a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 336:
公式 3479: \chi\approx \frac{(\bm{\sigma} \bm{p})}{2m_e}\varphi - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf5enw1br e6bf9b26d09c2668f2e57c79863eb92dc933f6e1a3afcc0066151f7dfc8c16db.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 337:
公式 3487: \begin{eqnarray}\Delta E_a^{\rm rel}= \frac{e^2 }{6 \pi} \sum\limits_n \int\limits^{\infty}_0 d\omega\, \omega^3\,n_{\beta}(\omega)  \langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2) r^2_{12}   | n a \rangle \,\,\,  \\  \times\left( \frac{1}{\varepsilon_a-\varepsilon_n + \omega + \mathrm{i}\, 0} +  \frac{1}{\varepsilon_a-\varepsilon_n - \omega + \mathrm{i}\,0}   \right).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9gnxzicg 40ff476d236fe2bc193f9fd4b51ce379507391698b9b3c3ba6e82d7e301678fe.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 338:
公式 3483: \begin{eqnarray}\Delta E_a^{\rm rel} = -\frac{2}{3\pi}\sum\limits_{n} \int\limits^{\infty}_0 d\omega\, \omega^3\,n_{\beta}(\omega) \Big[ \langle  a n| (\bm{\mu}_1 \bm{\mu}_2)| na \rangle \\-\frac{e^2}{4}\langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2)(r_1^2+r_2^2) - (\bm{\alpha}_1\bm{\alpha}_2)(\bm{r}_1\bm{r}_1) | na \rangle + \qquad\\ \frac{e^2}{4}\langle  a n| (\bm{r}_1\bm{\alpha}_2)(\bm{r}_2\bm{\alpha}_1)| na \rangle  \Big]\sum\limits_{\pm}\frac{1}{\varepsilon_a-\varepsilon_n \pm \omega + \mathrm{i}\,0},\qquad\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp50f84etz a5ebbbda0eff894878cf94a20d4ba38ebd9bdfeb7fce86beff9c4ac0fe772283.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 339:
公式 3492: \begin{eqnarray}I^{\beta}_{na}(r_{12}) = \int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega) \sum\limits_{\pm}\frac{\sin\omega r_{12}}{\varepsilon_a-\varepsilon_n \pm \omega + \mathrm{i}\,0}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpanu4fb8d 38eca824ed5076ed1f184ee709602d5ab05ef7d0a9bc616321a066ffb49cc65a.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 340:
公式 3493: \bm{a} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpq9ug9oi6 b2dd0e3f9fd024e9098db64d2fb8dbef61b19e85187a5bd698a2d6d670f62908.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 341:
公式 3502: \begin{eqnarray}[r_i,p_j] &=& \mathrm{i} \delta_{ij},\\(\bm{\sigma}\bm{a})\bm{\sigma}  &=& \bm{a} + \mathrm{i} [\bm{\sigma} \times \bm{a}],\\\bm{\sigma}(\bm{\sigma}\bm{a})  &=& \bm{a} + \mathrm{i} [\bm{a} \times  \bm{\sigma}].\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpnjw9r9v5 18b23df7b8e64c8efdc12fa9d9565f086385eb888e26a77284ffdc40f57dd1de.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 342:
公式 3515: \begin{eqnarray}    \hat{H}= \hat{H}_0 + \hat{V},\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2ba2vdpd 7bc52d90286a4422e9770fc68552ce5e08ade28606c47d5f5d36ac9e914989c0.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 343:
公式 3514: \begin{eqnarray}\Delta E_a^{\rm rel-} = \frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \frac{2\varepsilon_{an}}{\varepsilon_{an}^2-\omega^2}\langle a n| (\bm{\alpha}_1 \bm{\alpha}_2)(r_1^2+r_2^2) + \frac{\omega^2}{20}(r_1^4+r_2^4-4(\bm{r}_1 \bm{r}_2)(r_1^2+r_2^2)) | n a\rangle  -\qquad\\\frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega)\frac{\omega_{an}^3}{\omega_{an}^2-\omega^2}(a|r_{1i}r_{1j}|n)(n|r_{2i}r_{2j}|a) + \frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega)\frac{2\varepsilon_{an}}{\varepsilon_{an}^2-\omega^2}\langle a n| \frac{\omega^2}{5}(\bm{r}_1\bm{r}_2)^2+\frac{\omega^2}{10}r_1^2r_2^2 |n a\rangle\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbdx24toy ee37a6682728ea966e7d3f96dc52265a0712fc87ac8f6c64fe73c466a7bfcbd6.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 344:
公式 3523: \begin{eqnarray}\frac{\pi^3 e^2}{45\beta^4}\rightarrow 7.6425\times 10^{-8} \left[\frac{T}{300\, \text{K}}\right]^4 \text{ Hz.}\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7taiodye 32656f84b05ba9526770d51435d76db1baa3ef51e10594d8d6a7f8d9b665f91f.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 345:
公式 3521: \begin{eqnarray}\langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2)r^2_{12}  | n a \rangle = \\-\langle  a n| ([\bm{r}_1\times\bm{\alpha}_1]\cdot[\bm{r}_2\times\bm{\alpha}_2]) | n a \rangle +\,\,\,\,\,\\+\langle  a n| (\bm{\alpha}_1 \bm{\alpha}_2) (r^2_1+r^2_2) | n a \rangle\\- \langle  a n| (\bm{r}_1 \bm{\alpha}_2) (\bm{r}_2 \bm{\alpha}_1) - (\bm{\alpha}_1 \bm{\alpha}_2) (\bm{r}_1 \bm{r}_2) | n a \rangle.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm7ee6r2o 91aadc0c182256042cce21b4b00d76e73ec9a4d45e19cca49fa9913a7182d4c0.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 346:
公式 3524: \begin{eqnarray}I^{\beta}_{n^{(-)} a}(r_{12}) \approx \frac{1}{m_e c^2}\int\limits^{\infty}_0 d\omega\, n_{\beta}(\omega)\left[\omega r_{12}-\frac{\omega^3}{6}r_{12}^3+\dots\right],\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvd1af387 6acbcf6c74d28b69595d1b038a58414650a85f71a2bdef0400e268d194c50a55.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 347:
公式 3527: r^2_{12}=r^2_1+r^2_2-2(\bm{r}_1\bm{ r}_2) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphxpuh2rl 878728ffd255633c6161494ba49440d3efbea0d9a7d79d213ec450351c58ef3b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 348:
公式 3530: \begin{eqnarray}    \Delta E_{a}^{\mathrm{BBRS}}= \frac{2e^2}{3\pi} \text{P.V.}\int\limits_{0}^{\infty} \frac{d\omega\,\omega ^3}{ e^{\beta \omega}-1 }    \sum\limits_{n \neq a}   \sum\limits_{\pm}    \frac{\left |  ( a|  \bm{r} |n )  \right|^2}{E_a-E_n \pm \omega}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgdxzbn42 2b0fe88c545aa11c700bb5d80858c63e48c7aa87ebf8fb6cad7863b3bb8661e1.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 349:
公式 3539: \begin{eqnarray}J_{3}=\frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \frac{2\varepsilon_{an}}{\varepsilon_{an}^2-\omega^2}\\\times\langle a n| (\bm{\alpha}_1 \bm{\alpha}_2)(r_1^2+r_2^2) | n a\rangle \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps0l810kj ef5f3300a230a61a284215b117c459a8c7e9e21c37a673f07537e3c4dff5e113.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 350:
公式 3544: (\bm{\alpha}_1 \bm{\alpha}_2) (\bm{r}_1\bm{r}_2) = ([\bm{r}_1\times\bm{\alpha}_1]\cdot[\bm{r}_2\times\bm{\alpha}_2]) + (\bm{r}_1\bm{\alpha}_2)(\bm{r}_2\bm{\alpha}_1) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpksxq3591 8a8a905445bb9b545a3509732af19d891a7f2076024a5cfed3de8de32761f2b5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 351:
公式 3546: \begin{eqnarray}\bm{\mu} = \frac{e}{2}[\bm{r}\times\bm{\alpha}].\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfjqj7opa fca916d86867fa14161de8af05a18f70ddf6a6ae266818db7637c75efe4d9660.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 352:
公式 3547: \begin{eqnarray}\frac{1}{2}\langle a n| \alpha_{1i}\alpha_{2i} r_{1j}r_{2j} + \alpha_{1i}\alpha_{2j} r_{1j}r_{2i} | n a \rangle + \frac{1}{2}\langle a n| \alpha_{1j}\alpha_{2j} r_{1i}r_{2i} + \alpha_{1j}\alpha_{2i} r_{1i}r_{2j} | n a \rangle = \\\frac{1}{2}\langle an | (\alpha_{1i}r_{1j}+\alpha_{1j}r_{1i})(\alpha_{2i}r_{2j}+\alpha_{2j}r_{2i}) | n a\rangle\equiv\frac{1}{2}\langle a| \alpha_{1i}r_{1j}+\alpha_{1j}r_{1i}| n\rangle\langle n|\alpha_{2i}r_{2j}+\alpha_{2j}r_{2i}| a\rangle.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuv6c5ngp a39dd0ba0414e800afa31bfe6fa5108315c9b3d24d1956ebf3ce4d8c4db62a71.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 353:
公式 3548: \bm{\mathcal{E}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpthh_hyx3 d89ea7cf29b66eb2764b9074e88eea42112eaff9a222dfdc3961ea4fc1e213d4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 354:
公式 3554: \begin{eqnarray}\Delta E_a^{\rm rem} =  \frac{e^2}{6\pi}\sum\limits_{n}\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega) \omega_{an} ( a n | 2 (\bm{r}_1 \bm{r}_2) (r_1^2+r_2^2) | n a) - \frac{e^2}{6\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^3 n_\beta(\omega)\omega_{an} (a n|(\bm{r}_1 \bm{r}_2)^2|n a) +\qquad\\\frac{2e^2}{3\pi}\sum\limits_{nk}\int\limits_0^\infty d\omega\, \omega^5 n_\beta(\omega) \frac{\omega_{ak}}{\omega_{an}^2-\omega^2} ( a | r_i| k) ( k | r^2 | n) ( n | r_i | a) - \frac{e^2}{15\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \omega^5n_\beta(\omega)\frac{\omega_{an} }{\omega_{an}^2-\omega^2}(a n|(\bm{r}_1 \bm{r}_2)(r_1^2+r_2^2)|n a).\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpq_p6z45q e993da03dd8619409f713634e340f8596e8e8a718dd2d85da1290065af6cd1d9.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 355:
公式 3570: \bm{\mu} = e[\bm{r}\times\bm{\alpha}]/2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprx_6ewtu 8c94f781e9cd0670f755ececacdcbc35ed6969de3924f81a515a73ecb7741d3c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 356:
公式 3582: \begin{eqnarray}   \langle f |\hat{S}^{(2)}| i \rangle= (-ie)^2 \int d^4 x_1 d^4 x_2 \overline{\psi}_f(x_1) \gamma^{\mu} S(x_1,x_2)\times\\       D_{\mu \nu}(x_1,x_2)\gamma^{\nu} \psi_i(x_2),\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0umsyg2u 7ab810c9313ac4249e62a36b787679cd5e4b9525646789ab40a3745a321b7f5c.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 357:
公式 3595: \begin{eqnarray}\langle  a| \mu_i  | n \rangle = \frac{|e|}{2}\epsilon_{ijk}\langle  a|r_j\alpha_k  | n \rangle \rightarrow\\\begin{pmatrix}\varphi_a^* & \chi_a^*\end{pmatrix} \begin{pmatrix}0 & r_{j}\sigma_{k}\\r_{j}\sigma_{k} & 0\end{pmatrix} \begin{pmatrix}\varphi_n \\ \chi_n\end{pmatrix} \approx \\\varphi_a^* \left( r_{j}\sigma_{k} \frac{(\bm{\sigma} \bm{p})}{2m_e} + \frac{(\bm{\sigma} \bm{p})}{2m_e} r_{j}\sigma_{k}    \right) \varphi_n,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9xq36w3_ df99496ce79ee3cc0c3aabf870563bf4434a251a0356310f14b6d4ddfdaec1b3.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 358:
公式 3590: \langle a| (\bm{l} + 2 \bm{s}) |n \rangle - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0opb5uo3 3f4d603bdda459984f3b3f245abf202dcd1d51bf9a17d7e0cf56054011154ed3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 359:
公式 3599: \begin{eqnarray} \Delta E_a = \frac{e^2}{\pi} \sum_n \left[  \frac{1-\bm{\alpha}_1\bm{\alpha}_2}{r_{12}} I^{\beta}_{na}(r_{12})\right]_{anna}.\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpni0ctqpq 88592404e8ad5cd24518c3ece9423b1bdcb88f3d5a05d73fba98a6bb75413878.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 360:
公式 3611: \begin{eqnarray}\Delta E_a^{\mathcal{Q}} = - \frac{e^2}{90\pi}\sum\limits_n\int\limits_0^\infty d\omega\, \frac{\omega_{an} \omega^5 n_\beta}{\omega_{an}^2-\omega^2}(an| \mathcal{Q}^{(1)}_{ij} \mathcal{Q}^{(2)}_{ij} |na).\,\,\,\,\,\,\,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0nj8nlvi e9a07ffdb2d106727094a97d22c43c70fe7b0c4a027a503d48fffbe7f6e57c81.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 361:
公式 3635: \begin{eqnarray}        S(x_1,x_2)=\frac{i}{2\pi}\int\limits_{-\infty}^{+\infty}d\Omega\, e^{-i\Omega(t_1-t_2)}            \sum\limits_{n}\frac{\psi_{n}(\bm{r}_1)\overline{\psi}_{n}(\bm{r}_2)}{\Omega - \varepsilon_{n} (1-i0)}\,,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp13xuc_ig f38116c5ccf5af9fe89df54850910f20fe6823bafd3062aaf685b7a63a2d78ab.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 362:
公式 3634: \begin{eqnarray}|(\tilde{a}|\bm{r}|\tilde{n})|^2\approx \left|(a|\bm{r}|n) - \frac{1}{8m_e^2c^2}(a|p^2\bm{r}+\bm{r}p^2|n)\right|^2,\end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg1a9eyzj 3b7f3cdf94fb6c6fc615a8bb5f05054aff36594a4986251fc6d9890059d363ff.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 363:
公式 3670: \begin{align}{\delta} &= \frac{\lambda}{2 \pi} \sin ^{ -1 }{ \frac { \sqrt { { ( { I }_{ 3 }-{ I }_{ 1 } ) }^{ 2 }+{ ( { I }_{ 2 }{-I }_{ 4 } ) }^{ 2 } } }{ ({ I }_{ 1 }+{ I }_{ 2 }+{ I }_{ 3 }+{ I }_{ 4 } )/2} },\\[6pt]\varphi &=\frac{1}{2}\tan ^{ -1 }{ \frac { { I }_{ 3 }-{ I }_{ 1 } }{{ I }_{ 2 }-{ I }_{ 4 }}},\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp345pe0t6 74c13b062440ec4c4120467b7f4a798818c63a182c0fae01cac7ef92d22477d5.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 364:
公式 3692: \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv3jzi59_ 72a42d5635c6e2bf6798eda9b6b90328529d6ae0fb0e7b15b54d59d10a9e60b4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 365:
公式 3718: \bm{n} \neq C \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpy2as1lul 068896abf15d8f42af74a8e0470623bc86f52dac2d75c9670f35a778055a82ff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 366:
公式 3751: \bm{n} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqfi42kp3 0850f86756af672298034ef4267a5c9aae8b16cef2a2c9008807ae86a5ed9c55.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 367:
公式 3794: \bm{n} = C \bm{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4ge9s06j e3acf93618aa02ddc260fd6429a96a1737334eaabd5e0fbccc77b63c47dc55b5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 368:
公式 3863: \hat{H}=\hat{H}_{0}-t\hat{E}_{\mr{L},1}+k\hat{\mb{S}}_{\mr{L}}\cdot\hat{\mb{S}}_{12}+j\hat{\mb{S}}_{12}\cdot\hat{\mb{S}}_{34}+\hat{H}_{\mr{iR}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1hyyge9n 55e63ff4fb7b2665d15d9d6d7e8dd302fb5c960c35a5dc55732677986888da6d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 369:
公式 3874: \hat{E}_{\mr{L},1}=\hat{a}^{\dagger}_{\mr{1}}\hat{a}_{\mr{L}}+\hat{a}^{\dagger}_{\mr{L}}\hat{a}_{1} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp3whd8uct 54c20006c99c4c6ec87aa8f446e386023a48988e8dbb5165bb2717b14c80828b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 370:
公式 3871: M_{\mr{cl}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzb2yuvpc efca465d682a50e9fb0dde2b351bd09e427b5253bda71d9074001ea68bcbfe4b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 371:
公式 3875: S_{\mr{L}}=1/2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpnoadvq4y 3d13eae3d71cd2789e62aef23789156647794b00b3c18a33f8d1b93105cb60c2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 372:
公式 3872: \mathbf{T}^{\mr{dip}}_n - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpnpqd6jqo c360266eefb105f8bed86c3b61555bace37e23a2427a74c59e6c635171698013.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 373:
公式 3878: \begin{split}|S_{12},M_{12}\rangle&=\sum_{M_1,M_2}d_{\mr{UR}}(M_1,M_2)|S_1,M_1\rangle_{\mr{Fe1}}|S_2,M_2\rangle_{\mr{Fe2}}~,\\|S_{34},M_{34}\rangle&=\sum_{M_3,M_4}d_{\mr{LR}}(M_3,M_4)|S_3,M_3\rangle_{\mr{Fe3}}|S_4,M_4\rangle_{\mr{Fe4}}~.\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppzcs5j2e dbeba6ee5aa87d84862625b661f1f0ed758cfe84f66649588278176e2e7974e8.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 374:
公式 3880: \hat{\mb{S}}_{12} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzpaslxth 140e29d5f1488b997153e1f07f20a5217e6362870d14ffd80d6cc38833202ccb.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 375:
公式 3890: \hat{H}_{\mr{HDVV}}=\sum_{i>j}J_{ij}\hat{\mb{S}}_i\cdot\hat{\mb{S}}_j - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2e1d_nly 56aec566b0dae27eb18cd94487e9c8a992f6319d1cd332706d93ee5f62d4d4b1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 376:
公式 3888: \hat{E}_{\mr{L},1} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuhxx6y7q 28dd173a373b515c56ee92d4484fba022096aaa336eaadca3a585f7250b98840.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 377:
公式 3897: p_{\mr{ET}}=|C_0|^2+|C_1|^2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjlalzg86 108e4209daae5c3ab6821bb220362a9d9a11bb6664d0b734684ba25e25dcd411.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 378:
公式 3899: \mathbf{H}=\mathbf{H}_{\mr{diag}}+\bm{\Delta} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpk39v9sjs 35c515d0f21d4e12f7361fa558bd01cfba67934d4d02d20b7e35477d903edc12.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 379:
公式 3904: S_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyse9ri6i c4f12a4c14fd8354968a3486875ce7e3cb766dc707f285660fdc60e5d2eb04f3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 380:
公式 3915: Q_{\mr{cl}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpauv5jvd_ bf6035d6bf714774bf455ee1048c37017200fcc98e642bff001e55d5230f5c44.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 381:
公式 3910: \langle \hat{S}_{\mr{L},z}\rangle = p_{\mr{ET}}[\alpha\langle \mathrm{SC}0|\hat{S}_{\mr{L},z}|\mathrm{SC}0\rangle+\beta\langle \mathrm{SC}1|\hat{S}_{\mr{L},z}|\mathrm{SC}1\rangle] - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv3b7hqy8 6cd08530baa851e469c29986e71944d9b3a508bd5cebf1ca3d2b1c7bdfd7b9a6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 382:
公式 3912: \begin{split}\Delta\epsilon_0 &= \langle\mathrm{SC}0|\hat{H}_0|\mathrm{SC0}\rangle - \langle\mathrm{SC}2|\hat{H}_0|\mathrm{SC2}\rangle\\                 &= \langle\mathrm{SC}1|\hat{H}_0|\mathrm{SC1}\rangle - \langle\mathrm{SC}2|\hat{H}_0|\mathrm{SC2}\rangle\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpl7ucwtct fdfa7031770094d0625413cb1fdb29358f7e9eda09495e199c88219cf8356488.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 383:
公式 3909: M_{S_{\mr{L}}}=1/2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwa2kbg3t 0a6f9052af329d45ec8dc48c799612f5b58819e9e909f97fefdce50518ed7b07.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 384:
公式 3913: a^{\mr{iso}}=\mr{tr}(\mb{A})/3 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfsqm_v8k edf53a442f425cbb77ceca9cce311f14bda5b732fae31ddb810f1b3965035f2f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 385:
公式 3914: \hat{H}_{\mr{iR}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm_n743z_ fc63626749e392a98506789d888c2f08b3bfb344312df559fdf8707bdd59de60.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 386:
公式 3920: |S_{\mr{cl}}, M_{\mr{cl}}\rangle_{\mr{cl}}=\sum_{M_{12}+M_{34}=M_{\mr{cl}}}c(M_{12},M_{34})|S_{12},M_{12}\rangle_{\mr{UR}}|S_{34},M_{34}\rangle_{\mr{LR}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqy7iyalo 2219c00487d7c8be759a65df944fbf44f96c71b178c2372347069f114a1bfe67.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 387:
公式 3917: d_{\mr{UR}}(M_3,M_4) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdr25a0pl 82413de4627b9c29eae447a845136d4704957b18b3d46c9aac22119551276496.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 388:
公式 3922: a^{\mr{iso}}_n - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp57mt2gpu a627b0190b2632c116901b8855b3328248ff7c66edfa3c42684d96b76ac864c4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 389:
公式 3924: |\mathrm{SC0}\rangle=|0,0\rangle_{\mr{cl}}|1/2,1/2\rangle_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmppfj4womx 16efc1ee3610e074ec9466cbbdf33af5778dfddbd85818c1641d6298ec38b74a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 390:
公式 3926: |0,0\rangle_{\mr{cl}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyutfmvu4 fa17906a5dc367b23e6e6538eb561a95b939514cd9cf59357f7a96ddde944eae.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 391:
公式 3927: \hat{H}_{\mr{F}}=\sum_{\mr{all}~n}\hat{\mb{S}}{\cdot}\mathbf{A}_n^{\mr{obs}}{\cdot}\hat{\mb{I}}_n=\sum_l\hat{\mb{S}}_{L}{\cdot}\mathbf{A}_l^{\mr{int}}{\cdot}\hat{\mb{I}}_l+\sum_{i=1}^4\hat{\mb{S}}_i{\cdot}\mathbf{A}_i^{\mr{int}}{\cdot}\hat{\mb{I}}_i~, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp10srfxvk 71057cbbee6f9f3f4a6b1a8c2f316e2d06b610b94b69bedbc80d00f82895c1c9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 392:
公式 3932: \langle \hat{S}_{\mr{L},z}\rangle= (1/6) \times p_{\mr{ET}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpaoe_ms8l 761dfb040931726f32cbb9549aeef1bcd58cbe749c16227b88ef5b2894842d38.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 393:
公式 3928: S_{\mr{cl}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpql96am0g 95bca2e65c2f89be8efceeef1f5e93829153260dddcd68e276f497d22a76e478.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 394:
公式 3934: \hat{\mb{S}}_{34} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphm3jz6y_ 1b43836fff84e7918a1bdb3227a8a8263cd878e631c57606cd39a1f99512a204.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 395:
公式 3937: S_{\mr{cl}}=0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpixp_9y29 c3697bd948be020d514d77689a01de266dbfc9502ec75934544896dbcd152d5d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 396:
公式 3942: \mathbf{A}^{\mr{obs}}_n = a^{\mr{iso}}_n\mathbf{I} + \mathbf{T}_n^{\mr{dip}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzl6dcs1u 40133e147ec3b93a44dbbd7d4d2b4404ec6bb97128bf0ca5b74df2d2414fca3e.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 397:
公式 3944: |1/2,1/2\rangle_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpr4x99pjb 5eb7541d62f700786d567340385c8f61a9c0e8a76a616c38df576fd65028bdb4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 398:
公式 3943: \hat{\mb{S}}_i - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpni2ayhjd 77533480888666c82ce6e66ae41eaa77f367d37a2393cd1b6a8636e11e86abff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 399:
公式 3945: |\mathrm{SC}2\rangle=|1/2,1/2\rangle_{\mr{cl}}|0,0\rangle_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqi_9fh0q 3b4ba768abcd79641e333aa2f908ff2b876f83c0d59ffd4b14f683450fb9022c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 400:
公式 3949: \langle\Psi|\hat{S}_{\mr{L},z}|\Psi\rangle=\langle\hat{S}_{\mr{L},z}\rangle - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjps83m36 40cdee6bf518caaec89e8dbbc7bccf95bc2c7657687a49653449b266766d6216.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 401:
公式 3955: K_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprgc7q0ax 9191c7d257d1a995f6de715bb95608479f3f7ade7f5096f12199a66279c6a43f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 402:
公式 3954: |\mathrm{SC}1\rangle=\sqrt{\frac{2}{3}}|1,1\rangle_{\mr{cl}}|1/2,-1/2\rangle_{\mr{L}}-                     \sqrt{\frac{1}{3}}|1,0\rangle_{\mr{cl}}|1/2,1/2\rangle_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpb00_r00t 4ecd12728d8b2509de956609a135e7dbd72af974a09e4eb4db0374671711848f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 403:
公式 3956: Q_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkbzf75z0 911ad0d17176f00cbc39ca6a524e51e4a03ca41f665bc278253c93339d3049d8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 404:
公式 3959: \alpha = |C_0|^2/p_{\mr{ET}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjxb8t178 2cd474e3cfb97e55397ad1895c1c5b9a1ad4a1aa6cf83b89c17fcec20c75e450.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 405:
公式 3963: \begin{split}\mathbf{A}_1^{\mr{obs}}&=-\frac{12}{\sqrt{3}}\frac{k}{j}\mathbf{A}_1^{\mr{int}}~,~~\mathbf{A}_2^{\mr{obs}}=-\frac{17}{\sqrt{3}}\frac{k}{j}\mathbf{A}_2^{\mr{int}}~,\\\mathbf{A}_3^{\mr{obs}}&=+\frac{16}{\sqrt{3}}\frac{k}{j}\mathbf{A}_3^{\mr{int}}~,~~\mathbf{A}_4^{\mr{obs}}=+\frac{13}{\sqrt{3}}\frac{k}{j}\mathbf{A}_4^{\mr{int}}\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuk0ff7rw f0e82327a3c96c4dcf0a39fca5894bd162336eb028442c9cbce1a1e3136757f6.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 406:
公式 3969: \begin{split}C_1&=-\frac{H_{10}}{H_{11}-H_{00}}\\   &=\frac{5}{2}\times \frac{k}{j}\end{split} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpggccso5b d5179275a42b7f672892dd9b81043e131c96937fdd710b1a2285dd62ee079f7a.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{split} won't work here.
--------------------------------------------------------------------------------

错误 407:
公式 3970: C_{\mu}^{(1)}=-\frac{\langle \mr{SC}\mu|\hat{\Delta}|\Psi^{(0)}\rangle}{\langle \mr{SC}\mu|\hat{H}_{\mr{diag}}|\mr{SC}\mu\rangle - \langle\Psi^{(0)}|\hat{H}_{\mr{diag}}|\Psi^{(0)}\rangle} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjjs89wpe fbf02eb6b54ba9f396491e1ca402c172b955c5d904a8e99f514647143405b220.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 408:
公式 3974: p_{\mr{ET}}a^{\mr{pure}}_{\mr{L}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2sz_8zif 8b9fed3275752d4c5422ef014203bd6df503d1705af3447d003f2c76f054f1b0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 409:
公式 3971: K_{\mr{L}}=p_{\mr{ET}}^{-1}\frac{\langle \hat{S}_{\mr{L},z}\rangle}{\langle \hat{S}_z\rangle}=\frac{1}{3} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprqbidhb6 3053b6f60ee24860eb64d320b957f6d712b4b55ac6339d5f61e9824c984b4a1f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 410:
公式 3981: d_{\mr{UR}}(M_1,M_2) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpt5rb93mc ae2277a38d811710e5fb2b24af964895a0482e975805343ed7ecaaea9b2f91e0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 411:
公式 3983: p_{\mr{ET}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpabcy23ux 3eba7fb323db325ea9b308dda2412a22cff93c4aff75d47176d6242303167ef6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 412:
公式 3982: \beta = |C_1|^2/p_{\mr{ET}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwap4soqf 5984917aa4dab660e160bacc586d9a2a02b4e760fa112d7ca76636acaec76990.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 413:
公式 3986: a^{\mr{iso}}_{l}=K_{\mr{L}}p_{\mr{ET}}a^{\mr{int}}_{l} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqpq07hwh 26868431fa327ee94e68c564fab54d0cf58535372f275a46fccc4425c787b85c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 414:
公式 3985: \mathbf{A}_l^{\mr{obs}}=K_{\mr{L}}\mathbf{A}_l^{\mr{int}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpw5sel8t_ 226e1a14a94339342df9b177f16d5de2b519b8e767850dffade02252c9f21a3f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 415:
公式 4022: \Rey_b=5000 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp15h7sa7n 8e8700f05f51cccc41592a90a583e7780dae5b0bb947d9d884a10569a0e27f9d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 416:
公式 4055: \begin{eqnarray}   &\nabla \cdot \mathbf{u} = 0,   \\   &\displaystyle \frac{\partial \mathbf{u}}{\partial t} + \nabla \cdot (\mathbf{u} \mathbf{u}) = - \frac{1}{\rho_f} \nabla p + \frac{\mu}{\rho_f} \nabla^2 \mathbf{u} + \mathbf{f}_s + \mathbf{f}_b,   \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_0oc80di b8a84dfdeaee07a4047691e3cdabe161931e00f63ce866d8869e031a4e092880.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 417:
公式 4119: \Rey_b=U_b H \rho_f/ \mu = 5000 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjdij6ig9 6e01fe1938041fa496583bfee56b7af794341b08f5262a69b08c1d382153f0f0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 418:
公式 4154: \begin{eqnarray}    &\Delta \Tilde{\rho} \displaystyle \frac{\partial^2 \mathbf{X}}{\partial t^2} =     \displaystyle \frac{\partial}{\partial s}\left(T\displaystyle \frac{\partial \mathbf{X}}{\partial s}\right) - \gamma \displaystyle \frac{\partial^4 \mathbf{X}}{\partial s^4} - {\mathbf{F_{IBM}}},     \\    &\displaystyle \frac{\partial \mathbf{X}}{\partial s} \cdot \displaystyle \frac{\partial \mathbf{X}}{\partial s} = 1,    \end{eqnarray} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjpz5i66c 4223ba4b14cdf9fa8de0c946cd7d9d0e9573ef4eae163efdce5d04cda90a92a1.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

错误 419:
公式 4171: \VEC{X}= -\frac{\partial\psi}{\partial\VEC{p}} =  \MAT{E}:(\VEC{\varepsilon} - \VEC{p}) =\VEC{\sigma}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpe0hqj5z4 45d5580b4618d75d5fd544c9606d4ae40e89783adf109f20299f122111f7038b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 420:
公式 4177: \dot{\VEC{p}} = \dot{\lambda}\cfrac{df_{\plforb}}{d\VEC{X}}(\VEC{X}),\quad \dot{\lambda} \geq 0,\quad f_{\VEC{X}}(\VEC{X}) \leq 0, \quad \dot{\lambda}f_{\VEC{X}}(\VEC{X})=0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpanerz62y a830a0f20a16beef3c159378bdb4f7728ec65e83758638287e3af13410b8b71f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 421:
公式 4181: \begin{align}\begin{split}\psi(\VEC{\varepsilon},\VEC{p}) \coloneqq&\ \frac{1}{2} (\VEC{\varepsilon}-\VEC{p}):\MAT{E}(\VEC{\varepsilon}):(\VEC{\varepsilon}-\VEC{p}) \\ =&\ \frac{\kappa(\VEC{\varepsilon})}{2}[\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p})]^2 + \mu(\VEC{\varepsilon})\, (\VEC{\varepsilon}^D-\VEC{p}^D):(\VEC{\varepsilon}^D-\VEC{p}^D).\end{split}\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf2dsmt9a c3c2d9b4a4847bc1d54dd8fe9b78339e2298e3b547bf55f455d4fe8cf337986f.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 422:
公式 4179: \VEC{\sigma} = -p_0\mathbf{I} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpulib2kqw c1cd9bdb8a6ed9e38ee087cc8a13cc72985e8dbff59c8cc19b25b526360d0a7a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 423:
公式 4184: \MAT{K}_{\VEC{X}} \coloneqq \{ \VEC{X}^*\in\MAT{R}^{3\times 3}_{\rm sym} \, |\, f_{\plforb}(\VEC{X}^*) \leq 0 \} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfvvebgyc 9ef79e48e20e804fe7644a35c6e60c749be7b0c5c691e52477b4641b58b9742f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 424:
公式 4178: \VEC{\tau}\otimes\VEC{\eta} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpa4qgptvt 850fc363fbf8ebf9cd3ae43e3d13907a97075c165a13d65f663ef669d4a336ca.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 425:
公式 4185: {\VEC{X}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpq0ii090c 25e60cfa20dceb72a799980113d32f09459c9c527ea7154df914bff623eafd3f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 426:
公式 4180: \kappa(\VEC{\varepsilon})>0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpffqi4fx1 d6bbbe365d6c4b86c5b81a4e18ae18a067a66ece5eb640679908a49ba146633c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 427:
公式 4187: \VEC{\varepsilon} = (\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}/3)\mathbf{I} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsa63fqpy 1e765bd2a6dc6bf7a7db10a59ea421e327fb7599f50132325012a0324948b752.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 428:
公式 4189: \VEC{\sigma} \in \MAT{R}^{3\times 3}_{\text{sym}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmplbwt5p9u db594ecd70833badff6d67fd6b2634742b804fc49ea56d1deafbff6768254bbf.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 429:
公式 4191: \frac{d\VEC{\stress}}{d\VEC{\varepsilon}} = \frac{\partial \hat {\stressb}}{\partial \VEC{\varepsilon}} + \frac{\partial \hat {\stressb}}{\partial \VEC{p}}:\frac{\partial \VEC{p}}{\partial \VEC{\varepsilon}}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu7uj1lnt cbe1b383a0f0c95fb00d487e40eaa7e2ffa5ff015467640de39a4033b35627e4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 430:
公式 4195: \| \VEC{\varepsilon}^D \| - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphydhzmpm c13a0cb5210484f00b139a9ef8b0cdf481a4dd60812cfab5f0a901521db1544d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 431:
公式 4198: \dot{\VEC{p}} = \dot{\lambda} \left(\frac{1}{\sqrt{6}} \frac{\VEC{X}^D}{\lVert\VEC{X}^D\rVert} + \frac{a}{3}\, \mathbf{I} \right), - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4i47pi0o eb78a8bc2623d3aaa91e690d00ed373981880a3cf54a1fc6136f7ef48a2a838b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 432:
公式 4194: \MAT{K}_{\VEC{\stress}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9ek0icg3 e46d5b84e411f21cc28be40321cc5241319a03056d8e0603f96730f79eefaf6f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 433:
公式 4200: \MAT{K}_{\VEC{X}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkeq4bn38 82708e3f8df1d95b746e3d249b2b14e0ff8cee88cd1c9f57cda9f31fbb574749.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 434:
公式 4199: \frac{\partial f(\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}, \| \VEC{\varepsilon}^D \|)}{\partial \VEC{\varepsilon}}=\frac{\partial f}{\partial \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}\frac{\partial \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}{\partial \VEC{\varepsilon}} +  \frac{\partial f}{\partial \| \VEC{\varepsilon}^D \|}\frac{\partial \| \VEC{\varepsilon}^D \|}{\partial \VEC{\varepsilon}} = \frac{\partial f}{\partial \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}\mathbf{I} +  \frac{\partial f}{\partial \| \VEC{\varepsilon}^D \|}\frac{\VEC{\varepsilon}^D}{\| \VEC{\varepsilon}^D \|}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcfihxsgi 2dea5ab37230815306e754cbabb42605a6daf7c4a423a70b5cb162dbac8b18e3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 435:
公式 4201: {\cal D(\VEC{p}=\text{const})}=0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpr4yruc0f 377e46bad125795b084d56aff44c48a38cfb0a270858fa9754b2ff59ed930a65.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 436:
公式 4205: \psi(\VEC{\varepsilon},\VEC{p}) = \frac{1}{2} (\VEC{\varepsilon}-\VEC{p}) :\MAT{E}:(\VEC{\varepsilon}-\VEC{p}). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8syh9o9o cb7fadc9698ed2c7c7add7d76523445680f14ea1cb24c32d6073850f46a66d38.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 437:
公式 4206: \begin{aligned}\sigma_m &=\cfrac{\kappa_i}{1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}} \text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p}) \\&-\cfrac{\kappa_i^2\beta_m}{(1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon} )^2} [\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})]^2 \\\end{aligned} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprml1_a0f 6327c23b6b491681acd5833fb3ce4cce6b7458c8a2755f71e901280bc3806561.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 438:
公式 4204: \VEC{\varepsilon}^D=\VEC{0} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9_fpo7dw 225e6419d6dd710afb6f888d6063520a932d1beaeba510e5db0d890d3de821e6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 439:
公式 4210: f_{\VEC{X}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp64kgz0hp c60bfa2b93d00531d17307e682840ef8b083dbdcd44715062b264e4e173a642e.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 440:
公式 4209: \sigma_m = \displaystyle\frac{1}{3} (\sigma_z - 2 p_0),\quad    \lVert\VEC{\sigma}^D\rVert = \sqrt{\frac{2}{3}}\, \lvert q \rvert, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdi46glbr 018b183980d07d518aca93f7be02467cf3ce8deb93d52c90ebe5b30802152e87.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 441:
公式 4213: f_{\VEC{X}}(\VEC{X}) \coloneqq \cfrac{1}{\sqrt{6}}\left\lVert\VEC{X}^D\right\rVert + a X_m - b - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpnc5qre1g d665aa04726cf6718e1d5696c96e7763bea2aa21dfa8cec3e55478ac6f14c95a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 442:
公式 4214: f_{\VEC{\stress}}(\VEC{\sigma}) = \frac{\beta_m}{6}\left\lVert\VEC{\sigma}^D\right\rVert^2 + \frac{a-2\beta_mb}{\sqrt{6}} \left\lVert\VEC{\sigma}^D\right\rVert + a^2\sigma_m -  b\left(a-\beta_m b\right). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpil3a2knu 059e58a3a82dbee367befa007bb03327f898b1b977cd53f3354085a87a2b7751.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 443:
公式 4216: q \coloneqq p_0 + \sigma_z - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpg0zux0l5 e678502ecad36137137c940856b3dedac1cf4ec4f2eca9609bbcc4f9793bb814.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 444:
公式 4218: 45\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpd9f1njqa cb15dfc8e75e5ad16d8cbfaeb0693ffcd6e97c14ac404dedacf906583b03db7c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 445:
公式 4217: \frac{1}{\sqrt{6}}\left\lVert\VEC{\sigma}^D\right\rVert = \frac{-(a-2\beta_mb) + a\sqrt{1-4\sigma_m\beta_m}}{2\beta_m}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8i9bkb49 e08ba1c3bd23f7a8542c1e878eacb273239a192ebdbb195ec4b716106ef20d7b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 446:
公式 4223: \VEC{\sigma} = \hat {\VEC{\stress}}(\VEC{\varepsilon},\VEC{p}) := \kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p})\left[1-\beta_m\kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p})\right]\mathbf{I} + 2\mu(\VEC{\varepsilon}^D-\VEC{p}^D), - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmps444oi44 9a7c8e626b84e665ced64d338434a6feb49c670d1d3b1d0e7ffcd629137ab774.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 447:
公式 4229: \kappa(\VEC{\varepsilon}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpz8958hnl ed96c5052c7fe844f79b80478d6a0e3f7ba7243d6e340d7893f07f46ecdde377.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 448:
公式 4226: \frac{3}{2C_0} \left\lVert\VEC{\sigma}^D\right\rVert^2 + \frac{\sqrt{3}m_i}{2\sqrt{2}} \left\lVert\VEC{\sigma}^D\right\rVert + m_i\sigma_m - C_0 \leq 0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpu_lgo7i9 971761e3874cfb1f836cd05a2e5a768e5668245df8657676279d66e62afe8fc8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 449:
公式 4230: \text{Tr}\hspace{0.5mm} \VEC{\varepsilon} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9z_683by 9e3cf4adc65501a819e151c153d7aedcc1f44c1f3c4b5923e256185899199ce3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 450:
公式 4231: \frac{d\VEC{\stress}}{d\VEC{\varepsilon}} = 0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpkag2zll6 eb83aeaa102e5931a62ae6b1e0f5a557e723cbece8e5a7f9582fb9dd6e4b085f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 451:
公式 4232: \text{Tr}\hspace{0.5mm}\VEC{\varepsilon} = \frac{1}{2\kappa_i\beta_m} \left( \frac{1}{\sqrt{1 - 4\beta_m \bar{\stress}}} - 1 \right). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqg5vdowu 80d0eee9238da744b5c2db966b13ba9a011a4c96e83033553e1471867a1359ff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 452:
公式 4239: \VEC{X} = \cfrac{\kappa_i}{1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}} \text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})\mathbf{I}+2\mu_i(\VEC{\varepsilon}^D-\VEC{p}^D) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmphs0mkngj e1bf92f14bcdf8f7064779919f8c6fe7cb2ff2b94bec7b89ec8fffafda9c05d1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 453:
公式 4241: \Delta\lambda = \frac{f_{\plforb}(\VEC{X}^{el})}{\mu/3 + a^2\kappa(\VEC{\varepsilon})},\quad    \Delta\VEC{p} = \Delta\lambda\left(\VEC{n}^D+\frac{a}{3}\mathbf{I}\right). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpfp1qi7qj 7fe91a684310aadb207a5f692c6c6ae6aadb3f3cd7c5865a6d7647494d0b6fe0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 454:
公式 4240: \MAT{K}:=\MAT{I} - \MAT{J} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0zxcz0t8 289e54b81e97637b7a490b032890b83fb124f0213aae632b8a4cf58f559da531.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 455:
公式 4237: \begin{align}        \frac{d\VEC{\stress}}{d\VEC{\varepsilon}} =&\          3\kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})^2\MAT{J} + 2\mu\MAT{K} \\        &- \frac{\left[a\kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})\mathbf{I}+2\mu \VEC{n}^D\right]\otimes\left[a\kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})\mathbf{I}+2\mu \VEC{n}^D\right]}{\mu/3 + a^2\kappa(\VEC{\varepsilon})}        \\        &-\frac{\Delta\lambda}{\sqrt{6}}\frac{4\mu^2}{\|\VEC{X}^{D,el}\|}\left(\MAT{K}-6\VEC{n}^{D}\otimes\VEC{n}^{D}\right).    \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpto7f6u56 8acd3c1eb615b60564d922908db1d910a982eaf10f9a22199d3d54a31224b346.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 456:
公式 4243: \displaystyle\frac{1}{\mu(\varepsilon)^2}\displaystyle\frac{\partial\mu(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}} = -\frac{\partial\mu^{-1}(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpc32n_lbf 055c35508cb7a0f65dd0c8391c81ac5619e98b169ee7ef885a5fefbaf96f35a4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 457:
公式 4242: \VEC{\tau}^D - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpj_x78ef9 2bb5df9e7542d5933d580cef608b87ffd0aa218c552adc0600ec387e28a52182.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 458:
公式 4244: \Delta\VEC{p} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpko48qbaw 2aa4f32bf6ad591ed88043397a14e02ae452f8020602c3b8ed7b462c3a651f2d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 459:
公式 4250: \frac{d\VEC{\stress}}{d\VEC{\varepsilon}} =        3\kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})^2\MAT{J} + 2\mu\MAT{K}, \quad\text{with}\quad \omega(\VEC{\varepsilon},\VEC{p}) = \frac{1+2\beta_m\kappa_i\text{Tr}\hspace{0.5mm}\VEC{p}}{1+2\beta_m\kappa_i\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdsjzj3m5 77735ecdf8be326bc25c338a5255eb29e3f2e839910284a1533e73f9a9c0ef8f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 460:
公式 4252: \VEC{p}_n - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6dofijd1 a0802c3c6d62abc7055851ce3215e28389f00d49ecf3a241cd6abb749bf7a1e6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 461:
公式 4253: \VEC{\tau} : \VEC{\eta}\coloneqq  \text{Tr}\hspace{0.5mm} (\VEC{\tau} \cdot \VEC{\eta}) = \tau_{ij}\eta_{ji} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9f77wl4b dad3aea5c7e0eea6fd420c2c8f4aad078a019453e7e6f81f1f57353aca5054a5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 462:
公式 4256: \VEC{p}=\VEC{p}_n+\Delta\VEC{p} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpbqr2cma4 4d2cc275fa34596dd05888af97e2eea528c9364e278e61fcba9f311c55422cd9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 463:
公式 4257: \VEC{\tau} = \tau_m\, \mathbf{I} + \VEC{\tau}^D, \quad \text{with} \ \tau_m = \frac{\text{Tr}\hspace{0.5mm}\VEC{\tau}}{3}, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp09ibi5n7 71f33976c95b5853d3354ec78e91604f22d077c7af964668397a8173c419feec.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 464:
公式 4260: \VEC{\sigma} = \cfrac{\kappa_i}{1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}} \text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})\left(1-\cfrac{\kappa_i\beta_m}{1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon} }\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})\right)\mathbf{I}+2\mu_i(\VEC{\varepsilon}^D-\VEC{p}^D) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_5jo41qa 97ca3a4d739a0e3a81284db3263500fd92420113241c027654df77e99e5c9184.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 465:
公式 4255: \VEC{\tau} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpflempgmo 3d789453fab15aecb6befe2de65a3a42392ca8bacae738163f075fd2acdf11b4.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 466:
公式 4258: \| \VEC{\varepsilon}^D \|^2 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjyjcj_f4 9f2dd8c190109bb4c3275fe0385bdd0d9682d18c02e55527b34e100acd0b3dff.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 467:
公式 4262: f_{\VEC{X}}(\VEC{X}^{el})>0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprqrme67p 1df969108173febdd647eaf5a2481a713829cadb4c8fd4d1058e57f657e8dc94.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 468:
公式 4264: \VEC{\varepsilon}\in\MAT{R}^{3\times 3}_{\text{sym}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp1tpb7w30 c5d083d27ab3c3a5e9b39b6bcb18040c346f0aa1974d8af9bfd4c58a0633d7ac.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 469:
公式 4265: 10\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7sr8pk7t d1302b7d0fab30558f0999d184b59754635467f4f65b07c53cd3864cd008f416.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 470:
公式 4266: f(\VEC{\varepsilon}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvojx1est ff17508fa201a24f19a625c46ca5097c9e683a9728176b4b895a065f74cf55bb.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 471:
公式 4268: \VEC{\sigma} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgar8cvpo 126b6f38be98a0728fe7a6212756cc70da03068191e56ebf91888b0bb68751d2.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 472:
公式 4270: (\VEC{e}_x,\VEC{e}_y,\VEC{e}_z) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqxggyfs4 1c082493e2a93125675b2e180ee77c3aa8024e6b61c62305592d309fe517a4db.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 473:
公式 4273: \VEC{X} = -\partial\psi/\partial\VEC{p} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpq6goo504 9d3e2d9099fe790b3ef389b84fd23e538ef420816b354ddc074ea2bb200933da.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 474:
公式 4274: \psi(\VEC{\varepsilon},\VEC{p}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdo1sb6zc 516c99e9d9d983dc11be5aaba8547f477270feb421377251f6bd4969bd758d5b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 475:
公式 4271: f_{\VEC{X}}(\VEC{X}) \coloneqq \frac{1}{\sqrt{6}}\left\lVert\VEC{X}^D\right\rVert + a X_m - b, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6wf015hk f1830c5786197e9bcfa4dfd4f4cd097dedfeae6697d123fcb7626c49b0b92608.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 476:
公式 4276: \mu(\VEC{\varepsilon}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqgbqk85f 703a8845d599590dd6d8b65902d21d5f0909e1efa24e065bf7a9278699652290.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 477:
公式 4278: \VEC{\sigma} = \VEC{X} + \frac{1}{2\kappa(\VEC{\varepsilon})^2}\frac{\partial\kappa(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}}X_m^2 + \frac{1}{4\mu(\VEC{\varepsilon})^2}\frac{\partial\mu(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}} \VEC{X}^D :\VEC{X}^D. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7_x1l4v3 1c54915c4ab47e74911572e8b9a3181cecd3d267beb1d4bb765affea36ce3596.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 478:
公式 4280: {\cal D} =\VEC{\sigma} : \dot{\VEC{\varepsilon}} - \dot\psi (\VEC{\varepsilon},\VEC{p})\ge 0, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzk6q22xf 59a118930958eeee82333ced444ee6dea4a00bfbd0b11a583128212b311c4e9d.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 479:
公式 4281: \displaystyle\frac{1}{\kappa(\VEC{\varepsilon})^2}\frac{\partial\kappa(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}} = -\frac{\partial\kappa^{-1}(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0tnlpv88 3f8f85eab5759e12e95d7b80694df2637d5960f1731b6aee1d70cf083c57435b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 480:
公式 4282: f_{\VEC{X}}(\VEC{X}^{el})\leq 0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpc60swuv8 6b7cae5fbb30f42f3b06b33bc78372dfa60badcffd3d260bd0a8ab86e6201fd1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 481:
公式 4283: \begin{align}\begin{split}\VEC{\sigma} &= \frac{\partial\psi (\VEC{\varepsilon},\VEC{p})}{\partial \VEC{\varepsilon}} \\&= \MAT{E}(\VEC{\varepsilon}):(\VEC{\varepsilon} - \VEC{p}) + \frac{1}{2} \frac{\partial\kappa(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}}[\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p})]^2 + \frac{\partial\mu(\VEC{\varepsilon})}{\partial \VEC{\varepsilon}}(\VEC{\varepsilon}^D-\VEC{p}^D) : (\VEC{\varepsilon}^D-\VEC{p}^D),\end{split}\end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4tshtbko a780a47058fe34fbde4c0f7f19d3c4d817fac97540521d8a998ec057df4cd428.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 482:
公式 4286: \MAT{A} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp3glseye1 b5e29ab02ea1fe668ebae4a73595c928612ba3b3f6886db40e159b88a07da797.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 483:
公式 4285: \VEC{\sigma} = \VEC{X} - \beta_mX_m^2\mathbf{I}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpscunvygd 2fe80d370ae2ab730f62433cc8c8ec0e7d1d93e92b7ca5feb959d66f6a4a7e33.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 484:
公式 4289: (X_m,\VEC{X}^D)=(b/a, \VEC{0}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf6e8c8ik e8d70693469fcbde6cb0b9dc3f49a1fbbfeaf31dda007deaed6d882b919da145.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 485:
公式 4292: \MAT{E} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdgia03ct a3fff71a3fa14c226ef7d864646f6dfe54718707e80d52219011c4c8c7c3e8c1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 486:
公式 4291: \Delta\lambda < \sqrt{6}\|\VEC{X}^{D,el}\|/(2\mu) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpobxmrt2d 17bf508d415f21cfb44dea3bd6448bd3bdc0d07e16f7cca87a338e5cb8a34348.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 487:
公式 4288: f_{\VEC{\stress}} = \left\lVert\VEC{\sigma}^D\right\rVert^2 + A \left\lVert\VEC{\sigma}^D\right\rVert + B \sigma_m - C \leq 0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9agysl9j 4388b2e88298fdff41858adde37fbb9143d84121593d33184e1b34fa29e9d7c3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 488:
公式 4293: \VEC{\eta} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmptjizrmx4 770221790cad8102c26a05b1d7eb704e8bbd5f0487cbec0a5d3df529ab8f9e82.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 489:
公式 4294: \Delta\VEC{p} = \frac{\VEC{X}^{D,el}}{2\mu} + \frac{a X_m^{el} - b}{3a\kappa(\VEC{\varepsilon})}\mathbf{I}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp2swvtc76 37f8d3a2b27a57405cd7da2505704ac5342a82e8fff5d9eccbf07fc9209c2831.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 490:
公式 4296: {\partial\mu(\VEC{\varepsilon})}/{\partial \strainb} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpow5rp72y 8ceb75fc9fff6bbcafa69e52d1f100b90e218eb52ff4fcd225d509e5ccba2651.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 491:
公式 4299: \coloneqq - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzo3xtrmg 76f7f6fef438c56ea376c2544e1ea52a3df8176ed3adbb157dfa19488f32acc8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 492:
公式 4302: \VEC{\sigma} = \begin{pmatrix}    -p_0 & 0 & 0 \\     0 & -p_0 & 0 \\    0 & 0 & \sigma_z    \end{pmatrix},\quad    \VEC{\varepsilon} = \begin{pmatrix}    \varepsilon_x & 0 & 0 \\    0 & \varepsilon_x & 0 \\    0 & 0 & \varepsilon_z    \end{pmatrix}. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpqnvd32qc 20acaa0aaaf6e06f99ebc2e4f5868e8ae674248e35b20021c51c238a986b13f3.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 493:
公式 4303: \kappa(\VEC{\varepsilon}) = \frac{\kappa_i}{1+2\kappa_i \beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}},\quad \mu(\VEC{\varepsilon}) = \frac{\mu_i}{1+4\mu_i\beta^D\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpxvhdx12o d280c0a543ba4b53c84e562b01c926d003949e0b3c63992d19c20abbd4ce4b68.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 494:
公式 4306: \text{Tr}\hspace{0.5mm}\VEC{\varepsilon} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm9a5tl4k 82ef687e46afd82b073df35978f48ee66932a1c1d4e5a542351557b1c1d2772f.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 495:
公式 4307: \MAT{I} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmprkdeatvs 61412d17239ef36329831b2c81075504e076b06d625b9bca5c59d30341fc22c6.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 496:
公式 4310: \MAT{K}_{\VEC{\stress}} \coloneqq \{ \VEC{\stress}^*\in\MAT{R}^{3\times 3}_{\rm sym} \, |\, f_{\stressb}(\VEC{\stress}^*) \leq 0 \} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpv_e5pipk ebe1bebbaa1f0b8e2569a37e42942d98fa7ccc4fff1f3f89bd6cbc485167f0ac.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 497:
公式 4311: {\cal D} = -\frac{\partial \psi}{\partial \VEC{p}} :\dot{\VEC{p}} = \VEC{X} : \dot{\VEC{p}}\ge 0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpw82l02us c017358bdb58512d2848cf97ec625b34c703bec37ba2d696afdd909ee572cdbf.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 498:
公式 4315: (\MAT{A}:\VEC{\tau})_{ij}\coloneqq A_{ijkl}\tau_{kl} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp09bud_yx 6af76f70a126accf3e84c5832d956647211034c0943ed61ba7f271bdc4337297.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 499:
公式 4317: \frac{1}{\sqrt{6}}\left\lVert\VEC{\sigma}^D\right\rVert = b-a\sigma_m. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpm8rlocvf ccbf08acde0f8371ecef5a16c676b49e5dee1c523b5e054e3bc22c82ef5162a1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 500:
公式 4318: \VEC{X} =-\frac{\partial\psi (\VEC{\varepsilon},\VEC{p})}{\partial \VEC{p}}= \MAT{E}(\VEC{\varepsilon}):(\VEC{\varepsilon} - \VEC{p}) = \kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p})\mathbf{I} + 2\mu(\VEC{\varepsilon})\, (\VEC{\varepsilon}^D-\VEC{p}^D). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpy5iuw97y cc403cbe10afb9ebc30e23715c8922a6ee70e30b7fe12c99bcc377f6a26f6b1a.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 501:
公式 4319: f_{\VEC{X}}(\VEC{X}) = 0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcevuzist f399a52bd42ee1add1d02aaccb036fcd40a21c2c35c1f760f7aa0ba288439082.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 502:
公式 4320: \VEC{\sigma}  = \partial\psi/\partial\VEC{\varepsilon} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpczma1g4j 6b1f00ae6a7e18918fcbc4a40627a3f0c77aa2b26e732029e6968d37ab7daf94.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 503:
公式 4321: \VEC{\varepsilon} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp8o75wl8h db9b1bca4413046f118453fff2f627d5b4a5109cd96982ff9d5b17c7de8eab0c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 504:
公式 4326: \VEC{X} = X_m\mathbf{I} + \VEC{X}^D,\quad\text{with}\quad \begin{cases}X_m =\cfrac{\kappa_i}{1+2\kappa_i \beta_m \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})  \vspace{4mm} \\\VEC{X}^D = 2\mu_i (\VEC{\varepsilon}^D-\VEC{p}^D).\end{cases} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpzdy9x3a_ b84de38c4a23842f5b29fb99a6c7bebace987daea1e09ea0e2f58602963a7eaa.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 505:
公式 4325: \VEC{p}\in\MAT{R}^{3\times 3}_{\text{sym}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6f861y89 3529afba9586fad3a35d14398b2b43ac3bf575a1cacd2721aded8a77ec9e584c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 506:
公式 4328: \VEC{\varepsilon}^D = \VEC{0} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsi0sklxo 8f593ef87339a99dc6cd3f5f481e8c425c4870e56991c5bba67272bbe33b1845.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 507:
公式 4329: \MAT{J}:=\mathbf{I}\otimes\mathbf{I}/3 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjks4c1pd f61e48eedeeb6fee318b955cdef3998f2b490d8deb730593f0ed189143a1876c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 508:
公式 4333: \dot{\lambda} \geq 0,\quad f_{\VEC{X}}(\VEC{X}) \leq 0, \quad \dot{\lambda}f_{\VEC{X}}(\VEC{X})=0. - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpo24n6sj9 64b3a45525e0853851685ce12e21e018f7ec6f00eb1b8effe4b401aeb4a9c0ce.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 509:
公式 4331: \VEC{u} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwfi5ucz8 88c3c789777d3cd76a93fc203494c25162b3b279af76e05d7b8839b1abe3b863.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 510:
公式 4334: \VEC{X} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpz8bqaqxw 2429a884f202e6f2b165eb18ae516c4313fc0a0c0790888385434a778fcd24e0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 511:
公式 4336: \bar{\sigma} = \frac{1}{4 \beta_m} \left(1 - \frac{1}{(1+2\kappa_i\beta_m \text{Tr}\hspace{0.5mm}\VEC{\varepsilon})^2} \right), - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpivbc2vwr 20c599d3829ea91d123cdeabb06dd861747d770086807786d3b7388f4df2ddaf.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 512:
公式 4337: \VEC{X} = \VEC{X}^{el} := \kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon}-\VEC{p}_n)\mathbf{I} + 2\mu(\VEC{\varepsilon}^D-\VEC{p}_n^D). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsi9gfygd e72b7242e6821e2d5783d15506bcedfe09c02a3fe3ea6a0a877f7ff26ac77520.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 513:
公式 4338: \VEC{X} \in \MAT{R}^{3\times 3}_{\text{sym}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpw0ye80si 0d0dfdd97f98994e7f1a1bf560de504f874d1d71c58f912d49caf669c667fc6b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 514:
公式 4339: \VEC{\eta}:\frac{d\VEC{\stress}}{d\VEC{\varepsilon}}:\VEC{\eta} \geq     \frac{\mu\kappa(\VEC{\varepsilon})/3}{\mu/3+a^2\kappa(\VEC{\varepsilon})}\left(\omega(\VEC{\varepsilon},\VEC{p})\text{Tr}\hspace{0.5mm}\VEC{\eta}-6a\VEC{\eta}^D:\VEC{n}^D\right)^2 \geq 0, - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0_z9yt4c 6e7de5cb3db52d49b6e113be00acfeda69d7d66b5597c28f69800a5f2edaa388.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 515:
公式 4341: \VEC{\sigma} = \sigma_m\mathbf{I} + \VEC{\sigma}^D,\quad\text{with}\quad \begin{cases}\begin{aligned}\sigma_m &=\cfrac{\kappa_i}{1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon}} \text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p}) \\&-\cfrac{\kappa_i^2\beta_m}{(1+2\kappa_i\beta_m\text{Tr}\hspace{0.5mm}\VEC{\varepsilon} )^2} [\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})]^2 \\\end{aligned}\vspace{4mm} \\\VEC{\sigma}^D = 2\mu_i(\VEC{\varepsilon}^D-\VEC{p}^D),\end{cases} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmph0jlr7p4 2609316a9e04279cc3446ea5e22a4389f76acc2b8e0fc5a8a3da5969cc24c6dd.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 516:
公式 4343: f(\VEC{\varepsilon}) = C_1 + C_2\text{Tr}\hspace{0.5mm}\VEC{\varepsilon} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpjkrc_jus b77852434964a75e281f751d36ddf5c6e5c3c79f48d4c0e0a2675936a8212943.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 517:
公式 4345: \VEC{\sigma} = \bar{\sigma} \mathbf{I} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpsksi5d38 ad9646caee09c8a9d33e29aa6e9e3ec25ff02237adb09bd562b89b2d9238c9e1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 518:
公式 4346: {d\VEC{\stress}}/{d\VEC{\varepsilon}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp6p1ddn9v d055be10afe811d69fbfa341982a04301772b7e889274fb34e622b872f3f50b9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 519:
公式 4350: \left\lVert \VEC{\tau}\right\rVert \coloneqq \sqrt{\VEC{\tau} : \VEC{\tau}} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpt0s1u8zq e7834b8b58ff7dfe377d69733d796ece0a56de70ecaa7ddf9c255477b925d3c5.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 520:
公式 4351: \VEC{p} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp9l0zn8f0 bd2a7e6299fd9dedc4dc621a2a9ed1a9ad2e91e84ef14b2efa66dc6c523ec3a0.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 521:
公式 4353: \VEC{\tau},\VEC{\eta} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp314hcvqg f7be3cc9fe8d1e52c0c47d59777b62833c7545cec5e09bcfb13d272d8e316a03.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 522:
公式 4354: f(\VEC{\varepsilon})\in\mathbb{R} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4uutesy1 1b409976c481f0af900176a425af5103821af59bb7597601a5204342bd1d6204.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 523:
公式 4355: f_{\VEC{\stress}}(\VEC{\sigma}) = \cfrac{\beta_m}{6}\left\lVert\VEC{\sigma}^D\right\rVert^2 + \cfrac{a-2\beta_mb}{\sqrt{6}} \left\lVert\VEC{\sigma}^D\right\rVert + a^2\sigma_m -  b\left(a-\beta_m b\right) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmph62g8p1g 03385e4f2092fd09979199b3c120992625578ca0f2fe3121f4540b293ac5e764.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 524:
公式 4357: \varepsilon_{eq}=\sqrt{2/3}\|\VEC{\varepsilon}^D\| - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpp2af_kw4 3ec29a6977a5420a8ccb5c92c21a549f72b05f0515f4706863ae76f82cac6413.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 525:
公式 4358: \VEC{X} = \VEC{X}^{el} - 2\mu\Delta\VEC{p}^D -\kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\Delta\VEC{p})\mathbf{I} = \VEC{X}^{el} - \Delta\lambda\left(2\mu\frac{1}{\sqrt{6}}\frac{\VEC{X}^D}{\| \VEC{X}^D\|}+\kappa(\VEC{\varepsilon})a\mathbf{I}\right), - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_lf1pi1f 5a7d4b51ff8874b6059325e518f78b39ddf8455c8a6e97db07d4e50628ab459c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 526:
公式 4359: \VEC{\sigma} = \frac{\partial \psi}{\partial\VEC{\varepsilon}}= \MAT{E}:(\VEC{\varepsilon} - \VEC{p}). - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpedaztiju 3cf07b501bd81d5901ae6a2976412579ccdae9eb28ed3f93c3a5af16b60729fe.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 527:
公式 4361: \begin{align}    \VEC{\eta}:\frac{d\VEC{\stress}}{d\VEC{\varepsilon}}:\VEC{\eta} =&\  \kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})^2(\text{Tr}\hspace{0.5mm}\VEC{\eta})^2 + 2\mu\|\VEC{\eta}^D\|^2 - \frac{\left(a\kappa(\VEC{\varepsilon})\omega(\VEC{\varepsilon},\VEC{p})\text{Tr}\hspace{0.5mm}\VEC{\eta} + 2\mu\VEC{\eta}^D:\VEC{n}^D\right)^2}{\mu/3+a^2\kappa(\VEC{\varepsilon})} - \\    &- \frac{\Delta\lambda}{\sqrt{6}}\frac{4\mu^2}{\|\VEC{X}^{D,el}\|}\left[\|\VEC{\eta}^D\|^2-6(\VEC{\eta}^D:\VEC{n}^D)^2\right]. \end{align} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdopjrlen ab540e47f68cd93544411ddeeee5bc4f699a895a9336e62f4bb86aa9a89ee105.tex; ==> First Aid for underscore.sty applied!; ! Package amsmath Error: \begin{align} allowed only in paragraph mode.
--------------------------------------------------------------------------------

错误 528:
公式 4363: {\partial\kappa(\VEC{\varepsilon})}/{\partial \strainb} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpp8k0ugta 61d956a8fe11eca893b6dd5438c4a9484c5a91e762409cf776f2f2301d1590ca.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 529:
公式 4362: \VEC{\sigma}\neq\VEC{X} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp16yuegsw fd8a77f3cf300edf5cd31f2e60c57a6467e4ce7ba175f6d0867daa1e055e1ce9.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 530:
公式 4364: \VEC{n}^D := \VEC{X}^D/(\sqrt{6}\|\VEC{X}^D\|) = \VEC{X}^{D,el}/(\sqrt{6}\|\VEC{X}^{D,el}\|) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpuim12lav 412e1d559fa17cca15df0bf7ea4e85fe5edafd042a150f5de219d89e02089f7b.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 531:
公式 4366: \|\VEC{X}^{D}\|>0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0egxd8nm 94b7891b2938ba94ad764d559d0afc79828c3cc1df1d54d31778582214dce30c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 532:
公式 4368: \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}=0 - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpmbuajwow 2b99f4215865297062d0268c635b0e0f6d911f36129607f343f2a107f5d5fa93.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 533:
公式 4369: 53\degree-56\degree - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_muqoqi4 5040d0d3abaa0c43762540815ba88cc5abc19a5791520ced16edf28c6f873cd8.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 534:
公式 4370: \VEC{X}^{D} = \VEC{X}^{D,el} - 2\mu\Delta\VEC{p}^D = \VEC{0} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpwhleu_s3 a72503947a89bfdb1cada10311148510ef1c7193a819d1d670daf0c82cdea7c1.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 535:
公式 4373: \VEC{p} = \VEC{0} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp0waf3nbq 1abee3723c3e742e852738fda5878948933998c9d99a36c5b88278931714f5ac.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 536:
公式 4372: \psi(\VEC{\varepsilon},\VEC{p}) := \cfrac{1}{2}\cfrac{\kappa_i}{1+2\kappa_i \beta_m \text{Tr}\hspace{0.5mm}\VEC{\varepsilon}}[\text{Tr}\hspace{0.5mm}(\VEC{\varepsilon} -\VEC{p})]^2+\mu_i(\VEC{\varepsilon}^D-\VEC{p}^D):(\VEC{\varepsilon}^D-\VEC{p}^D) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdnm3o2v_ 9bb1f02c0280903de830a3e2c4daf1ad54c1bf7acfccf6edbd4bbf0eb2c4df3c.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 537:
公式 4374: (\sigma_m,\VEC{\sigma}^D)=(b(a-\beta_m b)/a^2, \VEC{0}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmpdc2cmky7 5a61f7fd79faf505721e9332cc40d339a398db1938a90779230825048ee453ef.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 538:
公式 4375: X_m = X_m^{el} - \kappa(\VEC{\varepsilon})\text{Tr}\hspace{0.5mm}(\Delta\VEC{p}) - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp5155u2aq fbcad4fa083496f1de8838fb3ceb6d14954e6805f2c31b2782af513f203f5841.tex; ==> First Aid for underscore.sty applied!; ! Undefined control sequence.
--------------------------------------------------------------------------------

错误 539:
公式 4555: \begin{eqnarray*}& &{\rm Tr} \,(D^2) =   \cos^2\theta \left(1+\frac{\displaystyle a^2}{\displaystyle {1+a^2}}+\frac{\displaystyle 1}{\displaystyle {2(1+a^2)}}\right)+\\ & &      \displaystyle \frac{\sin^2\theta}{2}\left(1+\frac{\displaystyle a^2}{\displaystyle {1+a^2}}-\frac{2\,a}{\displaystyle \sqrt{1+a^2}}+\frac{\displaystyle 1}{\displaystyle {(1+a^2)}}\right) +\frac{\cos\theta}{1+a^2}+\frac{\displaystyle 1}{\displaystyle {2(1+a^2)}} +\frac{\displaystyle a^2}{\displaystyle {1+a^2}}.         \end{eqnarray*} - 错误: LaTeX编译错误: latex -interaction=nonstopmode --halt-on-error --output-directory=tmp674lhnma 91769439efcca8f9301b4f835de549bdead7888e3f74ea17368d190e8eafeeb4.tex; ==> First Aid for underscore.sty applied!; ! Missing \endgroup inserted.
--------------------------------------------------------------------------------

