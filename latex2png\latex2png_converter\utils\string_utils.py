"""
字符串处理工具函数
"""

import re
from typing import List, Dict, Optional
from ..monitoring.logger import get_logger

def clean_latex_code(latex_code: str) -> str:
    """
    清理LaTeX代码
    
    Args:
        latex_code: 原始LaTeX代码
        
    Returns:
        str: 清理后的LaTeX代码
    """
    if not latex_code:
        return ""
    
    # 移除前后空白
    cleaned = latex_code.strip()
    
    # 移除多余的空行
    cleaned = re.sub(r'\n\s*\n', '\n', cleaned)
    
    # 标准化空格
    cleaned = re.sub(r'[ \t]+', ' ', cleaned)
    
    return cleaned

def normalize_whitespace(text: str) -> str:
    """
    标准化空白字符
    
    Args:
        text: 输入文本
        
    Returns:
        str: 标准化后的文本
    """
    if not text:
        return ""
    
    # 将所有空白字符替换为单个空格
    normalized = re.sub(r'\s+', ' ', text)
    
    # 移除前后空白
    normalized = normalized.strip()
    
    return normalized

def extract_latex_commands(latex_code: str) -> List[str]:
    """
    提取LaTeX命令
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        List[str]: 命令列表
    """
    if not latex_code:
        return []
    
    # 匹配LaTeX命令模式
    command_pattern = r'\\([a-zA-Z]+)'
    commands = re.findall(command_pattern, latex_code)
    
    # 去重并排序
    unique_commands = sorted(list(set(commands)))
    
    return unique_commands

def count_latex_elements(latex_code: str) -> Dict[str, int]:
    """
    统计LaTeX元素
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        Dict[str, int]: 元素统计
    """
    if not latex_code:
        return {}
    
    stats = {
        'total_chars': len(latex_code),
        'commands': len(re.findall(r'\\[a-zA-Z]+', latex_code)),
        'braces': latex_code.count('{'),
        'brackets': latex_code.count('['),
        'parentheses': latex_code.count('('),
        'dollar_signs': latex_code.count('$'),
        'underscores': latex_code.count('_'),
        'carets': latex_code.count('^'),
        'environments': len(re.findall(r'\\begin\{[^}]+\}', latex_code))
    }
    
    return stats

def is_valid_latex_identifier(identifier: str) -> bool:
    """
    检查是否为有效的LaTeX标识符
    
    Args:
        identifier: 标识符
        
    Returns:
        bool: 是否有效
    """
    if not identifier:
        return False
    
    # LaTeX标识符只能包含字母
    return re.match(r'^[a-zA-Z]+$', identifier) is not None

def escape_special_chars(text: str) -> str:
    """
    转义特殊字符
    
    Args:
        text: 输入文本
        
    Returns:
        str: 转义后的文本
    """
    if not text:
        return ""
    
    # LaTeX特殊字符映射
    special_chars = {
        '&': r'\&',
        '%': r'\%',
        '$': r'\$',
        '#': r'\#',
        '^': r'\^{}',
        '_': r'\_',
        '{': r'\{',
        '}': r'\}',
        '~': r'\~{}',
        '\\': r'\textbackslash{}'
    }
    
    escaped = text
    for char, replacement in special_chars.items():
        escaped = escaped.replace(char, replacement)
    
    return escaped

def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 输入文本
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def format_latex_for_display(latex_code: str, max_length: int = 50) -> str:
    """
    格式化LaTeX代码用于显示
    
    Args:
        latex_code: LaTeX代码
        max_length: 最大显示长度
        
    Returns:
        str: 格式化后的代码
    """
    if not latex_code:
        return ""
    
    # 清理代码
    cleaned = clean_latex_code(latex_code)
    
    # 截断长代码
    if len(cleaned) > max_length:
        cleaned = truncate_string(cleaned, max_length)
    
    return cleaned

def split_latex_by_lines(latex_code: str, max_line_length: int = 80) -> List[str]:
    """
    按行分割LaTeX代码
    
    Args:
        latex_code: LaTeX代码
        max_line_length: 最大行长度
        
    Returns:
        List[str]: 分割后的行列表
    """
    if not latex_code:
        return []
    
    lines = []
    current_line = ""
    
    words = latex_code.split()
    
    for word in words:
        if len(current_line + " " + word) <= max_line_length:
            if current_line:
                current_line += " " + word
            else:
                current_line = word
        else:
            if current_line:
                lines.append(current_line)
            current_line = word
    
    if current_line:
        lines.append(current_line)
    
    return lines

def find_matching_brace(text: str, start_pos: int) -> Optional[int]:
    """
    查找匹配的括号
    
    Args:
        text: 文本
        start_pos: 开始位置（应该是'{'的位置）
        
    Returns:
        Optional[int]: 匹配括号的位置，未找到返回None
    """
    if start_pos >= len(text) or text[start_pos] != '{':
        return None
    
    brace_count = 1
    pos = start_pos + 1
    
    while pos < len(text) and brace_count > 0:
        if text[pos] == '{':
            brace_count += 1
        elif text[pos] == '}':
            brace_count -= 1
        pos += 1
    
    return pos - 1 if brace_count == 0 else None

def extract_latex_environments(latex_code: str) -> List[Dict[str, str]]:
    """
    提取LaTeX环境
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        List[Dict[str, str]]: 环境信息列表
    """
    if not latex_code:
        return []
    
    environments = []
    
    # 查找所有begin环境
    begin_pattern = r'\\begin\{([^}]+)\}'
    begin_matches = re.finditer(begin_pattern, latex_code)
    
    for match in begin_matches:
        env_name = match.group(1)
        start_pos = match.start()
        
        # 查找对应的end环境
        end_pattern = rf'\\end\{{{re.escape(env_name)}\}}'
        end_match = re.search(end_pattern, latex_code[match.end():])
        
        if end_match:
            end_pos = match.end() + end_match.end()
            content = latex_code[match.end():match.end() + end_match.start()]
            
            environments.append({
                'name': env_name,
                'start_pos': start_pos,
                'end_pos': end_pos,
                'content': content.strip()
            })
    
    return environments

def generate_safe_identifier(base_name: str, existing_names: set = None) -> str:
    """
    生成安全的标识符
    
    Args:
        base_name: 基础名称
        existing_names: 已存在的名称集合
        
    Returns:
        str: 安全的标识符
    """
    if existing_names is None:
        existing_names = set()
    
    # 清理基础名称
    safe_name = re.sub(r'[^a-zA-Z0-9_]', '_', base_name)
    safe_name = re.sub(r'_+', '_', safe_name)
    safe_name = safe_name.strip('_')
    
    if not safe_name:
        safe_name = 'item'
    
    # 确保唯一性
    if safe_name not in existing_names:
        return safe_name
    
    counter = 1
    while f"{safe_name}_{counter}" in existing_names:
        counter += 1
    
    return f"{safe_name}_{counter}"
