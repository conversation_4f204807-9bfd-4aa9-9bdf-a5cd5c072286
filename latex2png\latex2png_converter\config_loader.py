#!/usr/bin/env python3
"""
配置加载器
从YAML配置文件加载用户配置，并设置环境
"""

import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

def load_user_config(config_file: str = "config/user_config.yaml") -> Dict[str, Any]:
    """
    加载用户配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    config_path = Path(config_file)
    
    if not config_path.exists():
        print(f"警告: 配置文件不存在: {config_path}")
        print("将使用默认配置")
        return {}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✓ 配置文件加载成功: {config_path}")
        return config or {}
        
    except Exception as e:
        print(f"错误: 配置文件加载失败: {e}")
        print("将使用默认配置")
        return {}

def setup_environment_from_config(config: Dict[str, Any]) -> bool:
    """
    根据配置设置环境变量
    
    Args:
        config: 配置字典
        
    Returns:
        是否成功设置
    """
    system_config = config.get('system', {})
    
    success = True
    
    # 1. 配置TeX Live路径
    texlive_path = system_config.get('texlive_path')
    if texlive_path and system_config.get('auto_configure_path', True):
        if Path(texlive_path).exists():
            current_path = os.environ.get('PATH', '')
            if texlive_path not in current_path:
                os.environ['PATH'] = f"{texlive_path};{current_path}"
                print(f"✓ TeX Live路径已添加: {texlive_path}")
            else:
                print(f"✓ TeX Live路径已存在: {texlive_path}")
        else:
            print(f"⚠ TeX Live路径不存在: {texlive_path}")
            success = False
    
    # 2. 清理MiKTeX路径冲突
    if system_config.get('clean_miktex_paths', True):
        path_parts = os.environ.get('PATH', '').split(';')
        clean_parts = [p for p in path_parts if 'miktex' not in p.lower()]
        if len(clean_parts) < len(path_parts):
            os.environ['PATH'] = ';'.join(clean_parts)
            print("✓ MiKTeX路径冲突已清理")
    
    # 3. 验证Python路径
    python_path = system_config.get('python_path')
    if python_path:
        if Path(python_path).exists():
            print(f"✓ Python路径验证成功: {python_path}")
        else:
            print(f"⚠ Python路径不存在: {python_path}")
            success = False
    
    return success

def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置的有效性
    
    Args:
        config: 配置字典
        
    Returns:
        配置是否有效
    """
    issues = []
    
    # 检查必需的配置项
    system_config = config.get('system', {})
    if not system_config.get('texlive_path'):
        issues.append("缺少TeX Live路径配置")
    
    if not system_config.get('python_path'):
        issues.append("缺少Python路径配置")
    
    # 检查性能配置
    perf_config = config.get('performance', {})
    max_workers = perf_config.get('max_workers', 16)
    if max_workers > 64:
        issues.append(f"max_workers过大: {max_workers}，建议不超过64")
    
    batch_size = perf_config.get('batch_size', 2000)
    if batch_size > 10000:
        issues.append(f"batch_size过大: {batch_size}，可能导致内存不足")
    
    # 检查输出配置
    render_config = config.get('render', {})
    dpi = render_config.get('dpi', 300)
    if dpi > 1200:
        issues.append(f"DPI过高: {dpi}，可能导致文件过大")
    
    if issues:
        print("配置验证发现问题:")
        for issue in issues:
            print(f"  ⚠ {issue}")
        return False
    
    print("✓ 配置验证通过")
    return True

def print_config_summary(config: Dict[str, Any]):
    """
    打印配置摘要
    
    Args:
        config: 配置字典
    """
    print("\n配置摘要:")
    print("=" * 40)
    
    # 系统配置
    system_config = config.get('system', {})
    print(f"TeX Live路径: {system_config.get('texlive_path', '未配置')}")
    print(f"Python路径: {system_config.get('python_path', '未配置')}")
    
    # 输入输出
    io_config = config.get('io', {})
    print(f"默认输入: {io_config.get('default_input_file', '未配置')}")
    print(f"默认输出: {io_config.get('default_output_dir', './output')}")
    
    # 渲染配置
    render_config = config.get('render', {})
    print(f"DPI: {render_config.get('dpi', 300)}")
    print(f"使用外部LaTeX: {render_config.get('use_external_latex', True)}")
    
    # 性能配置
    perf_config = config.get('performance', {})
    print(f"最大并发: {perf_config.get('max_workers', 16)}")
    print(f"批次大小: {perf_config.get('batch_size', 2000)}")
    
    print("=" * 40)

def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        'system': {
            'texlive_path': 'D:\\Program Files\\texlive\\2025\\bin\\windows',
            'python_path': 'D:\\miniforge3\\envs\\formula\\python.exe',
            'auto_configure_path': True,
            'clean_miktex_paths': True
        },
        'io': {
            'default_input_file': 'examples/sample_input.txt',
            'default_output_dir': './output'
        },
        'render': {
            'dpi': 300,
            'transparent_background': True,
            'use_external_latex': True
        },
        'performance': {
            'max_workers': 16,
            'batch_size': 2000,
            'timeout_seconds': 30
        }
    }
    
    config_path = Path("config/sample_config.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(sample_config, f, default_flow_style=False, 
                 allow_unicode=True, indent=2)
    
    print(f"示例配置文件已创建: {config_path}")

def main():
    """主函数 - 测试配置加载"""
    print("LaTeX2PNG 配置加载器测试")
    print("=" * 40)
    
    # 加载配置
    config = load_user_config()
    
    if not config:
        print("创建示例配置文件...")
        create_sample_config()
        return
    
    # 验证配置
    if not validate_config(config):
        print("配置验证失败，请检查配置文件")
        return
    
    # 设置环境
    if setup_environment_from_config(config):
        print("✓ 环境设置成功")
    else:
        print("⚠ 环境设置存在问题")
    
    # 打印摘要
    print_config_summary(config)
    
    # 测试LaTeX命令
    print("\n测试LaTeX命令:")
    try:
        import subprocess
        result = subprocess.run(['latex', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✓ LaTeX可用: {version}")
        else:
            print("✗ LaTeX命令失败")
    except Exception as e:
        print(f"✗ LaTeX测试失败: {e}")

if __name__ == "__main__":
    main()
