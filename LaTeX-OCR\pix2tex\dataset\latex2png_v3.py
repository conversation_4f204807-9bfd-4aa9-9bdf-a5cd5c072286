# mostly taken from http://code.google.com/p/latexmath2png/
# install preview.sty
#
# ==================== 历史版本说明 ====================
# 这是v2版本，已保存为历史版本
# 新的优化版本请使用 latex2png_v3.py
#
# ==================== 配置区域 ====================
# 在这里修改所有参数，无需使用命令行

# 在文件开头添加日志配置
import logging

# 配置简洁的日志输出
def setup_logging(verbose=False):
    """设置日志级别"""
    if verbose:
        logging.basicConfig(level=logging.DEBUG, format='[%(levelname)s] %(message)s')
    else:
        logging.basicConfig(level=logging.WARNING, format='[%(levelname)s] %(message)s')

# 基础配置
CONFIG = {
    # 工作模式选择 (必须选择其中一种):
    # 'batch'  - 批量处理：从文件中读取多个公式进行批量渲染
    # 'single' - 单个处理：渲染配置中指定的单个公式（测试用）
    # 'list'   - 列表处理：渲染配置中指定的公式列表
    'mode': 'batch',
    
    # 输入配置
    'input': {
        # batch模式：从文件读取公式列表，每行一个公式
        'formula_file': r"D:\formula_ge_dataset\arxiv_50\math_arxiv.txt",
        # 'formula_file': r"D:\formula_ge_dataset\arxiv_25\test.txt",
        # single模式：单个公式字符串（用于测试）
        'single_formula': r'x^2 + y^2 = z^2',
        #\begin{split}    \tilde{\mathcal{E}}(\omega)\propto\\ \exp\left[-\frac{(\omega - \omega_0)^2}{2\sigma^2_\omega} - i\frac{\varphi''(\omega - \omega_0)^2}{2}-i\frac{\varphi'''(\omega-\omega_0)^3}{6}\right]\end{split}

        # list模式：公式列表（用于小批量测试）
        'formula_list': [
            # 第一阶段修复测试用例

            # 测试新增自定义命令
            r'\Weightk',  # 测试单独的Weightk
            r'\Flux = \Dot{\Flux}',  # 测试Flux和复合命令
            r'\lambdah_{bl}',  # 测试连写的lambda h
            r'\argmin_{x} f(x)',  # 测试argmin操作符
            r'I\leq I_{\text{c}}',  # 测试leq和text下标

            # 测试改进的单位处理
            r'(\si{\watt/ \meter})',  # 测试带空格的复合单位
            r'\si{\ampere/\meter^2}',  # 测试复杂复合单位
            r'5\,\si{\micro\litre}',  # 测试带空格的单位

            # 测试特殊符号
            r'\langle x \rangle_T',  # 测试角括号
            r'\argmin_{X \in \mathbb{R}} f(X)',  # 测试复杂argmin

            # 测试环境修复
            r'{\color{red}{2a}}',  # 测试颜色命令
            r'\hspace{0.5mm}',  # 测试空格命令
        ]
    },
    
    # 输出配置
    'output': {
        # 输出目录 - 生成的PNG图像保存位置
        # 'output_dir': r'./test_output',
        'output_dir': r"D:\formula_ge_dataset\arxiv_50\test",
        # 输出文件名格式:
        # 'index'     - 使用序号命名: 000001.png, 000002.png, ...
        # 'hash'      - 使用公式哈希命名: a1b2c3d4.png (避免重复)
        # 'formula'   - 使用公式内容命名: x2_y2_z2.png (可能有特殊字符问题)
        'filename_format': 'index',
        
        # 文件名前缀配置:
        # 'auto'      - 自动从输出目录路径生成前缀，智能识别目录特征
        # 'custom'    - 使用下面custom_prefix指定的自定义前缀字符串
        # None        - 不使用前缀，直接使用基础文件名
        'filename_prefix': 'auto',
        'custom_prefix': 'my_dataset',  # 当filename_prefix='custom'时使用此前缀
        
        # 错误日志配置
        'save_error_log': True,  # 是否保存渲染失败的公式到日志文件
        
        # 错误日志文件路径配置
        'error_log_paths': {
            # 失败公式列表文件（与输入txt同格式，便于重新处理）
            'failed_formulas': r'D:\formula_ge_dataset\arxiv_50/failed_formulas.txt',
            # 'failed_formulas': r'D:\formula_ge_dataset\arxiv_25\failed_formulas.txt',
            # 详细错误报告文件（便于人工分析问题）
            'detailed_report': r'D:\formula_ge_dataset\arxiv_50/error_report.txt',
            # 'detailed_report': r'D:\formula_ge_dataset\arxiv_25\error_report.txt',
        },
        
        # 向后兼容：如果用户仍使用旧的error_log_file配置，自动生成路径
        # 'error_log_file': r'D:\formula_ge_dataset\arxiv_25\render_errors.txt',  # 已弃用，保持兼容
    },
    
    # 渲染参数
    'render': {
        # DPI设置 - 影响图像清晰度和文件大小
        # 150-200: 适合网页显示，文件较小
        # 250-300: 适合打印质量，推荐用于数据集
        # 400+: 超高清，文件很大
        'dpi': 150,
        
        # 字体设置 - 影响数学符号的显示效果
        # 'Latin Modern Math' - 默认LaTeX数学字体，兼容性最好
        # 'XITS Math'        - 更现代的数学字体
        # 'Cambria Math'     - Windows系统字体
        'font': 'Latin Modern Math',
        
        # 颜色空间设置:
        # 'gray'  - 灰度图像，适合OCR训练，文件小
        # 'RGB'   - 彩色图像，保留颜色信息，文件大
        'colorspace': 'gray',
        
        # JPEG质量 (1-100)，只在保存为JPEG时有效
        # PNG格式忽略此设置，但影响中间转换过程
        'quality': 90,
    },
    
    # 处理选项
    'processing': {
        # 是否跳过已存在的文件，避免重复渲染
        'skip_existing': True,
        
        # 错误处理策略:
        # 'skip'     - 跳过出错的公式，继续处理其他公式
        # 'stop'     - 遇到错误立即停止整个处理过程
        # 'retry'    - 尝试重新渲染出错的公式（实验性）
        'error_handling': 'skip',
        
        # 错误显示模式:
        # 'realtime' - 实时显示每个失败的公式（适合小批量调试）
        # 'batch'    - 批处理完成后统一显示错误摘要（适合大批量处理）
        # 'both'     - 两种模式都启用
        # 'none'     - 不显示错误信息（仅保存到文件）
        'error_display_mode': 'batch',
        
        # 错误详细程度:
        # 'simple'           - 仅显示错误类型和公式
        # 'detailed'         - 显示详细错误信息
        # 'with_suggestions' - 显示错误信息和修复建议（未来功能）
        'error_detail_level': 'detailed',
        
        # 界面显示选项
        'show_progress': True,   # 是否显示进度条
        'verbose': False,        # 是否显示详细调试信息（大批量时建议False）
        
        # 批处理参数
        'batch_size': 50,        # 初始批处理大小，系统会自动调整
    },

    # ==================== v3性能优化配置 ====================
    'optimization': {
        # 持久化缓存配置
        'enable_cache': True,  # 是否启用持久化缓存
        'cache_dir': r'D:\formula_ge_dataset\cache',  # 缓存目录
        'cache_max_size_gb': 5.0,  # 缓存最大大小(GB)
        'cache_cleanup_threshold': 0.8,  # 缓存清理阈值

        # 递归分批处理配置
        'enable_recursive_batch': True,  # 是否启用递归分批处理
        'recursive_max_depth': 3,        # 递归最大深度
        'error_rate_threshold': 0.3,     # 错误率阈值，超过此值将分割批次
        'min_batch_size': 1,             # 最小批次大小

        # 预处理器优化
        'enable_preprocessor_cache': True,  # 是否缓存预处理结果
        'preprocessor_cache_size': 10000,   # 预处理缓存最大条目数

        # 性能监控
        'enable_performance_stats': True,  # 是否启用性能统计
        'show_cache_stats': True,          # 是否显示缓存统计
        'show_preprocessor_stats': True,   # 是否显示预处理统计
    }
}

# ==================== 代码实现区域 ====================

import os
import re
import sys
import io
import glob
import tempfile
import shlex
import subprocess
import traceback
import hashlib
import json
from pathlib import Path
from PIL import Image
from tqdm import tqdm
import time
from collections import deque
from tqdm.auto import tqdm


class LaTeXPreprocessor:
    """LaTeX公式预处理器，将不兼容的命令转换为标准格式

    采用分层转换架构：
    1. 精确匹配规则（最高优先级）
    2. 单位类别规则（平衡准确性和覆盖面）
    3. 通用规则（兜底保证）
    """

    def __init__(self):
        # 转换统计
        self.conversion_stats = {
            'total_conversions': 0,
            'by_layer': {'exact': 0, 'category': 0, 'fallback': 0},
            'by_type': {},
            'issues_detected': [],
            'conversion_log': []
        }

        # 第一层：精确匹配规则（最高优先级，最准确）
        self.exact_patterns = {
            # siunitx精确转换
            r'\\SI\s*\{([^}]+)\}\{\\degree\}': (lambda m: f"{m.group(1)}°", 'siunitx_degree'),
            r'\\SI\s*\{([^}]+)\}\{\\nano\\meter\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{nm}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\nano\\metre\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{nm}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\micro\\meter\}': (lambda m: f"{m.group(1)}\\,\\mu\\mathrm{{m}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\micro\\metre\}': (lambda m: f"{m.group(1)}\\,\\mu\\mathrm{{m}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\milli\\meter\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{mm}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\meter\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{m}}", 'siunitx_length'),
            r'\\SI\s*\{([^}]+)\}\{\\metre\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{m}}", 'siunitx_length'),

            # 电学单位
            r'\\SI\s*\{([^}]+)\}\{\\ampere\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{A}}", 'siunitx_electrical'),
            r'\\SI\s*\{([^}]+)\}\{\\volt\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{V}}", 'siunitx_electrical'),
            r'\\SI\s*\{([^}]+)\}\{\\watt\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{W}}", 'siunitx_electrical'),
            r'\\SI\s*\{([^}]+)\}\{\\tesla\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{T}}", 'siunitx_electrical'),

            # 频率和时间
            r'\\SI\s*\{([^}]+)\}\{\\hertz\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{Hz}}", 'siunitx_frequency'),
            r'\\SI\s*\{([^}]+)\}\{\\kilo\\hertz\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{kHz}}", 'siunitx_frequency'),
            r'\\SI\s*\{([^}]+)\}\{\\second\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{s}}", 'siunitx_time'),
            r'\\SI\s*\{([^}]+)\}\{\\milli\\second\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{ms}}", 'siunitx_time'),
            r'\\SI\s*\{([^}]+)\}\{\\micro\\second\}': (lambda m: f"{m.group(1)}\\,\\mu\\mathrm{{s}}", 'siunitx_time'),

            # 温度
            r'\\SI\s*\{([^}]+)\}\{\\kelvin\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{K}}", 'siunitx_temperature'),

            # 体积
            r'\\SI\s*\{([^}]+)\}\{\\micro\\litre\}': (lambda m: f"{m.group(1)}\\,\\mu\\mathrm{{L}}", 'siunitx_volume'),
            r'\\SI\s*\{([^}]+)\}\{\\litre\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{L}}", 'siunitx_volume'),

            # 角度单位
            r'\\SI\s*\{([^}]+)\}\{\\steradian\}': (lambda m: f"{m.group(1)}\\,\\mathrm{{sr}}", 'siunitx_angle'),

            # 复合单位的精确匹配
            r'\\(\\si\{\\watt/\s*\\meter\}\\)': (lambda m: r'(\mathrm{W/m})', 'siunitx_compound'),
            r'\\si\{\\watt/\s*\\meter\}': (lambda m: r'\mathrm{W/m}', 'siunitx_compound'),
            r'\\si\{\\ampere/\\meter\^2\}': (lambda m: r'\mathrm{A/m^2}', 'siunitx_compound'),
        }

        # 第二层：通用规则系统
        self.universal_rules = {
            # 1. 通用转义修复
            'escape_fixes': {
                'patterns': {
                    r'\\([a-zA-Z]+)\\{([^}]+)\\}': r'\\\1{\2}',  # \command\{arg\} -> \command{arg}
                    r'\\([a-zA-Z]+)\\\{([^}]+)\\\}': r'\\\1{\2}', # \command\{arg\} -> \command{arg}
                    r'\\&': r'&',   # 表格中的&符号
                    r'\\_(?![a-zA-Z])': r'_',   # 下标符号（避免影响命令）
                    r'\\%': r'%',   # 百分号
                },
                'type': 'escape_fix'
            },

            # 2. 通用命令模式识别
            'pattern_commands': {
                'patterns': {
                    # 向量和矩阵命令模式
                    r'\\V[Ee][Cc]\{([^}]+)\}': r'\\boldsymbol{\1}',
                    r'\\MAT(?:RIX)?\{([^}]+)\}': r'\\boldsymbol{\1}',
                    r'\\VECTOR\{([^}]+)\}': r'\\boldsymbol{\1}',

                    # 微分命令模式
                    r'\\D[Oo][Tt]\{([^}]+)\}': r'\\dot{\1}',
                    r'\\DOT\{([^}]+)\}': r'\\dot{\1}',

                    # 分数命令模式
                    r'\\f\{([^}]+)\}\{([^}]+)\}': r'\\frac{\1}{\2}',
                    r'\\cfrac\{([^}]+)\}\{([^}]+)\}': r'\\frac{\1}{\2}',
                    r'\\dfrac\{([^}]+)\}\{([^}]+)\}': r'\\frac{\1}{\2}',

                    # 偏导数和常见符号模式
                    r'\\p(?![a-zA-Z])': r'\\partial',
                    r'\\pd(?![a-zA-Z])': r'\\partial',
                    r'\\Re(?![a-zA-Z])': r'\\mathrm{Re}',
                    r'\\Im(?![a-zA-Z])': r'\\mathrm{Im}',

                    # 范数和绝对值
                    r'\\lVert': r'\\|',
                    r'\\rVert': r'\\|',
                    r'\\coloneqq': r':=',
                    r'\\approxeq': r'\\approx',
                    r'\\apprle': r'\\lesssim',
                },
                'type': 'pattern_command'
            },

            # 3. 智能单位处理系统
            'smart_units': {
                'si_prefixes': {
                    r'\\nano': 'n', r'\\micro': 'μ', r'\\milli': 'm',
                    r'\\kilo': 'k', r'\\mega': 'M', r'\\giga': 'G',
                    r'\\tera': 'T', r'\\peta': 'P'
                },
                'base_units': {
                    r'\\meter': 'm', r'\\metre': 'm', r'\\gram': 'g',
                    r'\\second': 's', r'\\ampere': 'A', r'\\kelvin': 'K',
                    r'\\volt': 'V', r'\\watt': 'W', r'\\hertz': 'Hz',
                    r'\\tesla': 'T', r'\\henry': 'H', r'\\weber': 'Wb',
                    r'\\pascal': 'Pa', r'\\joule': 'J', r'\\newton': 'N',
                    r'\\litre': 'L', r'\\liter': 'L'
                },
                'type': 'smart_unit'
            },

            # 4. 角度符号处理
            'angle_symbols': {
                'patterns': {
                    r'\\degree': '°',
                    r'([0-9.]+)\s*\\degree': r'\1°',
                    r'([a-zA-Z_]+)\s*=\s*([0-9.]+)\s*\\degree': r'\1=\2°',
                    r'([a-zA-Z_]+)\s*\\degree': r'\1°',
                },
                'type': 'angle_conversion'
            }
        }

        # 第三层：通用规则（兜底保证）
        self.fallback_rules = {
            # 环境修复规则
            'environment_fixes': {
                # 移除问题命令
                r'\\numberthis': '',
                r'\\tag\{[^}]*\}': '',

                # 修复环境嵌套问题
                r'\\begin\{align\}\\begin\{split\}': r'\\begin{align}',
                r'\\end\{split\}\\end\{align\}': r'\\end{align}',

                # 修复array环境格式
                r'\\begin\{array\}\s*\{([^}]+)\}': r'\\begin{array}{\1}',

                # 修复特殊格式问题
                r'_(\d+)\$\$\^{([^}]+)}': r'_{\1}^{\2}',  # 修复$$格式错误

                # 修复cases环境中的区间表示
                r'\\\\(\[[^\]]*\])': r'\\\\\1',  # 保持区间格式

                # 修复substack环境
                r'\\substack\{([^}]+)\}': lambda m: f"\\substack{{{m.group(1).replace('\\\\', '\\\\ ')}}}",
            },

            # 新增：特殊符号和数学操作符
            'special_symbols': {
                # 数学操作符的下标处理
                r'\\argmin_\{([^}]+)\}': r'\\underset{\1}{\\operatorname{argmin}}',
                r'\\argmax_\{([^}]+)\}': r'\\underset{\1}{\\operatorname{argmax}}',

                # 特殊数学符号（移除有问题的规则）
                # r'\\langle': r'\\langle',  # 这些规则会导致无限循环
                # r'\\rangle': r'\\rangle',
                # r'\\|': r'\\|',  # 这个规则有问题

                # 特殊空格处理（移除自引用规则）
                # r'\\,': r'\\,',  # 这些会导致无限循环
                # r'\\quad': r'\\quad',
                # r'\\qquad': r'\\qquad',

                # 特殊下标和上标（移除自引用规则）
                # r'\^{([^}]+)}': r'^{\1}',
                # r'_\{([^}]+)\}': r'_{\1}',

                # 特殊函数（移除自引用规则）
                # r'\\cos': r'\\cos',
                # r'\\sin': r'\\sin',
                # r'\\sqrt': r'\\sqrt',
                # r'\\exp': r'\\exp',
                # r'\\ln': r'\\ln',
                # r'\\log': r'\\log',
            },

            # 通用自定义命令（基于常见模式）
            'generic_commands': {
                # 常见学术符号
                r'\\myRe': r'\\mathrm{Re}',
                r'\\surften': r'\\sigma',
                r'\\Rey': r'\\mathrm{Re}',
                r'\\hbl': r'h_{\\mathrm{bl}}',

                # 物理量相关
                r'\\stressb': r'\\boldsymbol{\\sigma}',
                r'\\strainb': r'\\boldsymbol{\\varepsilon}',
                r'\\plforb': r'\\mathrm{pl}',

                # 数学符号
                r'\\mr\{([^}]+)\}': r'\\mathrm{\1}',
                r'\\mb\{([^}]+)\}': r'\\mathbf{\1}',

                # 特定领域命令（可根据需要扩展）
                r'\\Weightk\{([^}]+)\}': r'w_{\1}',
                r'\\Irevkdt\{([^}]+)\}': r'I_{\\mathrm{rev},\1}',
                r'\\Ieddyk\{([^}]+)\}': r'I_{\\mathrm{eddy},\1}',
                r'\\Fluxintk\{([^}]+)\}': r'\\Phi_{\\mathrm{int},\1}',
                r'\\Coerck\{([^}]+)\}': r'H_{\1}',
                r'\\Timeck\{([^}]+)\}': r'\\tau_{\1}',
                r'\\Irevk\{([^}]+)\}': r'I_{\\mathrm{rev},\1}',
                r'\\Iirrk\{([^}]+)\}': r'I_{\\mathrm{irr},\1}',
                r'\\Vc': r'V_c',
                r'\\Ic': r'I_c',
                r'\\Rmat': r'R_{\\mathrm{mat}}',
                r'\\Rfil': r'R_{\\mathrm{fil}}',
                r'\\Timec': r'\\tau_c',
            },

            # 新增：特定领域命令扩展
            'domain_specific_commands': {
                # 物理/工程领域单独命令
                r'\\Weightk(?![a-zA-Z{])': r'w',  # 单独的Weightk
                r'\\Flux(?![a-zA-Z{])': r'\\Phi',  # 单独的Flux
                r'\\lambdah(?![a-zA-Z])': r'\\lambda h',  # 连写的lambda h
                r'\\bar\{\\stress\}': r'\\bar{\\sigma}',  # 应力平均值

                # 数学操作符
                r'\\argmin': r'\\operatorname{argmin}',  # argmin操作符
                r'\\len': r'\\mathrm{len}',  # 长度函数
                r'\\leq(?![a-zA-Z])': r'\\leq',  # 确保leq正确

                # 特殊格式和空格
                r'\\hspace\{[^}]+\}': r'\\,',  # 简化空格命令为thin space
                r'\\text\{([^}]+)\}': r'\\text{\1}',  # 确保text命令正确

                # 下标中的text转换
                r'_\{\\text\{([^}]+)\}\}': r'_{\\mathrm{\1}}',  # text下标转为mathrm

                # 特殊符号组合
                r'\\Dot\{\\Flux\}': r'\\dot{\\Phi}',  # 复合命令
                r'\\Dot\{\\I\}': r'\\dot{I}',  # 复合命令
            },

            # 颜色和格式命令
            'color_and_format': {
                r'\{\\color\{[^}]+\}\{([^}]+)\}\}': r'\1',  # {\color{red}{content}} -> content
                r'\\color\{[^}]+\}\{([^}]+)\}': r'\1',     # \color{red}{content} -> content
                r'\\cancel\{([^}]+)\}': r'\\not{\1}',      # \cancel{content} -> \not{content}
            },

            # 最后的兜底规则
            'final_fallback': {
                r'\\SI\s*\{([^}]+)\}\{([^}]+)\}': r'\1\\,\\mathrm{\2}',  # 通用SI转换
                r'\\si\{([^}]+)\}': r'\\mathrm{\1}',  # 通用si转换
            }
        }

        # siunitx单位映射
        self.unit_map = {
            r'\\nano\\meter': 'nm',
            r'\\meter': 'm',
            r'\\ampere': 'A',
            r'\\degree': '°',
            r'\\mega\\pascal': 'MPa',
            r'\\kilo\\gram': 'kg',
            r'\\second': 's',
            r'\\volt': 'V',
            r'\\watt': 'W',
            r'\\joule': 'J',
            r'\\newton': 'N',
            r'\\pascal': 'Pa',
            r'\\hertz': 'Hz',
            # 添加更多单位前缀
            r'\\nano': 'n',
            r'\\micro': 'μ',
            r'\\milli': 'm',
            r'\\kilo': 'k',
            r'\\mega': 'M',
        }

    def preprocess(self, formula):
        """主预处理函数 - 分层转换架构"""
        if not formula or not isinstance(formula, str):
            return formula

        original_formula = formula
        self.conversion_stats['total_conversions'] += 1

        # 第一层：精确匹配规则（最高优先级）
        formula = self._apply_exact_patterns(formula)

        # 第二层：单位类别规则
        formula = self._apply_category_rules(formula)

        # 第三层：通用规则（兜底保证）
        formula = self._apply_fallback_rules(formula)

        # 额外的格式修复
        formula = self._fix_special_formats(formula)
        formula = self._fix_math_environments(formula)

        # 记录转换日志
        if formula != original_formula:
            self._log_conversion(original_formula, formula)
            if CONFIG['processing']['verbose']:
                print(f"预处理转换: {original_formula} -> {formula}")

        return formula

    def _apply_exact_patterns(self, formula):
        """应用精确匹配规则"""
        for pattern, (converter, conv_type) in self.exact_patterns.items():
            if re.search(pattern, formula):
                old_formula = formula
                if callable(converter):
                    formula = re.sub(pattern, converter, formula)
                else:
                    formula = re.sub(pattern, converter, formula)

                if formula != old_formula:
                    self.conversion_stats['by_layer']['exact'] += 1
                    self.conversion_stats['by_type'][conv_type] = self.conversion_stats['by_type'].get(conv_type, 0) + 1

        return formula

    def _apply_category_rules(self, formula):
        """应用通用规则系统"""
        for rule_name, rule_config in self.universal_rules.items():
            old_formula = formula

            if rule_name == 'smart_units':
                # 智能单位处理需要特殊逻辑
                formula = self._apply_smart_unit_rules(formula, rule_config)
            else:
                # 其他规则直接应用模式匹配
                for pattern, replacement in rule_config['patterns'].items():
                    formula = re.sub(pattern, replacement, formula)

            if formula != old_formula:
                self.conversion_stats['by_layer']['category'] += 1
                self.conversion_stats['by_type'][rule_config['type']] = self.conversion_stats['by_type'].get(rule_config['type'], 0) + 1

        return formula

    def _apply_smart_unit_rules(self, formula, unit_config):
        """智能单位处理"""
        # 处理SI单位：\SI{value}{\prefix\unit} -> value \, prefix+unit
        def convert_si_unit(match):
            value = match.group(1)
            unit_part = match.group(2)

            # 解析前缀+单位
            converted_unit = self._convert_compound_unit(unit_part, unit_config)
            return f"{value}\\,\\mathrm{{{converted_unit}}}"

        # 处理带前缀的SI命令
        formula = re.sub(r'\\SI\s*\{([^}]+)\}\{\\([a-z]+)\\([a-z]+)\}', convert_si_unit, formula)
        formula = re.sub(r'\\SI\s*\{([^}]+)\}\{\\([a-z]+)\}', convert_si_unit, formula)

        # 处理si命令：\si{\unit} -> unit
        def convert_si_only(match):
            unit_part = match.group(1)
            converted_unit = self._convert_compound_unit(unit_part, unit_config)
            return f"\\mathrm{{{converted_unit}}}"

        formula = re.sub(r'\\si\{\\([a-z/]+(?:\\[a-z]+)*)\}', convert_si_only, formula)

        # 处理带空格的si命令：5\,\si{\nano\metre} -> 5\,\mathrm{nm}
        def convert_spaced_si(match):
            value = match.group(1)
            unit_part = match.group(2)
            converted_unit = self._convert_compound_unit(unit_part, unit_config)
            return f"{value}\\,\\mathrm{{{converted_unit}}}"

        formula = re.sub(r'(\d+(?:\.\d+)?)\s*\\,\s*\\si\{\\([a-z/]+(?:\\[a-z]+)*)\}', convert_spaced_si, formula)

        # 处理带括号的复合单位：(\si{\watt/ \meter}) -> (\mathrm{W/m})
        def convert_parenthesized_si(match):
            unit_part = match.group(1)
            converted_unit = self._convert_compound_unit(unit_part, unit_config)
            return f"(\\mathrm{{{converted_unit}}})"

        formula = re.sub(r'\\left\\(\\si\{\\([a-z/\\s]+)\\}\\right\\)', convert_parenthesized_si, formula)
        formula = re.sub(r'\\(\\si\{\\([a-z/\\s]+)\\}\\)', convert_parenthesized_si, formula)

        # 处理简单括号的情况：(\si{\watt/ \meter})
        formula = re.sub(r'\\(\\si\{([^}]+)\\}\\)', lambda m: f"(\\mathrm{{{self._convert_compound_unit(m.group(1), unit_config)}}})", formula)

        # 处理更复杂的复合单位格式
        def convert_complex_compound(match):
            full_unit = match.group(1)
            # 处理 \ampere/\meter^2 格式
            if '/' in full_unit and '^' in full_unit:
                parts = full_unit.split('/')
                numerator = self._convert_single_unit(parts[0], unit_config)
                denominator_part = parts[1]
                if '^' in denominator_part:
                    base, power = denominator_part.split('^')
                    base_converted = self._convert_single_unit(base, unit_config)
                    return f"{numerator}/{base_converted}^{power}"

            return self._convert_compound_unit(full_unit, unit_config)

        formula = re.sub(r'\\si\{\\([a-z/\\^0-9]+)\}', lambda m: f"\\mathrm{{{convert_complex_compound(m)}}}", formula)

        return formula

    def _convert_compound_unit(self, unit_str, unit_config):
        """改进的复合单位转换"""
        # 处理带括号的复合单位：(\si{\watt/\meter})
        if unit_str.startswith('(') and unit_str.endswith(')'):
            inner_unit = unit_str[1:-1]
            converted = self._convert_compound_unit(inner_unit, unit_config)
            return f"({converted})"

        # 处理带空格的复合单位：\watt/ \meter
        unit_str = re.sub(r'\s+', '', unit_str)  # 移除所有空格

        # 处理斜杠分隔的单位
        if '/' in unit_str:
            parts = unit_str.split('/')
            numerator = self._convert_single_unit(parts[0].strip(), unit_config)
            denominator = self._convert_single_unit(parts[1].strip(), unit_config)
            return f"{numerator}/{denominator}"

        # 处理乘积单位（用空格或点分隔）
        if ' ' in unit_str or '·' in unit_str:
            separators = [' ', '·', r'\.']
            for sep in separators:
                if sep in unit_str:
                    parts = re.split(sep, unit_str)
                    converted_parts = [self._convert_single_unit(part.strip(), unit_config) for part in parts if part.strip()]
                    return '·'.join(converted_parts)

        return self._convert_single_unit(unit_str, unit_config)

    def _convert_single_unit(self, unit_str, unit_config):
        """转换单个单位"""
        # 移除反斜杠
        clean_unit = unit_str.replace('\\', '').strip()

        # 检查是否有前缀
        for prefix_pattern, prefix_symbol in unit_config['si_prefixes'].items():
            prefix_name = prefix_pattern.replace('\\', '')
            if clean_unit.startswith(prefix_name):
                base_unit = clean_unit[len(prefix_name):]
                base_symbol = unit_config['base_units'].get(f'\\{base_unit}', base_unit)
                return f"{prefix_symbol}{base_symbol}"

        # 没有前缀，直接转换基础单位
        base_symbol = unit_config['base_units'].get(f'\\{clean_unit}', clean_unit)
        return base_symbol

    def _apply_fallback_rules(self, formula):
        """应用通用规则"""
        for rule_group, patterns in self.fallback_rules.items():
            old_formula = formula
            for pattern, replacement in patterns.items():
                formula = re.sub(pattern, replacement, formula)

            if formula != old_formula:
                self.conversion_stats['by_layer']['fallback'] += 1
                self.conversion_stats['by_type'][rule_group] = self.conversion_stats['by_type'].get(rule_group, 0) + 1

        return formula

    def _log_conversion(self, original, converted):
        """记录转换日志"""
        self.conversion_stats['conversion_log'].append({
            'original': original,
            'converted': converted,
            'issues': self._detect_conversion_issues(original, converted)
        })

    def _detect_conversion_issues(self, original, converted):
        """检测转换问题"""
        issues = []

        # 检测未转换的siunitx命令
        if re.search(r'\\SI\s*\{', converted):
            issues.append('untranslated_SI_command')

        # 检测过长的单位名称
        if re.search(r'\\mathrm\{[a-z]{6,}\}', converted):
            issues.append('verbose_unit_name')

        # 检测可能需要缩写的单位
        long_units = re.findall(r'\\mathrm\{([a-z]{4,})\}', converted)
        if long_units:
            issues.append(f'potential_abbreviation_needed: {", ".join(long_units)}')

        # 检测未转换的自定义命令
        custom_commands = re.findall(r'\\([A-Z][a-zA-Z]+)', converted)
        if custom_commands:
            issues.append(f'untranslated_custom_commands: {", ".join(custom_commands)}')

        return issues

    def get_conversion_stats(self):
        """获取转换统计信息"""
        return {
            'summary': {
                'total_conversions': self.conversion_stats['total_conversions'],
                'successful_conversions': len([log for log in self.conversion_stats['conversion_log'] if log['original'] != log['converted']]),
                'issues_detected': len([log for log in self.conversion_stats['conversion_log'] if log['issues']]),
            },
            'by_layer': self.conversion_stats['by_layer'],
            'by_type': self.conversion_stats['by_type'],
            'common_issues': self._analyze_common_issues(),
            'conversion_examples': self.conversion_stats['conversion_log'][-10:] if self.conversion_stats['conversion_log'] else []
        }

    def _analyze_common_issues(self):
        """分析常见问题"""
        issue_counts = {}
        for log in self.conversion_stats['conversion_log']:
            for issue in log['issues']:
                issue_type = issue.split(':')[0]
                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1

        return sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)

    def print_conversion_report(self):
        """打印转换报告"""
        stats = self.get_conversion_stats()

        print("\n" + "="*60)
        print("LaTeX预处理转换报告")
        print("="*60)

        print(f"\n📊 总体统计:")
        print(f"  总处理公式数: {stats['summary']['total_conversions']}")
        print(f"  成功转换数: {stats['summary']['successful_conversions']}")
        print(f"  检测到问题数: {stats['summary']['issues_detected']}")

        if stats['summary']['total_conversions'] > 0:
            success_rate = stats['summary']['successful_conversions'] / stats['summary']['total_conversions'] * 100
            print(f"  转换成功率: {success_rate:.1f}%")

        print(f"\n🔄 分层转换统计:")
        for layer, count in stats['by_layer'].items():
            print(f"  {layer}层转换: {count}次")

        print(f"\n📋 转换类型统计:")
        for conv_type, count in sorted(stats['by_type'].items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {conv_type}: {count}次")

        if stats['common_issues']:
            print(f"\n⚠️  常见问题 (前5项):")
            for issue, count in stats['common_issues'][:5]:
                print(f"  {issue}: {count}次")

        if stats['conversion_examples']:
            print(f"\n💡 最近转换示例:")
            for example in stats['conversion_examples'][-3:]:
                print(f"  原始: {example['original'][:50]}...")
                print(f"  转换: {example['converted'][:50]}...")
                if example['issues']:
                    print(f"  问题: {', '.join(example['issues'][:2])}")
                print()

        print("="*60)

    def _fix_special_formats(self, formula):
        """修复特殊格式问题"""
        # 修复siunitx中的空格问题
        formula = re.sub(r'\\SI\s+\{', r'\\SI{', formula)

        # 修复错误的$$格式
        formula = re.sub(r'_(\d+)\$\$\^{([^}]+)}', r'_{\1}^{\2}', formula)

        # 修复\numberthis命令
        formula = re.sub(r'\\numberthis', r'', formula)

        # 修复\tag{'}命令
        formula = re.sub(r'\\tag\{\'?\}', r'', formula)

        return formula

    def _fix_math_environments(self, formula):
        """处理特殊数学环境问题"""
        # 修复复杂的align环境中的换行问题
        # 这里可以添加更多复杂的环境修复逻辑

        # 修复array环境的格式问题
        formula = re.sub(r'\\begin\{array\}\s*\{([^}]+)\}', r'\\begin{array}{\1}', formula)

        return formula

    # 旧的转换方法已被分层转换架构替代


class CacheManager:
    """持久化缓存管理器 - 与LaTeXPreprocessor协同工作"""

    def __init__(self, cache_dir, max_size_gb=5.0, cleanup_threshold=0.8):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_gb * 1024 * 1024 * 1024
        self.cleanup_threshold = cleanup_threshold
        self.metadata_file = self.cache_dir / 'cache_metadata.json'
        self.metadata = self._load_metadata()
        self.stats = {'hits': 0, 'misses': 0, 'saves': 0}

        # 预处理器缓存（内存中）
        self.preprocessor_cache = {}
        self.preprocessor_cache_size = CONFIG['optimization'].get('preprocessor_cache_size', 10000)

    def _load_metadata(self):
        """加载缓存元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载缓存元数据失败: {e}")
                return {}
        return {}

    def _save_metadata(self):
        """保存缓存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存元数据失败: {e}")

    def _get_cache_path(self, formula_hash):
        """获取缓存文件路径"""
        # 使用两级目录结构避免单目录文件过多
        return self.cache_dir / formula_hash[:2] / f"{formula_hash}.png"

    def _get_formula_hash(self, original_formula, preprocessed_formula):
        """计算公式的MD5哈希（基于预处理后的公式）"""
        # 使用预处理后的公式计算哈希，确保缓存一致性
        normalized_formula = ' '.join(preprocessed_formula.strip().split())
        # 同时包含原始公式信息，用于调试
        cache_key = f"{normalized_formula}|{original_formula[:50]}"
        return hashlib.md5(cache_key.encode('utf-8')).hexdigest()

    def get_cached_image(self, original_formula, preprocessed_formula):
        """获取缓存的图像"""
        if not CONFIG['optimization']['enable_cache']:
            return None

        formula_hash = self._get_formula_hash(original_formula, preprocessed_formula)
        cache_path = self._get_cache_path(formula_hash)

        if cache_path.exists():
            try:
                # 更新访问时间
                self.metadata[formula_hash] = {
                    'access_time': time.time(),
                    'original_formula': original_formula[:100],
                    'preprocessed_formula': preprocessed_formula[:100],
                    'file_size': cache_path.stat().st_size,
                    'create_time': self.metadata.get(formula_hash, {}).get('create_time', time.time())
                }

                image = Image.open(cache_path)
                self.stats['hits'] += 1
                return image
            except Exception as e:
                # 缓存文件损坏，删除
                cache_path.unlink(missing_ok=True)
                if formula_hash in self.metadata:
                    del self.metadata[formula_hash]
                print(f"缓存文件损坏已删除: {cache_path}")

        self.stats['misses'] += 1
        return None

    def cache_image(self, original_formula, preprocessed_formula, image):
        """缓存图像"""
        if not CONFIG['optimization']['enable_cache']:
            return

        formula_hash = self._get_formula_hash(original_formula, preprocessed_formula)
        cache_path = self._get_cache_path(formula_hash)

        try:
            # 确保目录存在
            cache_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存图像
            image.save(cache_path)

            # 更新元数据
            current_time = time.time()
            self.metadata[formula_hash] = {
                'access_time': current_time,
                'create_time': current_time,
                'original_formula': original_formula[:100],
                'preprocessed_formula': preprocessed_formula[:100],
                'file_size': cache_path.stat().st_size
            }

            self.stats['saves'] += 1

            # 定期清理缓存和保存元数据
            if len(self.metadata) % 50 == 0:
                self._cleanup_cache_if_needed()
                self._save_metadata()

        except Exception as e:
            print(f"缓存图像失败: {e}")

    def get_preprocessed_formula(self, original_formula, preprocessor):
        """获取预处理后的公式（带缓存）"""
        if not CONFIG['optimization']['enable_preprocessor_cache']:
            return preprocessor.preprocess(original_formula)

        if original_formula in self.preprocessor_cache:
            return self.preprocessor_cache[original_formula]

        # 预处理缓存未命中
        preprocessed = preprocessor.preprocess(original_formula)

        # 缓存预处理结果（LRU策略）
        if len(self.preprocessor_cache) >= self.preprocessor_cache_size:
            # 删除最旧的条目
            oldest_key = next(iter(self.preprocessor_cache))
            del self.preprocessor_cache[oldest_key]

        self.preprocessor_cache[original_formula] = preprocessed
        return preprocessed

    def _cleanup_cache_if_needed(self):
        """根据需要清理缓存"""
        try:
            # 计算当前缓存大小
            total_size = sum(meta.get('file_size', 0) for meta in self.metadata.values())

            if total_size > self.max_size_bytes * self.cleanup_threshold:
                print(f"缓存大小 {total_size/1024/1024/1024:.2f}GB 超过阈值，开始清理...")

                # 按访问时间排序，删除最旧的缓存
                sorted_items = sorted(
                    self.metadata.items(),
                    key=lambda x: x[1].get('access_time', 0)
                )

                target_size = self.max_size_bytes * 0.6  # 清理到60%
                removed_count = 0

                for formula_hash, meta in sorted_items:
                    if total_size <= target_size:
                        break

                    cache_path = self._get_cache_path(formula_hash)
                    if cache_path.exists():
                        file_size = cache_path.stat().st_size
                        cache_path.unlink()
                        total_size -= file_size
                        removed_count += 1

                    del self.metadata[formula_hash]

                if removed_count > 0:
                    print(f"清理了 {removed_count} 个过期缓存文件")

        except Exception as e:
            print(f"清理缓存失败: {e}")

    def get_stats(self):
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0

        return {
            'hit_rate': hit_rate,
            'total_cached': len(self.metadata),
            'cache_size_mb': sum(meta.get('file_size', 0) for meta in self.metadata.values()) / 1024 / 1024,
            'preprocessor_cache_size': len(self.preprocessor_cache),
            **self.stats
        }

    def cleanup_and_save(self):
        """清理并保存元数据"""
        self._cleanup_cache_if_needed()
        self._save_metadata()


# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager():
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None and CONFIG['optimization']['enable_cache']:
        _cache_manager = CacheManager(
            CONFIG['optimization']['cache_dir'],
            CONFIG['optimization']['cache_max_size_gb'],
            CONFIG['optimization']['cache_cleanup_threshold']
        )
    return _cache_manager


class ErrorContext:
    """错误收集上下文管理器"""
    _instance = None
    
    def __init__(self):
        self.errors = []
        self.enabled = False
        self.config = None
        self.output_paths = {}
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def enable(self, config):
        self.enabled = True
        self.config = config
        self.errors.clear()
        self._setup_output_paths(config)
    
    def _setup_output_paths(self, config):
        """设置输出文件路径"""
        output_config = config['output']
        
        if 'error_log_paths' in output_config:
            self.output_paths = {
                'failed_formulas': output_config['error_log_paths']['failed_formulas'],
                'detailed_report': output_config['error_log_paths']['detailed_report']
            }
        else:
            # 默认路径
            output_dir = output_config.get('output_dir', './output')
            self.output_paths = {
                'failed_formulas': os.path.join(output_dir, 'failed_formulas.txt'),
                'detailed_report': os.path.join(output_dir, 'error_report.txt')
            }
        
        # 确保输出目录存在
        for path in self.output_paths.values():
            os.makedirs(os.path.dirname(path), exist_ok=True)
    
    def add_error(self, index, formula, stage, exception):
        """添加错误记录"""
        if not self.enabled:
            return
            
        error_detail = {
            'index': index,
            'formula': formula.strip(),
            'error_type': self._classify_error(exception, stage),
            'error_message': str(exception),
            'stage': stage,
            'timestamp': time.time()
        }
        
        self.errors.append(error_detail)
        
        # 实时显示
        if self.config['processing']['error_display_mode'] in ['realtime', 'both']:
            self._print_realtime_error(error_detail)
    
    def _classify_error(self, exception, stage):
        """分类错误类型"""
        error_msg = str(exception)
        
        if stage == 'latex_compile':
            if 'Undefined control sequence' in error_msg:
                return 'latex_syntax_error'
            elif 'Missing' in error_msg:
                return 'latex_syntax_error'
            else:
                return 'latex_compile_error'
        elif stage == 'pdf_convert':
            return 'pdf_convert_error'
        else:
            return 'unknown_error'
    
    def _print_realtime_error(self, error_detail):
        """实时打印错误"""
        if self.config['processing']['error_detail_level'] == 'simple':
            print(f"❌ [{error_detail['index']:05d}] {error_detail['error_type']}")
        else:
            print(f"❌ 错误 #{error_detail['index']:05d}: {error_detail['formula'][:50]}...")
    
    def print_error_summary(self):
        """打印错误摘要"""
        if not self.errors:
            return
        
        print(f"\n📊 错误统计: {len(self.errors)}个")
        
        # 错误类型统计
        error_stats = {}
        for error in self.errors:
            error_type = error['error_type']
            error_stats[error_type] = error_stats.get(error_type, 0) + 1
        
        for error_type, count in error_stats.items():
            print(f"   {error_type}: {count}个")
    
    def save_error_logs(self):
        """保存错误日志"""
        if not self.errors:
            return
        
        try:
            # 保存失败公式列表
            with open(self.output_paths['failed_formulas'], 'w', encoding='utf-8') as f:
                for error in self.errors:
                    formula = error['formula']
                    if formula.startswith('$ ') and formula.endswith(' $'):
                        formula = formula[2:-2].strip()
                    f.write(formula + '\n')
            
            # 保存详细错误报告
            with open(self.output_paths['detailed_report'], 'w', encoding='utf-8') as f:
                f.write("=== LaTeX渲染错误报告 ===\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总错误数: {len(self.errors)}\n\n")
                
                for i, error in enumerate(self.errors, 1):
                    f.write(f"错误 #{i:04d}\n")
                    f.write(f"索引: {error['index']}\n")
                    f.write(f"公式: {error['formula']}\n")
                    f.write(f"类型: {error['error_type']}\n")
                    f.write(f"详情: {error['error_message']}\n")
                    f.write("-" * 30 + "\n\n")
            
            print(f"\n📝 错误日志已保存:")
            print(f"   失败公式: {self.output_paths['failed_formulas']}")
            print(f"   详细报告: {self.output_paths['detailed_report']}")
            
        except Exception as e:
            print(f"⚠️  保存错误日志失败: {e}")


class Latex:
    # 恢复原来的LaTeX文档模板
    BASE = r'''
\documentclass[varwidth]{standalone}
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{fontspec}
\usepackage{bm}
\usepackage{textcomp}
\usepackage{gensymb}
\usepackage[active,tightpage,displaymath,textmath]{preview}
\setmainfont{%s}
\begin{document}
\thispagestyle{empty}
%s
\end{document}
'''

    def __init__(self, math, dpi=250, font='Latin Modern Math'):
        '''初始化LaTeX渲染器

        Args:
            math (list): LaTeX数学公式列表，每个元素是一个公式字符串
            dpi (int): 输出图像的分辨率，默认250DPI
            font (str): 数学字体名称，默认使用Latin Modern Math
        '''
        # 初始化预处理器和缓存管理器
        self.preprocessor = LaTeXPreprocessor()
        self.cache_manager = get_cache_manager()
        # 将预处理器实例保存到类属性，以便统计报告
        Latex.global_preprocessor = self.preprocessor

        # 存储原始公式和预处理后的公式
        self.original_formulas = []
        self.math = []

        for formula in math:
            formula = formula.strip()
            if not formula:
                continue

            self.original_formulas.append(formula)

            # 预处理公式，转换不兼容的命令（带缓存）
            if self.cache_manager:
                preprocessed_formula = self.cache_manager.get_preprocessed_formula(formula, self.preprocessor)
            else:
                preprocessed_formula = self.preprocessor.preprocess(formula)

            # 检查是否已经有数学环境（使用预处理后的公式）
            if (preprocessed_formula.startswith('$') and preprocessed_formula.endswith('$')) or \
               preprocessed_formula.startswith('\\[') or \
               preprocessed_formula.startswith('\\('):
                # 已经有数学环境，直接使用
                self.math.append(preprocessed_formula)
            elif preprocessed_formula.startswith('\\begin{'):
                # 检查是否是独立的数学环境
                math_environments = ['equation', 'equation*', 'align', 'align*', 'gather', 'gather*',
                                   'multline', 'multline*', 'eqnarray', 'eqnarray*', 'displaymath']
                inner_environments = ['aligned', 'gathered', 'cases', 'matrix', 'pmatrix',
                                    'bmatrix', 'Bmatrix', 'vmatrix', 'Vmatrix', 'smallmatrix']
                display_only_environments = ['split']  # 只能在display math模式中使用的环境

                # 提取环境名称
                import re
                env_match = re.match(r'\\begin\{([^}]+)\}', preprocessed_formula)
                if env_match:
                    env_name = env_match.group(1)
                    if env_name in math_environments:
                        # 独立的数学环境，直接使用
                        self.math.append(preprocessed_formula)
                    elif env_name in inner_environments:
                        # 内部环境，需要包装在数学模式中
                        self.math.append(f'$ {preprocessed_formula} $')
                    elif env_name in display_only_environments:
                        # 只能在display math模式中使用的环境，使用\[...\]包装
                        self.math.append(f'\\[ {preprocessed_formula} \\]')
                    else:
                        # 其他环境，直接使用
                        self.math.append(preprocessed_formula)
                else:
                    # 无法解析环境名，直接使用
                    self.math.append(preprocessed_formula)
            else:
                # 没有数学环境，使用$包围（和render.py保持一致）
                self.math.append(f'$ {preprocessed_formula} $')  # 注意空格，防止转义
        
        self.dpi = dpi    # 图像分辨率
        self.font = font  # 数学字体
        # 计算模板中公式插入位置的行号，用于错误定位
        self.prefix_line = self.BASE.split("\n").index("%s")

    @property
    def template(self):
        return self.BASE % (self.font, "%s")

    def write(self, return_bytes=False):
        '''将数学公式渲染为PNG图像'''
        try:
            # 创建临时工作目录和LaTeX文件
            workdir = tempfile.mkdtemp()
            fd, texfile = tempfile.mkstemp(suffix='.tex', dir=workdir)
            
            # 构建完整的LaTeX文档
            document = self.template % '\n'.join(self.math)

            # 将数学公式插入LaTeX模板并写入文件
            with os.fdopen(fd, 'w+', encoding='utf-8') as f:
                f.write(document)

            # 调用转换函数：LaTeX → PDF → PNG
            png, error_index = self.convert_file(
                texfile, workdir, return_bytes=return_bytes)
            
            return png, error_index

        except Exception as e:
            return [], list(range(len(self.math)))
        finally:
            # 清理临时LaTeX文件
            if 'texfile' in locals() and os.path.exists(texfile):
                try:
                    os.remove(texfile)
                except PermissionError:
                    pass

    def convert_file(self, infile, workdir, return_bytes=False):
        '''执行LaTeX编译和图像转换的核心函数'''
        infile = infile.replace('\\', '/')
        workdir = workdir.replace('\\', '/')
        base_name = os.path.splitext(os.path.basename(infile))[0]
        
        try:
            # ========== 第一步：LaTeX → PDF ==========
            cmd = 'xelatex -interaction nonstopmode -file-line-error -output-directory "%s" "%s"' % (
                workdir, infile)
            
            p = subprocess.Popen(
                cmd,
                shell=True,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
                
            sout_bytes, serr_bytes = p.communicate()

            if p.returncode != 0:
                return [], list(range(len(self.math)))
            
            # ========== 第二步：PDF → PNG ==========
            pdffile = os.path.join(workdir, base_name + '.pdf')
            pdffile = pdffile.replace('\\', '/')
            
            if not os.path.exists(pdffile):
                return [], list(range(len(self.math)))
            
            pngfile = os.path.join(workdir, base_name + '.png')
            pngfile = pngfile.replace('\\', '/')
            
            if sys.platform == 'win32':
                cmd = 'magick -density %i -colorspace %s "%s" -quality %i "%s"' % (
                    self.dpi,
                    CONFIG['render']['colorspace'],
                    pdffile,
                    CONFIG['render']['quality'],
                    pngfile,
                )
            else:
                cmd = 'convert -density %i -colorspace %s "%s" -quality %i "%s"' % (
                    self.dpi,
                    CONFIG['render']['colorspace'],
                    pdffile,
                    CONFIG['render']['quality'],
                    pngfile,
                )
            
            p = subprocess.Popen(
                cmd,
                shell=True,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            sout_bytes, serr_bytes = p.communicate()
            
            if p.returncode != 0:
                return [], list(range(len(self.math)))
            
            # 检查生成的PNG文件
            if len(self.math) > 1:
                png_files = [pngfile.replace('.png', '')+'-%i.png' % i for i in range(len(self.math))]
            else:
                png_files = [pngfile]
            
            existing_files = [pf for pf in png_files if os.path.exists(pf)]
            
            # ========== 第三步：文件读取 ==========
            try:
                if return_bytes:
                    png = [open(pf, 'rb').read() for pf in existing_files]
                else:
                    png = existing_files
                
                return png, []
            except Exception as e:
                return [], list(range(len(self.math)))
            
        except Exception as e:
            return [], list(range(len(self.math)))


# 全局缓存字典，避免重复渲染相同的公式
__cache = {}


def tex2png(eq, **kwargs):
    '''将单个LaTeX公式转换为PNG（带缓存）
    
    Args:
        eq (str): LaTeX公式字符串
        **kwargs: 传递给Latex类的其他参数
        
    Returns:
        tuple: (PNG字节数据列表, 错误索引列表)
    '''
    if not eq in __cache:
        # 如果缓存中没有，则渲染并缓存结果
        __cache[eq] = Latex(eq, **kwargs).write(return_bytes=True)
    return __cache[eq]


def tex2pil(math, return_error_index=False, **kwargs):
    '''将LaTeX数学公式转换为PIL图像（带缓存支持）'''
    cache_manager = get_cache_manager()

    try:
        # 如果启用缓存且为单个公式，先检查缓存
        if cache_manager and len(math) == 1:
            # 预处理公式以计算缓存键
            preprocessor = LaTeXPreprocessor()
            original_formula = math[0]
            preprocessed_formula = cache_manager.get_preprocessed_formula(original_formula, preprocessor)

            # 检查缓存
            cached_image = cache_manager.get_cached_image(original_formula, preprocessed_formula)
            if cached_image is not None:
                if return_error_index:
                    return [cached_image], []
                else:
                    return [cached_image]

        # 缓存未命中或多个公式，进行渲染
        latex = Latex(math, **kwargs)
        pngs, error_index = latex.write()

        # 转换为PIL图像对象
        images = []
        for i, png_path in enumerate(pngs):
            try:
                img = Image.open(png_path)
                images.append(img)

                # 缓存成功的结果
                if cache_manager and i < len(latex.original_formulas):
                    original_formula = latex.original_formulas[i]
                    preprocessed_formula = latex.math[i]
                    cache_manager.cache_image(original_formula, preprocessed_formula, img)

            except Exception as e:
                continue

        if return_error_index:
            return images, error_index
        else:
            return images

    except Exception as e:
        if return_error_index:
            return [], list(range(len(math)))
        else:
            return []



def extract(text, expression=None):
    """使用正则表达式从文本中提取信息"""
    try:
        # 添加空值检查
        if text is None or expression is None:
            return [], False
            
        pattern = re.compile(expression)
        results = re.findall(pattern, text)
        return results, True if len(results) != 0 else False
    except Exception as e:
        print(f"[调试] extract函数异常: {e}")
        return [], False


def auto_generate_prefix_from_output_dir(output_dir):
    """从输出目录路径自动生成前缀"""
    if not output_dir:
        return None
    
    # 标准化路径分隔符
    path = output_dir.replace('\\', '/').strip('/')
    
    # 分割路径
    parts = [p for p in path.split('/') if p]
    
    if not parts:
        return None
    
    # 提取有意义的部分
    meaningful_parts = []
    
    for part in reversed(parts):  # 从后往前遍历
        # 跳过常见的无意义目录名
        if part.lower() in ['images', 'img', 'pics', 'pictures', 'output', 'out', 'data', 'dataset']:
            continue
        
        # 提取数字和字母组合
        clean_part = re.sub(r'[^\w\d]', '_', part)
        if clean_part and not clean_part.isdigit():
            meaningful_parts.append(clean_part)
        
        # 最多取2个有意义的部分
        if len(meaningful_parts) >= 2:
            break
    
    if meaningful_parts:
        return '_'.join(reversed(meaningful_parts))
    else:
        # 如果没有找到有意义的部分，使用最后一个非images目录
        for part in reversed(parts):
            if part.lower() not in ['images', 'img', 'pics', 'pictures']:
                return re.sub(r'[^\w\d]', '_', part)
        
        return None


def generate_filename(formula, index, format_type='index', prefix_config=None, output_dir=None):
    """生成输出文件名（简化版本）"""
    # 生成基础文件名
    if format_type == 'index':
        base_name = f"{index:06d}"
    elif format_type == 'hash':
        base_name = hashlib.md5(formula.encode()).hexdigest()[:8]
    elif format_type == 'formula':
        # 清理公式中的特殊字符，生成安全的文件名
        safe_name = re.sub(r'[^\w\s-]', '', formula)[:50]
        base_name = re.sub(r'[-\s]+', '_', safe_name)
    else:
        base_name = f"{index:06d}"
    
    # 处理前缀（只在第一次时输出信息）
    prefix = None
    if prefix_config == 'auto':
        prefix = auto_generate_prefix_from_output_dir(output_dir)
        # 只在第一次生成时输出信息
        if index == 0:
            print(f"使用前缀: {prefix}")
    elif prefix_config == 'custom':
        prefix = CONFIG['output'].get('custom_prefix', None)
    elif prefix_config and isinstance(prefix_config, str):
        # 直接指定前缀字符串
        prefix = prefix_config
    
    # 添加前缀
    if prefix and prefix.strip():
        return f"{prefix}_{base_name}"
    else:
        return base_name


def validate_formula(formula):
    """验证公式是否包含可能导致问题的内容"""
    problematic_patterns = [
        r'\\bm\{',  # 需要bm包
        r'\\text\{', # 需要amsmath包
        r'\\boldsymbol\{', # 需要amsmath包
    ]
    
    issues = []
    for pattern in problematic_patterns:
        if re.search(pattern, formula):
            issues.append(pattern)
    
    return issues

def debug_formula(formula, index):
    """调试单个公式"""
    print(f"[调试] 公式 {index}:")
    print(f"  原始内容: {repr(formula)}")
    print(f"  长度: {len(formula)}")
    print(f"  是否包含非ASCII: {any(ord(c) > 127 for c in formula)}")
    
    # 检查是否已经有数学环境
    has_math_env = (
        (formula.startswith('$') and formula.endswith('$')) or
        formula.startswith('\\begin{') or
        formula.startswith('\\[') or
        formula.startswith('\\(')
    )
    print(f"  是否有数学环境: {has_math_env}")

def recursive_batch_render(formulas, batch_size=50, max_depth=3, current_depth=0,
                          base_indices=None, dpi=250, font='Latin Modern Math',
                          progress_desc="渲染批次"):
    """递归分批渲染策略 - 智能处理失败情况，最大化利用预处理器效果"""
    if base_indices is None:
        base_indices = list(range(len(formulas)))

    error_ctx = ErrorContext.get_instance()
    cache_manager = get_cache_manager()
    show_progress = CONFIG['processing'].get('show_progress', True)

    # 如果到达最小批次或最大深度，使用单个处理
    if batch_size <= CONFIG['optimization']['min_batch_size'] or current_depth >= max_depth:
        return _single_render_with_cache(formulas, base_indices, dpi, font, show_progress)

    # 首先检查缓存（如果启用）
    cached_results = []
    uncached_formulas = []
    uncached_indices = []

    if cache_manager:
        preprocessor = LaTeXPreprocessor()
        for i, formula in enumerate(formulas):
            preprocessed_formula = cache_manager.get_preprocessed_formula(formula, preprocessor)
            cached_image = cache_manager.get_cached_image(formula, preprocessed_formula)
            if cached_image is not None:
                cached_results.append((base_indices[i], cached_image))
            else:
                uncached_formulas.append(formula)
                uncached_indices.append(base_indices[i])
    else:
        uncached_formulas = formulas
        uncached_indices = base_indices

    # 如果全部命中缓存，直接返回
    if not uncached_formulas:
        result_images = [None] * len(formulas)
        for idx, img in cached_results:
            original_idx = base_indices.index(idx)
            result_images[original_idx] = img
        return result_images

    # 尝试批量处理未缓存的公式
    try:
        images, error_indices = tex2pil(uncached_formulas, return_error_index=True, dpi=dpi, font=font)

        # 计算错误率
        error_rate = len(error_indices) / len(uncached_formulas) if uncached_formulas else 0

        if error_rate == 0:
            # 全部成功，合并缓存结果和新结果
            all_results = cached_results.copy()
            all_results.extend([(uncached_indices[i], img) for i, img in enumerate(images)])

            # 转换为结果格式
            result_images = [None] * len(formulas)
            for idx, img in all_results:
                original_idx = base_indices.index(idx)
                result_images[original_idx] = img

            return result_images

        elif error_rate < CONFIG['optimization']['error_rate_threshold']:
            # 错误率较低，只对失败的公式递归处理
            success_results = cached_results.copy()

            # 处理成功的公式
            for i, image in enumerate(images):
                if i not in error_indices:
                    success_results.append((uncached_indices[i], image))

            # 递归处理失败的公式
            failed_formulas = [uncached_formulas[i] for i in error_indices]
            failed_indices = [uncached_indices[i] for i in error_indices]

            if failed_formulas:
                failed_results = recursive_batch_render(
                    failed_formulas,
                    max(1, batch_size // 2),
                    max_depth,
                    current_depth + 1,
                    failed_indices,
                    dpi, font,
                    f"递归处理-深度{current_depth+1}"
                )

                # 合并失败公式的处理结果
                for i, result in enumerate(failed_results):
                    if result is not None:
                        success_results.append((failed_indices[i], result))

            # 转换为结果格式
            result_images = [None] * len(formulas)
            for idx, img in success_results:
                original_idx = base_indices.index(idx)
                result_images[original_idx] = img

            return result_images

        else:
            # 错误率高，分割批次递归处理
            if len(uncached_formulas) <= 1:
                # 无法再分割，单个处理
                return _single_render_with_cache(formulas, base_indices, dpi, font, show_progress)

            # 分割为两部分
            mid = len(uncached_formulas) // 2

            left_formulas = uncached_formulas[:mid]
            left_indices = uncached_indices[:mid]
            right_formulas = uncached_formulas[mid:]
            right_indices = uncached_indices[mid:]

            # 递归处理两部分
            left_results = recursive_batch_render(
                left_formulas, max(1, batch_size // 2), max_depth, current_depth + 1,
                left_indices, dpi, font, f"左半部分-深度{current_depth+1}"
            )

            right_results = recursive_batch_render(
                right_formulas, max(1, batch_size // 2), max_depth, current_depth + 1,
                right_indices, dpi, font, f"右半部分-深度{current_depth+1}"
            )

            # 合并结果
            all_results = cached_results.copy()

            # 添加左半部分结果
            for i, result in enumerate(left_results):
                if result is not None:
                    all_results.append((left_indices[i], result))

            # 添加右半部分结果
            for i, result in enumerate(right_results):
                if result is not None:
                    all_results.append((right_indices[i], result))

            # 转换为结果格式
            result_images = [None] * len(formulas)
            for idx, img in all_results:
                original_idx = base_indices.index(idx)
                result_images[original_idx] = img

            return result_images

    except Exception as e:
        # 批量处理异常，分割处理
        if len(uncached_formulas) <= 1:
            return _single_render_with_cache(formulas, base_indices, dpi, font, show_progress)

        # 分割为两部分递归处理
        mid = len(uncached_formulas) // 2

        left_formulas = uncached_formulas[:mid]
        left_indices = uncached_indices[:mid]
        right_formulas = uncached_formulas[mid:]
        right_indices = uncached_indices[mid:]

        left_results = recursive_batch_render(
            left_formulas, max(1, batch_size // 2), max_depth, current_depth + 1,
            left_indices, dpi, font, f"异常恢复左-深度{current_depth+1}"
        )

        right_results = recursive_batch_render(
            right_formulas, max(1, batch_size // 2), max_depth, current_depth + 1,
            right_indices, dpi, font, f"异常恢复右-深度{current_depth+1}"
        )

        # 合并结果
        all_results = cached_results.copy()

        for i, result in enumerate(left_results):
            if result is not None:
                all_results.append((left_indices[i], result))

        for i, result in enumerate(right_results):
            if result is not None:
                all_results.append((right_indices[i], result))

        # 转换为结果格式
        result_images = [None] * len(formulas)
        for idx, img in all_results:
            original_idx = base_indices.index(idx)
            result_images[original_idx] = img

        return result_images


def _single_render_with_cache(formulas, base_indices, dpi, font, show_progress):
    """单个渲染（带缓存支持）"""
    cache_manager = get_cache_manager()
    error_ctx = ErrorContext.get_instance()
    results = []

    if show_progress and len(formulas) > 1:
        pbar = tqdm(zip(base_indices, formulas), total=len(formulas),
                   desc="单个渲染", unit="个", leave=False)
    else:
        pbar = zip(base_indices, formulas)

    for idx, formula in pbar:
        success = False

        # 首先检查缓存
        if cache_manager:
            preprocessor = LaTeXPreprocessor()
            preprocessed_formula = cache_manager.get_preprocessed_formula(formula, preprocessor)
            cached_image = cache_manager.get_cached_image(formula, preprocessed_formula)
            if cached_image is not None:
                results.append((idx, cached_image))
                success = True

        if not success:
            # 缓存未命中，尝试渲染
            try:
                images, error_indices = tex2pil([formula], return_error_index=True, dpi=dpi, font=font)

                if len(error_indices) == 0 and len(images) > 0:
                    image = images[0]
                    results.append((idx, image))
                    success = True

            except Exception as e:
                if error_ctx.enabled:
                    error_ctx.add_error(idx, formula, 'single_render', e)

        if not success and error_ctx.enabled:
            error_ctx.add_error(idx, formula, 'render_failed', Exception("渲染失败"))

    # 转换为结果格式
    result_images = [None] * len(formulas)
    for idx, img in results:
        original_idx = base_indices.index(idx)
        result_images[original_idx] = img

    return result_images


# 为了向后兼容，保留adaptive_batch_render函数
def adaptive_batch_render(formulas, initial_batch_size=50, min_batch_size=1, dpi=250, font='Latin Modern Math'):
    """向后兼容的适配器函数"""
    if CONFIG['optimization']['enable_recursive_batch']:
        return recursive_batch_render(
            formulas,
            batch_size=initial_batch_size,
            max_depth=CONFIG['optimization']['recursive_max_depth'],
            dpi=dpi,
            font=font
        )
    else:
        # 使用原来的逻辑（简化版）
        all_results = []
        error_ctx = ErrorContext.get_instance()

        for i in range(0, len(formulas), initial_batch_size):
            batch = formulas[i:i+initial_batch_size]
            batch_indices = list(range(i, min(i+initial_batch_size, len(formulas))))

            try:
                images, error_indices = tex2pil(batch, return_error_index=True, dpi=dpi, font=font)

                if len(error_indices) == 0 and len(images) > 0:
                    all_results.extend([(idx, img) for idx, img in zip(batch_indices, images)])
                else:
                    # 降级到单个处理
                    for idx, formula in zip(batch_indices, batch):
                        try:
                            single_images, single_errors = tex2pil([formula], return_error_index=True, dpi=dpi, font=font)
                            if len(single_errors) == 0 and len(single_images) > 0:
                                all_results.append((idx, single_images[0]))
                        except Exception as e:
                            if error_ctx.enabled:
                                error_ctx.add_error(idx, formula, 'single_render', e)

            except Exception as e:
                for idx, formula in zip(batch_indices, batch):
                    if error_ctx.enabled:
                        error_ctx.add_error(idx, formula, 'batch_error', e)

        # 转换结果格式
        result_images = [None] * len(formulas)
        for idx, img in all_results:
            if idx < len(result_images):
                result_images[idx] = img

        return result_images


def batch_render_formulas(formulas, output_dir, initial_batch_size=50):
    """批量渲染公式，新增错误收集功能"""
    verbose = CONFIG['processing'].get('verbose', False)
    show_progress = CONFIG['processing'].get('show_progress', True)
    
    print(f"开始渲染 {len(formulas)} 个公式...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 启用错误收集
    error_ctx = ErrorContext.get_instance()
    if CONFIG['output']['save_error_log']:
        error_ctx.enable(CONFIG)
    
    try:
        # 使用自适应批量渲染
        images = adaptive_batch_render(
            formulas, 
            initial_batch_size=initial_batch_size,
            min_batch_size=1,
            dpi=CONFIG['render']['dpi'], 
            font=CONFIG['render']['font']
        )
        
        # 保存成功的图像 - 添加进度条
        saved_count = 0
        
        if show_progress:
            save_pbar = tqdm(enumerate(images), total=len(images), desc="保存图像", unit="个")
        else:
            save_pbar = enumerate(images)
            
        for i, image in save_pbar:
            if image is not None:  # 成功渲染的图像
                try:
                    filename = generate_filename(formulas[i], i, CONFIG['output']['filename_format'])
                    output_path = os.path.join(output_dir, f"{filename}.png")
                    
                    # 检查是否跳过已存在文件
                    if CONFIG['processing']['skip_existing'] and os.path.exists(output_path):
                        if verbose:
                            print(f"跳过已存在文件: {output_path}")
                        saved_count += 1
                        continue
                    
                    image.save(output_path)
                    saved_count += 1
                    
                    if verbose:
                        print(f"已保存: {output_path}")
                    elif show_progress:
                        save_pbar.set_postfix({"已保存": saved_count})
                        
                except Exception as e:
                    print(f"保存图像失败 #{i}: {e}")
                    error_ctx.add_error(i, formulas[i], 'save_error', e)
        
        # 输出结果统计
        success_rate = saved_count / len(formulas) * 100
        error_count = len(error_ctx.errors) if error_ctx.enabled else 0

        print(f"\n✅ 渲染完成: {saved_count}/{len(formulas)} 个公式 ({success_rate:.1f}%)")
        if error_count > 0:
            print(f"❌ 失败: {error_count} 个公式")
        print(f"📁 保存位置: {output_dir}")

        # 显示性能统计
        if CONFIG['optimization']['enable_performance_stats']:
            cache_manager = get_cache_manager()
            if cache_manager and CONFIG['optimization']['show_cache_stats']:
                stats = cache_manager.get_stats()
                print(f"\n📊 缓存统计:")
                print(f"   命中率: {stats['hit_rate']:.1%}")
                print(f"   总缓存数量: {stats['total_cached']} 个")
                print(f"   缓存大小: {stats['cache_size_mb']:.1f} MB")
                print(f"   预处理缓存: {stats['preprocessor_cache_size']} 个")
                print(f"   命中次数: {stats['hits']}")
                print(f"   未命中次数: {stats['misses']}")

            # 显示预处理统计
            if CONFIG['optimization']['show_preprocessor_stats'] and hasattr(Latex, 'global_preprocessor'):
                preprocessor = Latex.global_preprocessor
                if preprocessor:
                    print(f"\n🔄 预处理统计:")
                    conv_stats = preprocessor.get_conversion_stats()
                    print(f"   总处理公式: {conv_stats['summary']['total_conversions']}")
                    print(f"   成功转换: {conv_stats['summary']['successful_conversions']}")
                    if conv_stats['summary']['total_conversions'] > 0:
                        conv_rate = conv_stats['summary']['successful_conversions'] / conv_stats['summary']['total_conversions'] * 100
                        print(f"   转换率: {conv_rate:.1f}%")

        # 处理错误日志
        if CONFIG['output']['save_error_log'] and error_ctx.errors:
            if CONFIG['processing']['error_display_mode'] in ['batch', 'both']:
                error_ctx.print_error_summary()
            error_ctx.save_error_logs()

        # 清理缓存并保存元数据
        cache_manager = get_cache_manager()
        if cache_manager:
            cache_manager.cleanup_and_save()

        return saved_count
        
    finally:
        if CONFIG['output']['save_error_log']:
            error_ctx.enabled = False

def main_with_config():
    """使用配置区域的参数运行程序"""
    print("=== LaTeX公式渲染工具 ===")
    
    # 确保输出目录存在
    os.makedirs(CONFIG['output']['output_dir'], exist_ok=True)
    
    try:
        if CONFIG['mode'] == 'batch':
            # 模式2: 批量处理文件中的公式
            formula_file = CONFIG['input']['formula_file']
            if not os.path.exists(formula_file):
                raise FileNotFoundError(f"公式文件不存在: {formula_file}")
            
            # 读取所有公式
            with open(formula_file, 'r', encoding='utf-8') as f:
                formulas = [line.strip() for line in f if line.strip()]
            
            print(f"读取了 {len(formulas)} 个公式")
            
            # 直接开始渲染，不进行预检查
            success_count = batch_render_formulas(
                formulas, 
                CONFIG['output']['output_dir'],
                initial_batch_size=CONFIG['processing']['batch_size']
            )
            
            print(f"完成！成功: {success_count}/{len(formulas)} ({success_count/len(formulas)*100:.1f}%)")

            # 显示预处理统计报告
            if hasattr(Latex, 'global_preprocessor') and CONFIG['processing']['verbose']:
                Latex.global_preprocessor.print_conversion_report()
            
        elif CONFIG['mode'] == 'single':
            # 模式1: 渲染单个公式（测试用）
            formula = CONFIG['input']['single_formula']
            print(f"渲染单个公式: {formula}")

            # 确保输出目录存在
            os.makedirs(CONFIG['output']['output_dir'], exist_ok=True)

            images, error_indices = tex2pil([formula],
                           return_error_index=True,
                           dpi=CONFIG['render']['dpi'],
                           font=CONFIG['render']['font'])

            if images and len(error_indices) == 0:
                filename = generate_filename(formula, 0, CONFIG['output']['filename_format'])
                output_path = os.path.join(CONFIG['output']['output_dir'], f"{filename}.png")
                images[0].save(output_path)
                print(f"已保存: {output_path}")
            else:
                print(f"❌ 渲染失败: {formula}")
                print(f"错误索引: {error_indices}")
                if CONFIG['processing']['verbose']:
                    print("尝试手动调试...")
            
        elif CONFIG['mode'] == 'list':
            # 模式3: 渲染配置中的公式列表
            formulas = CONFIG['input']['formula_list']
            print(f"渲染 {len(formulas)} 个配置公式...")
            
            # 启用错误收集
            error_ctx = ErrorContext.get_instance()
            if CONFIG['output']['save_error_log']:
                error_ctx.enable(CONFIG)
            
            try:
                # 使用自适应批量渲染，确保单个错误不影响其他公式
                images = adaptive_batch_render(
                    formulas,
                    initial_batch_size=len(formulas),  # 先尝试一次性处理
                    min_batch_size=1,
                    dpi=CONFIG['render']['dpi'], 
                    font=CONFIG['render']['font']
                )
                
                success_count = 0
                for i, image in enumerate(images):
                    if image is not None:  # 成功渲染的图像
                        try:
                            filename = generate_filename(formulas[i], i, CONFIG['output']['filename_format'])
                            output_path = os.path.join(CONFIG['output']['output_dir'], f"{filename}.png")
                            image.save(output_path)
                            success_count += 1
                            print(f"已保存: {output_path}")
                        except Exception as e:
                            print(f"保存图像失败 #{i}: {e}")
                            error_ctx.add_error(i, formulas[i], 'save_error', e)
                
                print(f"成功渲染 {success_count} 个公式")
                
                # 显示错误信息
                if CONFIG['output']['save_error_log'] and error_ctx.errors:
                    error_ctx.print_error_summary()
                    error_ctx.save_error_logs()

                # 显示预处理统计报告
                if hasattr(Latex, 'global_preprocessor'):
                    Latex.global_preprocessor.print_conversion_report()

            finally:
                if CONFIG['output']['save_error_log']:
                    error_ctx.enabled = False
        
        else:
            raise NotImplementedError(f"不支持的模式: {CONFIG['mode']}")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        if CONFIG['processing'].get('verbose', False):
            import traceback
            traceback.print_exc()


def test_optimization():
    """测试递归分批处理 + 持久化缓存优化效果"""
    print("=== 测试v3优化功能 ===")

    # 测试公式列表（包含重复公式测试缓存）
    test_formulas = [
        r'x^2 + y^2 = z^2',
        r'\frac{a}{b} = \frac{c}{d}',
        r'\sum_{i=1}^{n} i = \frac{n(n+1)}{2}',
        r'\int_{0}^{1} x^2 dx = \frac{1}{3}',
        r'\lim_{x \to 0} \frac{\sin x}{x} = 1',
        r'x^2 + y^2 = z^2',  # 重复公式，测试缓存
        r'\alpha + \beta = \gamma',
        r'\frac{a}{b} = \frac{c}{d}',  # 重复公式，测试缓存
        r'\SI{5}{\nano\meter}',  # 测试预处理器
        r'\Weightk',  # 测试自定义命令预处理
        r'\si{\watt/\meter}',  # 测试复合单位预处理
    ]

    print(f"测试 {len(test_formulas)} 个公式（包含重复公式和预处理测试）")

    # 临时修改配置为测试模式
    original_config = CONFIG.copy()
    CONFIG['mode'] = 'list'
    CONFIG['input']['formula_list'] = test_formulas
    CONFIG['output']['output_dir'] = r'./test_v3_optimization'
    CONFIG['optimization']['enable_cache'] = True
    CONFIG['optimization']['enable_recursive_batch'] = True
    CONFIG['optimization']['enable_preprocessor_cache'] = True
    CONFIG['optimization']['enable_performance_stats'] = True
    CONFIG['optimization']['show_cache_stats'] = True
    CONFIG['optimization']['show_preprocessor_stats'] = True
    CONFIG['processing']['show_progress'] = True
    CONFIG['processing']['verbose'] = True

    try:
        # 运行测试
        main_with_config()

        print(f"\n🎯 测试完成！")
        print(f"   预期效果:")
        print(f"   - 2个重复公式应该命中缓存")
        print(f"   - 预处理器应该转换siunitx和自定义命令")
        print(f"   - 递归分批应该智能处理失败情况")

    finally:
        # 恢复原始配置
        CONFIG.clear()
        CONFIG.update(original_config)


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_optimization()
    else:
        main_with_config()
