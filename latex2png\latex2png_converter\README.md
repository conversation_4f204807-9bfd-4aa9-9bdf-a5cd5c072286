# LaTeX to PNG 转换器

高效的LaTeX公式转PNG图像的批量转换工具，用于扩充公式识别数据集。

## 功能特性

- **批量处理**：支持千万级LaTeX公式的批量转换
- **多引擎渲染**：TeX Live主引擎 + SymPy备用引擎
- **智能预处理**：三级LaTeX代码规范化处理
- **错误分类**：自动分类和存储处理错误
- **高性能**：多进程并发处理，目标速度≥2000张/分钟
- **容错机制**：递归分批处理，单点失败不影响整体

## 快速开始

### 环境要求

- Python 3.8+
- TeX Live 发行版（推荐）或 MiKTeX
- 32GB+ 内存（推荐）
- 多核CPU

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd latex2png_converter

# 安装依赖
pip install -r requirements.txt

# 或者直接安装
pip install -e .
```

### 基本使用

```bash
# 基本用法
python main.py input.txt --output-dir ./output

# 指定配置文件
python main.py input.txt --config config.yaml --output-dir ./output

# 调整并发数和批次大小
python main.py input.txt --max-workers 20 --batch-size 3000
```

### 输入格式

输入文件为UTF-8编码的文本文件，每行包含一个LaTeX公式：

```
x^2 + y^2 = z^2
\frac{1}{2}\pi r^2
\int_0^\infty e^{-x} dx
\sum_{i=1}^n i = \frac{n(n+1)}{2}
```

### 输出格式

```
output/
├── images/                 # PNG图像文件
│   ├── math_0000001.png
│   ├── math_0000002.png
│   └── ...
├── mapping.json           # 图像文件名与LaTeX代码的映射
├── processing_statistics.json  # 处理统计信息
└── error_logs/            # 错误日志
    ├── latex_syntax_errors.txt
    ├── system_render_errors.txt
    └── unknown_errors.txt
```

## 配置说明

### 渲染配置

```yaml
render:
  dpi: 300                    # 图像DPI
  transparent_background: true # 背景透明
  font_family: "Computer Modern"
  font_size: 12
  image_format: "png"
```

### 处理配置

```yaml
process:
  max_workers: 16            # 最大并发数
  batch_size: 2000          # 批次大小
  timeout_seconds: 30       # 单项超时时间
  retry_attempts: 2         # 重试次数
```

## 性能指标

- **处理速度**：≥2000张图像/分钟
- **渲染成功率**：≥99.9%（排除LaTeX编码错误）
- **内存使用**：≤24GB（32GB系统）
- **并发支持**：16-20个进程

## 开发指南

### 项目结构

```
latex2png_converter/
├── main.py                 # 主程序入口
├── cli_parser.py          # 命令行参数解析
├── core/                  # 核心处理模块
├── engines/               # 渲染引擎实现
├── preprocessing/         # 预处理模块
├── config/               # 配置管理
├── utils/                # 工具函数
├── monitoring/           # 监控和日志
└── tests/                # 测试代码
```

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_core/

# 生成覆盖率报告
python -m pytest --cov=latex2png_converter tests/
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
