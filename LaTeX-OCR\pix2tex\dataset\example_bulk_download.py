#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量下载arXiv论文的使用示例
演示如何配置和使用新的bulk模式获取10000篇physics论文
"""

import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入arxiv模块
from arxiv import CONFIG, BULK_CONFIG, main_with_config

def setup_physics_10k_download():
    """配置下载10000篇physics论文"""
    print("=== 配置physics领域10000篇论文下载 ===")
    
    # 基础配置
    CONFIG['mode'] = 'bulk'  # 使用批量下载模式
    CONFIG['output_dir'] = r'D:\formula_ge_dataset\arxiv_physics_10k'  # 输出目录
    CONFIG['save_papers'] = None  # 不保存论文文件，节省空间
    CONFIG['use_demacro'] = False  # 不使用demacro，提高速度
    CONFIG['verbose'] = True  # 显示详细信息
    
    # 批量下载配置
    BULK_CONFIG['target_paper_count'] = 10000  # 目标论文数量
    BULK_CONFIG['primary_category'] = 'physics'  # 主要分类
    BULK_CONFIG['batch_size'] = 2000  # 每批次最大论文数
    BULK_CONFIG['download_delay'] = 3.5  # 下载间隔(秒)
    BULK_CONFIG['batch_delay'] = 10  # 批次间延迟(秒)
    BULK_CONFIG['max_retries'] = 5  # 最大重试次数
    
    # 搜索策略配置
    BULK_CONFIG['enable_subcategories'] = True  # 启用子分类搜索
    BULK_CONFIG['monthly_search'] = True  # 按月搜索，获取更多论文
    BULK_CONFIG['enable_deduplication'] = True  # 启用去重
    
    # 搜索年份范围 (从新到旧)
    BULK_CONFIG['search_years'] = [
        2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015,
        2014, 2013, 2012, 2011, 2010, 2009, 2008, 2007
    ]
    
    print("配置完成！")
    print(f"目标: {BULK_CONFIG['target_paper_count']} 篇 {BULK_CONFIG['primary_category']} 论文")
    print(f"输出: {CONFIG['output_dir']}")
    print(f"策略: {'子分类+' if BULK_CONFIG['enable_subcategories'] else ''}{'按月' if BULK_CONFIG['monthly_search'] else '按年'}搜索")
    print(f"年份: {BULK_CONFIG['search_years'][:5]}... (共{len(BULK_CONFIG['search_years'])}年)")

def setup_math_5k_download():
    """配置下载5000篇math论文"""
    print("=== 配置math领域5000篇论文下载 ===")
    
    # 基础配置
    CONFIG['mode'] = 'bulk'
    CONFIG['output_dir'] = r'D:\formula_ge_dataset\arxiv_math_5k'
    CONFIG['save_papers'] = None
    CONFIG['use_demacro'] = False
    CONFIG['verbose'] = True
    
    # 批量下载配置
    BULK_CONFIG['target_paper_count'] = 5000
    BULK_CONFIG['primary_category'] = 'math'
    BULK_CONFIG['batch_size'] = 2000
    BULK_CONFIG['download_delay'] = 3.5
    BULK_CONFIG['batch_delay'] = 10
    BULK_CONFIG['max_retries'] = 5
    
    # 搜索策略配置
    BULK_CONFIG['enable_subcategories'] = True
    BULK_CONFIG['monthly_search'] = True
    BULK_CONFIG['enable_deduplication'] = True
    
    # 数学论文相对较少，搜索更多年份
    BULK_CONFIG['search_years'] = list(range(2024, 1999, -1))  # 2024到2000年
    
    print("配置完成！")
    print(f"目标: {BULK_CONFIG['target_paper_count']} 篇 {BULK_CONFIG['primary_category']} 论文")
    print(f"输出: {CONFIG['output_dir']}")

def setup_custom_download(category, count, output_dir):
    """自定义配置下载"""
    print(f"=== 配置{category}领域{count}篇论文下载 ===")
    
    CONFIG['mode'] = 'bulk'
    CONFIG['output_dir'] = output_dir
    CONFIG['save_papers'] = None
    CONFIG['use_demacro'] = False
    CONFIG['verbose'] = True
    
    BULK_CONFIG['target_paper_count'] = count
    BULK_CONFIG['primary_category'] = category
    BULK_CONFIG['batch_size'] = min(2000, count)
    BULK_CONFIG['download_delay'] = 3.5
    BULK_CONFIG['batch_delay'] = 10
    BULK_CONFIG['max_retries'] = 5
    
    BULK_CONFIG['enable_subcategories'] = True
    BULK_CONFIG['monthly_search'] = True
    BULK_CONFIG['enable_deduplication'] = True
    BULK_CONFIG['search_years'] = list(range(2024, 2004, -1))  # 最近20年
    
    print("配置完成！")

def show_progress_info(output_dir):
    """显示下载进度信息"""
    import json
    
    progress_file = os.path.join(output_dir, 'progress.json')
    if not os.path.exists(progress_file):
        print(f"未找到进度文件: {progress_file}")
        return
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress = json.load(f)
        
        print(f"\n=== 下载进度信息 ===")
        print(f"目标论文数: {progress.get('target_papers', 0)}")
        print(f"已处理论文: {progress.get('total_papers_processed', 0)}")
        print(f"已提取公式: {progress.get('total_formulas_extracted', 0)}")
        print(f"完成率: {progress.get('completion_rate', 0)*100:.1f}%")
        print(f"URL进度: {progress.get('url_completion_rate', 0)*100:.1f}%")
        print(f"上次更新: {progress.get('last_update', '未知')}")
        
        config_snapshot = progress.get('config_snapshot', {})
        if config_snapshot:
            print(f"配置快照:")
            print(f"  分类: {config_snapshot.get('primary_category', '未知')}")
            print(f"  子分类: {'启用' if config_snapshot.get('enable_subcategories') else '禁用'}")
            print(f"  按月搜索: {'启用' if config_snapshot.get('monthly_search') else '禁用'}")
            
    except Exception as e:
        print(f"读取进度文件时出错: {e}")

def main():
    """主函数 - 选择下载配置"""
    print("ArXiv批量下载工具")
    print("请选择下载配置:")
    print("1. Physics 10000篇论文 (推荐)")
    print("2. Math 5000篇论文")
    print("3. 自定义配置")
    print("4. 查看进度信息")
    print("5. 直接开始下载 (使用当前配置)")
    
    try:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            setup_physics_10k_download()
            input("\n按回车键开始下载...")
            main_with_config()
            
        elif choice == '2':
            setup_math_5k_download()
            input("\n按回车键开始下载...")
            main_with_config()
            
        elif choice == '3':
            category = input("请输入分类 (如: physics, math, cs): ").strip()
            count = int(input("请输入目标论文数量: ").strip())
            output_dir = input("请输入输出目录: ").strip()
            
            setup_custom_download(category, count, output_dir)
            input("\n按回车键开始下载...")
            main_with_config()
            
        elif choice == '4':
            output_dir = input("请输入输出目录路径: ").strip()
            show_progress_info(output_dir)
            
        elif choice == '5':
            print("使用当前配置开始下载...")
            main_with_config()
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == '__main__':
    main()
