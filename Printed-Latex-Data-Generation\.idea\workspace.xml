<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="8030c594-c08b-445c-915f-dbe1a68ba51e" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Jupyter Notebooks/Data generation.ipynb" beforeDir="false" afterPath="$PROJECT_DIR$/Jupyter Notebooks/Data generation.ipynb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/log.txt" beforeDir="false" afterPath="$PROJECT_DIR$/log.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tex_to_svg.py" beforeDir="false" afterPath="$PROJECT_DIR$/tex_to_svg.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="hdfof6io" />
  </component>
  <component name="ProjectId" id="2JMkmfRAt98hoiLyWeq8oDETUD0" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.path.for.package.tslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8030c594-c08b-445c-915f-dbe1a68ba51e" name="Default Changelist" comment="" />
      <created>1671895500968</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1671895500968</updated>
      <workItem from="1671895503997" duration="15281000" />
      <workItem from="1671911026359" duration="2717000" />
      <workItem from="1671916093326" duration="1137000" />
      <workItem from="1671920607693" duration="189000" />
      <workItem from="1671981323151" duration="413000" />
      <workItem from="1672063838796" duration="34000" />
      <workItem from="1672074326602" duration="1000" />
      <workItem from="1672421120991" duration="59000" />
      <workItem from="1672458140206" duration="752000" />
      <workItem from="1672504480401" duration="473000" />
      <workItem from="1673388702606" duration="6825000" />
      <workItem from="1673551084580" duration="1001000" />
      <workItem from="1673575768460" duration="639000" />
      <workItem from="1674146722169" duration="74000" />
      <workItem from="1678913943923" duration="215000" />
      <workItem from="1680461718979" duration="20035000" />
      <workItem from="1696274936296" duration="3069000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
</project>