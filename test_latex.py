#!/usr/bin/env python3
import matplotlib
matplotlib.rcParams['text.usetex'] = True
matplotlib.rcParams['text.latex.preamble'] = r'''
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{bm}
\usepackage{siunitx}
\usepackage{xcolor}
'''
import matplotlib.pyplot as plt

try:
    fig, ax = plt.subplots()
    ax.text(0.5, 0.5, r'$\bm{u}$', transform=ax.transAxes)
    plt.savefig('test_bm.png')
    print('Test completed successfully')
except Exception as e:
    print(f'Error: {e}')
