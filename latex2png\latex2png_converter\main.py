#!/usr/bin/env python3
"""
LaTeX to PNG 转换器主程序
"""

import sys
import asyncio
import signal
from pathlib import Path
from typing import Optional

from cli_parser import parse_args, setup_logging_from_args, create_config_from_args, handle_special_commands
from core.batch_manager import BatchManager
from monitoring.logger import get_logger
from monitoring.progress_tracker import ProgressTracker
from utils.performance_utils import check_system_resources

# 全局变量用于优雅关闭
batch_manager: Optional[BatchManager] = None
current_task: Optional[asyncio.Task] = None

def signal_handler(signum, frame):
    """信号处理器，用于优雅关闭"""
    logger = get_logger(__name__)
    logger.info(f"接收到信号 {signum}，准备优雅关闭...")
    
    if current_task and not current_task.done():
        current_task.cancel()
    
    if batch_manager:
        try:
            # 停止性能监控
            batch_manager.performance_metrics.stop_monitoring()
            # 清理渲染引擎
            batch_manager.render_engine.cleanup_engines()
        except Exception as e:
            logger.warning(f"清理资源时出现警告: {e}")
    
    sys.exit(0)

async def test_engines(config) -> bool:
    """测试渲染引擎"""
    logger = get_logger(__name__)
    logger.info("开始测试渲染引擎...")
    
    try:
        from core.render_engine import RenderEngineManager
        
        engine_manager = RenderEngineManager(config.render)
        
        # 初始化引擎
        init_results = engine_manager.initialize_engines()
        
        # 测试引擎
        test_results = engine_manager.test_all_engines()
        
        # 输出结果
        print("\n渲染引擎测试结果:")
        print("=" * 50)
        
        for engine_name, available in init_results.items():
            test_passed = test_results.get(engine_name, False)
            status = "✓" if available and test_passed else "✗"
            
            print(f"{status} {engine_name.upper()} 引擎:")
            print(f"  - 可用性: {'是' if available else '否'}")
            print(f"  - 测试结果: {'通过' if test_passed else '失败'}")
        
        # 生成引擎报告
        report = engine_manager.create_engine_report()
        print(f"\n{report}")
        
        # 清理资源
        engine_manager.cleanup_engines()
        
        # 返回是否有可用引擎
        return any(init_results.values())
        
    except Exception as e:
        logger.error(f"测试引擎失败: {e}")
        return False

async def main_async():
    """异步主函数"""
    global batch_manager, current_task
    
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 设置日志
        setup_logging_from_args(args)
        logger = get_logger(__name__)
        
        logger.info("LaTeX to PNG 转换器启动")
        
        # 处理特殊命令
        if handle_special_commands(args):
            return 0
        
        # 创建配置
        config = create_config_from_args(args)
        
        # 测试引擎命令
        if args.test_engines:
            success = await test_engines(config)
            return 0 if success else 1
        
        # 检查系统资源
        logger.info("检查系统资源...")
        resource_check = check_system_resources(min_memory_gb=2.0, min_disk_gb=1.0)
        if not resource_check['overall_ok']:
            logger.warning("系统资源可能不足，但继续执行")
        
        # 试运行模式
        if args.dry_run:
            logger.info("试运行模式：将模拟处理过程但不生成实际图像")
            # 这里可以添加试运行逻辑
            return 0
        
        # 创建批量处理管理器
        logger.info("初始化批量处理管理器...")
        batch_manager = BatchManager(config)
        
        # 创建进度跟踪器
        progress_tracker = ProgressTracker("LaTeX转换")
        
        # 开始处理
        logger.info(f"开始处理文件: {args.input_file}")
        logger.info(f"输出目录: {args.output_dir}")
        
        # 创建处理任务
        current_task = asyncio.create_task(
            batch_manager.process_with_retry(
                args.input_file, 
                args.output_dir, 
                max_retries=config.process.retry_attempts
            )
        )
        
        # 等待处理完成
        result = await current_task
        
        # 输出最终结果
        success_rate = result.success_count / result.total_count if result.total_count > 0 else 0
        
        print(f"\n处理完成!")
        print(f"总处理数: {result.total_count:,}")
        print(f"成功数: {result.success_count:,}")
        print(f"失败数: {result.failed_count:,}")
        print(f"成功率: {success_rate:.2%}")
        print(f"总耗时: {result.processing_time:.2f} 秒")
        print(f"处理速率: {result.total_count/result.processing_time:.2f} 项/秒")
        print(f"输出目录: {args.output_dir}")
        
        # 生成详细报告
        if args.verbose:
            report = batch_manager.generate_processing_report()
            print(f"\n详细报告:\n{report}")
        
        # 根据成功率决定退出码
        if success_rate >= 0.95:
            logger.info("处理成功完成")
            return 0
        elif success_rate >= 0.80:
            logger.warning("处理完成，但成功率较低")
            return 1
        else:
            logger.error("处理完成，但成功率过低")
            return 2
            
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        return 130
    except Exception as e:
        logger.error(f"处理失败: {e}")
        if args.debug:
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return 1
    finally:
        # 清理资源
        if batch_manager:
            try:
                batch_manager.performance_metrics.stop_monitoring()
                batch_manager.render_engine.cleanup_engines()
            except Exception as e:
                logger.warning(f"清理资源时出现警告: {e}")

def main():
    """主函数入口"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本", file=sys.stderr)
        sys.exit(1)
    
    try:
        # 运行异步主函数
        exit_code = asyncio.run(main_async())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断", file=sys.stderr)
        sys.exit(130)
    except Exception as e:
        print(f"致命错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
