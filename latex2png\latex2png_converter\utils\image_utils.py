"""
图像处理工具函数
"""

from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from ..monitoring.logger import get_logger

def validate_image_quality(image_path: str, min_size_bytes: int = 100) -> bool:
    """
    验证图像质量
    
    Args:
        image_path: 图像文件路径
        min_size_bytes: 最小文件大小（字节）
        
    Returns:
        bool: 图像是否有效
    """
    logger = get_logger(__name__)
    
    try:
        path = Path(image_path)
        
        # 检查文件是否存在
        if not path.exists():
            logger.debug(f"图像文件不存在: {image_path}")
            return False
        
        # 检查文件大小
        file_size = path.stat().st_size
        if file_size < min_size_bytes:
            logger.debug(f"图像文件太小: {file_size} bytes < {min_size_bytes} bytes")
            return False
        
        # 检查文件扩展名
        if path.suffix.lower() not in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
            logger.debug(f"不支持的图像格式: {path.suffix}")
            return False
        
        # 尝试使用PIL验证图像（如果可用）
        try:
            from PIL import Image
            with Image.open(path) as img:
                # 验证图像可以正常打开
                img.verify()
                logger.debug(f"图像验证通过: {image_path}")
                return True
        except ImportError:
            # PIL不可用，使用基本验证
            logger.debug(f"PIL不可用，使用基本验证: {image_path}")
            return True
        except Exception as e:
            logger.debug(f"PIL图像验证失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"图像质量验证失败 {image_path}: {e}")
        return False

def get_image_info(image_path: str) -> Dict[str, Any]:
    """
    获取图像信息
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        Dict[str, Any]: 图像信息
    """
    logger = get_logger(__name__)
    
    try:
        path = Path(image_path)
        if not path.exists():
            return {'exists': False}
        
        info = {
            'exists': True,
            'path': str(path),
            'size_bytes': path.stat().st_size,
            'format': path.suffix.lower()
        }
        
        # 尝试获取详细图像信息
        try:
            from PIL import Image
            with Image.open(path) as img:
                info.update({
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format_detail': img.format,
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                })
        except ImportError:
            logger.debug("PIL不可用，无法获取详细图像信息")
        except Exception as e:
            logger.debug(f"获取图像详细信息失败: {e}")
        
        return info
        
    except Exception as e:
        logger.error(f"获取图像信息失败 {image_path}: {e}")
        return {'exists': False, 'error': str(e)}

def optimize_image_size(image_path: str, max_width: int = 2000, max_height: int = 2000, 
                       quality: int = 85) -> str:
    """
    优化图像大小（占位实现）
    
    Args:
        image_path: 图像文件路径
        max_width: 最大宽度
        max_height: 最大高度
        quality: 压缩质量
        
    Returns:
        str: 优化后的图像路径
    """
    logger = get_logger(__name__)
    logger.debug(f"图像大小优化（占位实现）: {image_path}")
    
    # 后续迭代将实现：
    # 1. 使用PIL调整图像大小
    # 2. 压缩图像质量
    # 3. 转换图像格式
    # 4. 批量优化处理
    
    return image_path

def create_thumbnail(image_path: str, thumbnail_size: Tuple[int, int] = (200, 200)) -> Optional[str]:
    """
    创建缩略图（占位实现）
    
    Args:
        image_path: 原图像路径
        thumbnail_size: 缩略图尺寸
        
    Returns:
        Optional[str]: 缩略图路径，失败时返回None
    """
    logger = get_logger(__name__)
    logger.debug(f"创建缩略图（占位实现）: {image_path}")
    
    # 后续迭代将实现缩略图生成功能
    return None

def batch_validate_images(image_paths: list, min_size_bytes: int = 100) -> Dict[str, bool]:
    """
    批量验证图像
    
    Args:
        image_paths: 图像路径列表
        min_size_bytes: 最小文件大小
        
    Returns:
        Dict[str, bool]: 验证结果映射
    """
    logger = get_logger(__name__)
    
    results = {}
    valid_count = 0
    
    for image_path in image_paths:
        is_valid = validate_image_quality(image_path, min_size_bytes)
        results[image_path] = is_valid
        if is_valid:
            valid_count += 1
    
    logger.info(f"批量图像验证完成: {valid_count}/{len(image_paths)} 有效")
    return results

def calculate_total_size(image_paths: list) -> Dict[str, Any]:
    """
    计算图像总大小
    
    Args:
        image_paths: 图像路径列表
        
    Returns:
        Dict[str, Any]: 大小统计信息
    """
    logger = get_logger(__name__)
    
    total_bytes = 0
    valid_files = 0
    invalid_files = 0
    
    for image_path in image_paths:
        try:
            path = Path(image_path)
            if path.exists() and path.is_file():
                total_bytes += path.stat().st_size
                valid_files += 1
            else:
                invalid_files += 1
        except Exception as e:
            logger.debug(f"计算文件大小失败 {image_path}: {e}")
            invalid_files += 1
    
    return {
        'total_files': len(image_paths),
        'valid_files': valid_files,
        'invalid_files': invalid_files,
        'total_bytes': total_bytes,
        'total_mb': total_bytes / (1024 * 1024),
        'total_gb': total_bytes / (1024 * 1024 * 1024),
        'average_size_bytes': total_bytes / valid_files if valid_files > 0 else 0
    }

def cleanup_invalid_images(image_paths: list, dry_run: bool = True) -> Dict[str, Any]:
    """
    清理无效图像文件
    
    Args:
        image_paths: 图像路径列表
        dry_run: 是否为试运行（不实际删除）
        
    Returns:
        Dict[str, Any]: 清理结果
    """
    logger = get_logger(__name__)
    
    invalid_images = []
    
    for image_path in image_paths:
        if not validate_image_quality(image_path):
            invalid_images.append(image_path)
    
    deleted_count = 0
    if not dry_run:
        for image_path in invalid_images:
            try:
                Path(image_path).unlink()
                deleted_count += 1
                logger.debug(f"删除无效图像: {image_path}")
            except Exception as e:
                logger.warning(f"删除文件失败 {image_path}: {e}")
    
    result = {
        'total_checked': len(image_paths),
        'invalid_found': len(invalid_images),
        'deleted_count': deleted_count,
        'dry_run': dry_run,
        'invalid_files': invalid_images
    }
    
    if dry_run:
        logger.info(f"试运行：发现 {len(invalid_images)} 个无效图像文件")
    else:
        logger.info(f"清理完成：删除了 {deleted_count}/{len(invalid_images)} 个无效图像文件")
    
    return result

def convert_image_format(image_path: str, target_format: str = 'PNG') -> Optional[str]:
    """
    转换图像格式（占位实现）
    
    Args:
        image_path: 原图像路径
        target_format: 目标格式
        
    Returns:
        Optional[str]: 转换后的图像路径
    """
    logger = get_logger(__name__)
    logger.debug(f"图像格式转换（占位实现）: {image_path} -> {target_format}")
    
    # 后续迭代将实现图像格式转换功能
    return None
