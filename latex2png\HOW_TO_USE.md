# LaTeX2PNG 使用指南

## 📁 文件结构说明（已清理）

```
latex2png/
├── latex2png_converter/           # 主程序目录
│   ├── config/                   # 配置文件目录
│   │   ├── user_config.yaml      # 标准配置
│   │   └── high_performance_config.yaml  # 高性能配置（默认）
│   ├── run_with_config.py        # 主程序入口（已集成高性能配置）
│   ├── preprocessing/            # 预处理模块
│   ├── engines/                  # 渲染引擎
│   ├── core/                     # 核心功能
│   └── utils/                    # 工具函数
└── HOW_TO_USE.md                 # 本使用说明
```

## 🎯 **重要：你需要修改的配置文件**

**程序默认使用高性能配置，你只需要修改：**

- **推荐** → 修改 `latex2png_converter/config/high_performance_config.yaml`
- **可选** → 修改 `latex2png_converter/config/user_config.yaml`（需要加 --standard 参数）

## 🚀 快速开始

### 1. 选择配置文件

**两个配置文件的区别**：

- **`user_config.yaml`** - 标准配置
  - 适用：日常使用，中小批量处理（<10万张）
  - 特点：平衡质量和速度，系统负载适中
  - 性能：约200张/秒

- **`high_performance_config.yaml`** - 高性能配置  
  - 适用：大批量处理（100万张级别）
  - 特点：优化系统资源使用，减少卡顿
  - 性能：约210张/秒，系统卡顿更少

### 2. 修改配置文件

**必须修改的配置项**：

```yaml
# 在你选择的配置文件中修改以下路径：
io:
  default_input_file: "你的输入文件路径.txt"    # LaTeX公式文件
  default_output_dir: "你的输出目录路径"        # 图片输出目录

system:
  texlive_path: "你的TeX Live安装路径"         # 如：D:\Program Files\texlive\2025\bin\windows
```

**可选修改的配置项**：

```yaml
render:
  dpi: 150                    # 图片质量（150=快速，300=高质量）
  background:
    type: "solid_color"       # 背景类型：solid_color/transparent
    color: "white"            # 背景颜色：white/black等

performance:
  max_workers: 12             # 并发进程数（建议8-16）
  batch_size: 1000            # 批次大小（建议500-2000）

output:
  image_prefix: "formula_"    # 图片文件名前缀
  image_digits: 6             # 文件编号位数
```

### 3. 运行程序

## 🚀 **运行程序（已简化）**

**默认使用高性能配置**：
```bash
# 使用高性能配置（默认，推荐大批量处理）
D:\miniforge3\envs\formula\python.exe latex2png_converter/run_with_config.py 你的输入文件.txt

# 使用标准配置
D:\miniforge3\envs\formula\python.exe latex2png_converter/run_with_config.py 你的输入文件.txt --standard
```

**示例**：
```bash
# 处理100万张公式（使用默认高性能配置）
D:\miniforge3\envs\formula\python.exe latex2png_converter/run_with_config.py D:\formula_ge_dataset\arxiv_25\math_arxiv.txt

# 指定输出目录
D:\miniforge3\envs\formula\python.exe latex2png_converter/run_with_config.py D:\data\formulas.txt -o D:\output
```

## 📊 性能预期

| 配置类型 | 处理速度 | 100万张耗时 | 适用场景 |
|---------|---------|------------|----------|
| 标准配置 | ~200张/秒 | ~1.4小时 | 日常使用 |
| 高性能配置 | ~210张/秒 | ~1.3小时 | 大批量处理 |

## 🔧 常见问题

### Q1: 系统卡顿怎么办？
A: 使用高性能配置，它已经优化了进程优先级和资源使用

### Q2: 处理速度慢怎么办？
A: 
1. 使用高性能配置
2. 降低DPI到100-150
3. 减少并发进程数到8-12
4. 使用SSD存储输出文件

### Q3: 内存不足怎么办？
A: 
1. 减少max_workers到8
2. 减少batch_size到500
3. 增加memory_limit_gb设置

### Q4: TeX Live路径问题？
A: 确保system.texlive_path指向正确的bin目录，如：
`D:\Program Files\texlive\2025\bin\windows`

## 📝 输入文件格式

输入文件应该是UTF-8编码的文本文件，每行一个LaTeX公式：

```
x^2 + y^2 = z^2
E = mc^2
\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}
\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}
```

## 📂 输出文件

程序会在输出目录创建：
- `images/` - PNG图片文件
- `mapping.json` - 公式与文件名的映射
- `error_logs/` - 错误日志（如果有失败的公式）

## 🎯 推荐使用流程

1. **小批量测试**：先用100-1000个公式测试配置是否正确
2. **选择配置**：根据数据量选择标准配置或高性能配置  
3. **大批量处理**：确认无误后处理完整数据集
4. **监控进度**：观察处理速度和系统资源使用情况

---

**重要提示**：
- 大批量处理建议使用高性能配置
- 处理过程中避免运行其他重负载程序
- 确保输出目录有足够的磁盘空间
- 建议使用SSD存储输出文件以提升性能
