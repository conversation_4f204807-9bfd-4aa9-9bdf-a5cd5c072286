"""
LaTeX预处理器
负责LaTeX代码的规范化处理
"""

from typing import Dict, List, Optional
from ..preprocessing.normalizers.level1_basic import Level1BasicNormalizer
from ..preprocessing.normalizers.level2_structure import Level2StructureNormalizer
from ..preprocessing.normalizers.level3_semantic import Level3SemanticNormalizer
from ..preprocessing.validator import LaTeXValidator
from ..monitoring.logger import get_logger

class LaTeXProcessor:
    """LaTeX预处理器"""

    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)

        # 初始化验证器
        self.validator = LaTeXValidator()

        # 初始化规范化器
        self.normalizers = {
            'level1': Level1BasicNormalizer(),
            'level2': Level2StructureNormalizer(),  # 占位实现
            'level3': Level3SemanticNormalizer()    # 占位实现
        }

        # 获取启用的规范化级别
        if hasattr(config, 'enabled_levels'):
            self.enabled_levels = config.enabled_levels
        else:
            # 默认只启用level1
            self.enabled_levels = ['level1']

        self.logger.info(f"LaTeX预处理器初始化完成，启用级别: {self.enabled_levels}")

    def normalize(self, latex_code: str) -> str:
        """规范化LaTeX代码"""
        try:
            # 输入验证
            if not latex_code or not latex_code.strip():
                raise ValueError("LaTeX代码不能为空")

            original_code = latex_code
            result = latex_code.strip()

            # 预验证（可选）
            if self._should_validate_input():
                is_valid, errors = self.validator.validate(result)
                if not is_valid:
                    self.logger.debug(f"输入验证发现问题: {errors[:3]}...")  # 只记录前3个错误

            # 应用启用的规范化器
            for level in self.enabled_levels:
                if level in self.normalizers:
                    normalizer = self.normalizers[level]
                    old_result = result
                    result = normalizer.normalize(result)
                    
                    if old_result != result:
                        self.logger.debug(f"应用 {level} 规范化: {original_code[:50]}...")

            # 后验证（可选）
            if self._should_validate_output():
                is_valid, errors = self.validator.validate(result)
                if not is_valid:
                    self.logger.warning(f"规范化后验证失败: {errors[:2]}...")
                    # 如果规范化后反而出现问题，返回原始代码
                    if self._is_worse_than_original(original_code, result):
                        self.logger.warning("规范化结果质量下降，使用原始代码")
                        return original_code

            return result

        except Exception as e:
            self.logger.warning(f"LaTeX规范化失败: {e}, 使用原始代码")
            return latex_code

    def set_enabled_levels(self, levels: List[str]):
        """设置启用的规范化级别"""
        valid_levels = [level for level in levels if level in self.normalizers]
        self.enabled_levels = valid_levels
        self.logger.info(f"更新启用规范化级别: {valid_levels}")

    def get_normalizer_info(self) -> Dict[str, Dict]:
        """获取规范化器信息"""
        info = {}
        for level, normalizer in self.normalizers.items():
            info[level] = {
                'class_name': normalizer.__class__.__name__,
                'enabled': level in self.enabled_levels,
                'description': normalizer.__doc__ or "无描述"
            }
        return info

    def validate_latex(self, latex_code: str) -> tuple:
        """验证LaTeX代码"""
        return self.validator.validate(latex_code)

    def is_simple_expression(self, latex_code: str) -> bool:
        """判断是否为简单表达式"""
        return self.validator.is_simple_expression(latex_code)

    def preprocess_batch(self, latex_codes: List[str]) -> List[str]:
        """批量预处理LaTeX代码"""
        results = []
        for i, code in enumerate(latex_codes):
            try:
                normalized = self.normalize(code)
                results.append(normalized)
            except Exception as e:
                self.logger.error(f"批量处理第 {i} 项失败: {e}")
                results.append(code)  # 失败时使用原始代码
        
        return results

    def get_processing_stats(self, original_codes: List[str], normalized_codes: List[str]) -> Dict:
        """获取处理统计信息"""
        if len(original_codes) != len(normalized_codes):
            return {}

        stats = {
            'total_processed': len(original_codes),
            'changed_count': 0,
            'unchanged_count': 0,
            'average_length_change': 0,
            'validation_passed': 0,
            'validation_failed': 0
        }

        length_changes = []
        
        for orig, norm in zip(original_codes, normalized_codes):
            if orig != norm:
                stats['changed_count'] += 1
                length_changes.append(len(norm) - len(orig))
            else:
                stats['unchanged_count'] += 1
            
            # 验证规范化结果
            is_valid, _ = self.validator.validate(norm)
            if is_valid:
                stats['validation_passed'] += 1
            else:
                stats['validation_failed'] += 1

        if length_changes:
            stats['average_length_change'] = sum(length_changes) / len(length_changes)

        return stats

    def _should_validate_input(self) -> bool:
        """是否应该验证输入"""
        # 可以根据配置决定是否验证输入
        return getattr(self.config, 'validate_input', False)

    def _should_validate_output(self) -> bool:
        """是否应该验证输出"""
        # 可以根据配置决定是否验证输出
        return getattr(self.config, 'validate_output', False)

    def _is_worse_than_original(self, original: str, normalized: str) -> bool:
        """判断规范化结果是否比原始代码更差"""
        try:
            # 简单的质量评估：比较验证错误数量
            _, orig_errors = self.validator.validate(original)
            _, norm_errors = self.validator.validate(normalized)
            
            # 如果规范化后错误更多，认为质量下降
            return len(norm_errors) > len(orig_errors)
        except Exception:
            return False

    def create_processing_report(self, original_codes: List[str], normalized_codes: List[str]) -> str:
        """创建处理报告"""
        stats = self.get_processing_stats(original_codes, normalized_codes)
        
        if not stats:
            return "无法生成处理报告：输入数据不匹配"

        report = f"""
LaTeX预处理报告:
===============

基本统计:
- 总处理数: {stats['total_processed']}
- 发生变化: {stats['changed_count']} ({stats['changed_count']/stats['total_processed']*100:.1f}%)
- 未发生变化: {stats['unchanged_count']} ({stats['unchanged_count']/stats['total_processed']*100:.1f}%)
- 平均长度变化: {stats['average_length_change']:.1f} 字符

验证结果:
- 验证通过: {stats['validation_passed']} ({stats['validation_passed']/stats['total_processed']*100:.1f}%)
- 验证失败: {stats['validation_failed']} ({stats['validation_failed']/stats['total_processed']*100:.1f}%)

启用的规范化级别: {', '.join(self.enabled_levels)}
        """
        
        return report.strip()
