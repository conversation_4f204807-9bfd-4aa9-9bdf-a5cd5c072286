{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\" style=\"margin-top: 1em;\"><ul class=\"toc-item\"><li><span><a href=\"#Visualization\" data-toc-modified-id=\"Visualization-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Visualization</a></span><ul class=\"toc-item\"><li><span><a href=\"#Preparation\" data-toc-modified-id=\"Preparation-1.1\"><span class=\"toc-item-num\">1.1&nbsp;&nbsp;</span>Preparation</a></span></li><li><span><a href=\"#Show-Image-and-Predict-LaTeX\" data-toc-modified-id=\"Show-Image-and-Predict-LaTeX-1.2\"><span class=\"toc-item-num\">1.2&nbsp;&nbsp;</span>Show Image and Predict LaTeX</a></span></li><li><span><a href=\"#Show-Attention-Slices-over-Image-correspond-to-LaTeX-Symbols\" data-toc-modified-id=\"Show-Attention-Slices-over-Image-correspond-to-LaTeX-Symbols-1.3\"><span class=\"toc-item-num\">1.3&nbsp;&nbsp;</span>Show Attention Slices over Image correspond to LaTeX Symbols</a></span></li><li><span><a href=\"#Save-Attention-Slices-as-GIF\" data-toc-modified-id=\"Save-Attention-Slices-as-GIF-1.4\"><span class=\"toc-item-num\">1.4&nbsp;&nbsp;</span>Save Attention Slices as GIF</a></span></li></ul></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualization"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Preparation\n", "\n", "prepare libs and load prebuild model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T13:40:53.877869Z", "start_time": "2019-03-09T13:40:45.976097Z"}, "hidden": true}, "outputs": [], "source": ["from scipy.misc import imread\n", "import PIL\n", "import os\n", "import PIL.Image as PILImage\n", "import numpy as np\n", "from IPython.display import display, Math, Latex, Image\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "from model.img2seq import Img2SeqModel\n", "from model.utils.general import Config, run\n", "from model.utils.text import Vocab\n", "from model.utils.image import greyscale, crop_image, pad_image, downsample_image, TIMEOUT\n", "import model.components.attention_mechanism"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T13:41:41.613760Z", "start_time": "2019-03-09T13:41:39.970210Z"}, "hidden": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["checkpoint ./results/full/model_weights/model.cpkt-24\n", "INFO:tensorflow:Restoring parameters from ./results/full/model_weights/model.cpkt-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Restoring parameters from ./results/full/model_weights/model.cpkt-24\n"]}, {"name": "stdout", "output_type": "stream", "text": ["!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! find a checkpoint, load epoch 24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["- done.\n"]}], "source": ["# restore config and model\n", "dir_output = \"./results/full/\"\n", "config_vocab = Config(dir_output + \"vocab.json\")\n", "config_model = Config(dir_output + \"model.json\")\n", "vocab = Vocab(config_vocab)\n", "\n", "def clear_global_attention_slice_stack():\n", "    '''\n", "    这是 attention 的全局变量\n", "    务必在调用 img2SeqModel.predict() 之前把 attention slices 栈清空\n", "    不然每预测一次，各自不同公式的 attention slices 会堆在一起\n", "    '''\n", "    model.components.attention_mechanism.ctx_vector = []\n", "\n", "clear_global_attention_slice_stack()\n", "img2SeqModel = Img2SeqModel(config_model, dir_output, vocab)\n", "img2SeqModel.build_pred()\n", "# img2SeqModel.restore_session(dir_output + \"model_weights/model.cpkt\")"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Show Image and Predict LaTeX\n", "\n", "replace `img_path` below with any image path you need to predict."]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T14:13:43.118843Z", "start_time": "2019-03-09T14:13:42.775965Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image path and shape:\n", "\n", "data/images_test/6.png (40, 240, 3)\n", "\n", "\n", "the image to predict:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/pyworks/env35/lib/python3.5/site-packages/ipykernel_launcher.py:8: DeprecationWarning: `imread` is deprecated!\n", "`imread` is deprecated in SciPy 1.0.0, and will be removed in 1.2.0.\n", "Use ``imageio.imread`` instead.\n", "  \n"]}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f6ff0434c50>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["predict LaTeX:\n", "\n", "\\left\\{ Q ^ { i } , Q ^ { j } \\right\\} = c ^ { i j } \\Gamma ^ { M } C P _ { M } + C \\epsilon ^ { i j } Z ,\n", "\n", "\n", "render LaTeX:\n"]}, {"data": {"text/latex": ["$$\\left\\{ Q ^ { i } , Q ^ { j } \\right\\} = c ^ { i j } \\Gamma ^ { M } C P _ { M } + C \\epsilon ^ { i j } Z ,$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["img_path = \"data/images_test/6.png\"\n", "\n", "import matplotlib.pyplot as plt #plt 用于显示图片\n", "import matplotlib.image as mpimg #mpimg 用于读取图片\n", "\n", "print(\"image path and shape:\\n\")\n", "lena = mpimg.imread(img_path) #读取目录下的图片，返回 np.array\n", "img = imread(img_path)\n", "img = greyscale(img)\n", "print(img_path, lena.shape)\n", "\n", "print(\"\\n\\nthe image to predict:\")\n", "plt.figure(num=1, figsize=(8,5),)\n", "plt.imshow(lena) # 显示图片\n", "plt.axis('off') # 不显示坐标轴\n", "plt.show()\n", "\n", "print(\"predict LaTeX:\\n\")\n", "clear_global_attention_slice_stack()\n", "hyps = img2SeqModel.predict(img)\n", "# hyps 是个列表，元素类型是 str, 元素个数等于 beam_search 的 bean_size\n", "# bean_size 在 `./configs/model.json` 里配置，预训练模型里取 2\n", "print(hyps[0])\n", "\n", "print(\"\\n\\nrender LaTeX:\")\n", "displayPreds = lambda LaTeX: display(Math(LaTeX))\n", "displayPreds(hyps[0])"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Show Attention Slices over Image correspond to LaTeX Symbols"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T13:46:41.917066Z", "start_time": "2019-03-09T13:46:41.910972Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attention slices： 51\n", "LaTeX symbols： 48\n", "两者相差 1 ，因为最后一个 attention 预测的字符是尾字符，只起截断作用，不计入 LaTeX\n"]}], "source": ["LaTeX_symbols_count = len(hyps[0].split(\" \"))\n", "attention_slices_count = len(model.components.attention_mechanism.ctx_vector)\n", "print(\"Attention slices：\", attention_slices_count)\n", "print(\"LaTeX symbols：\", LaTeX_symbols_count)\n", "print(\"两者相差 1 ，因为最后一个 attention 预测的字符是尾字符，只起截断作用，不计入 LaTeX\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T13:46:22.866575Z", "start_time": "2019-03-09T13:46:22.847187Z"}, "hidden": true}, "outputs": [], "source": ["# 模仿卷积层 encoder 缩放\n", "def getWH(img_w, img_h):\n", "    img_w, img_h = np.ceil(img_w / 2), np.ceil(img_h / 2)\n", "    img_w, img_h = np.ceil(img_w / 2), np.ceil(img_h / 2)\n", "    img_w, img_h = np.ceil(img_w / 2), np.ceil(img_h / 2)\n", "    img_w, img_h = np.ceil(img_w - 2), np.ceil(img_h - 2)\n", "    return int(img_w), int(img_h)\n", "\n", "def readImageAndShape(img_path):\n", "    lena = mpimg.imread(img_path)  # 读取目录下的图片，返回 np.array\n", "    img = imread(img_path)\n", "    img = greyscale(img)\n", "\n", "    img_w, img_h = lena.shape[1], lena.shape[0]\n", "\n", "    return img, img_w, img_h"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2019-03-05T07:37:10.010527Z", "start_time": "2019-03-05T07:37:09.958212Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["240 40 28 3\n"]}], "source": ["img_w, img_h = lena.shape[1], lena.shape[0]\n", "att_w, att_h = getWH(img_w, img_h)\n", "print(img_w, img_h, att_w, att_h)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-03-05T07:37:10.071368Z", "start_time": "2019-03-05T07:37:10.014330Z"}, "hidden": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一个 attention slice 扁平化的长度 84\n", "恰好等于 att_w x att_h = 84\n"]}], "source": ["print(\"第一个 attention slice 扁平化的长度\",\n", "      len(model.components.attention_mechanism.ctx_vector[0][0]))\n", "print(\"恰好等于 att_w x att_h =\", att_w * att_h)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2019-03-05T07:37:20.231531Z", "start_time": "2019-03-05T07:37:10.075090Z"}, "hidden": true, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/pyworks/env35/lib/python3.5/site-packages/matplotlib/pyplot.py:528: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`).\n", "  max_open_warning, RuntimeWarning)\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa3008e4a8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa3008e978>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2818c160>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa281b6d68>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2814cb00>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa281670b8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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********************************************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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2810d048>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2812fcf8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa280d8a20>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa280fc9e8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGqdJREFUeJztnWlwlMfVqJ+eRQgQjsEYjDFCCBBYBkKwsBEGA14SIAs3KeMgQ0wqXyBViSv+Kq5UFv+5VfmTqlR8c2/quw6+9WHjxAHssh2cxAYSAwGMI4MCITZCC0YggUERSwRom6Xvj3e66RnNSKNdGZ2nSqV35t3Oe7rf06dPn+5RWmsEQRCEf398Ay2AIAiC0DuIQRcEQcgQxKALgiBkCGLQBUEQMgQx6IIgCBmCGHRBEIQMoUcGXSm1XClVoZSqVkr9sLeEEgRBELqO6m4eulLKD1QCjwJ1wGGgRGt9ovfEEwRBENKlJx76fUC11vpjrXUbsA1Y1TtiCYIgCF0l0INzJwK1zuc64P6OThg7dqzOy8vrwS0HjsuXL3f5nJycnLSPzcrK6vL1BUEYGpSVlTVorW/v7LieGPS0UEptBDYC5Obmcvjw4T67V1fDR0qptI/dunVrV8Vh8eLFaR971113dfn6fUl3QnFd0Wd36IpMfS2LIPQnPp/vTFrH9eAe54BJzue7Yt/FobV+QWtdpLUuuv32ThsYQRAEoZv0xKAfBqYrpaYopbKANcBbvSNW36C1JhKJEIlEBuT+0WiUaDTaLe93MNKRPs1zZsqzCl0jVV3XWtt9Qu/T7ZCL1jqslHoK2AX4gc1a6496Iowp/L7qLiul8Pv9fXLtdPD5MivtvyN99tazhsNhwuGwvV8wGLTbibS2tgJePRo+fHiv3F/oHqnKXykl4bA+pEcxdK3128DbvSSLIAiC0AP6fFC0K5iWu7W1lVAoBMDIkSM7bdFN9y0ajdpjjYfgdu0qKir4+OOPAVi+fDmBQPce3/QkEkMKPp8vpayhUIhdu3YBUFBQQEFBgT23tbWV5uZmgEHvWXamT1Nuu3btoqCgAMA+a7qembmHz+fjyJEjHDx4EAC/3893v/tdwKsr7jW3bdtGba2XdDVy5Eg2bNjQYeaQW4bmXon7hponmaw+u/vS1UcoFGL37t0ATJ8+Pa6uV1RUcOrUKQBWrFiRcb3WgWZQGHRTWaqqqgDYsWMH+fn5AKxatapDQwk3K16yY8x3SilycnJ6XIHcip3sfqmMQTAYtGmMiefX19fzhz/8AYBx48bx2GOPdRhaGGiMDpPp08idk5MTJ3tXnsNtjO+9916uXLkCQGlpKWfOeIP9U6ZMQSnFyZMnAc9QLFy4EPCyi1KFgkz5dVSGg1HnfUVifU717F3RSTAYjGtM3Xvk5ORIim4fMigMulKKcDjMb3/7WwAeeOABHnnkESDe64b2nsLVq1f54IMPAM8zmDJlCgB333133LlVVVX861//YsWKFT2Wta2tDYCTJ09SV1dnPf1FixYxYsQIK6f7f9++fdx2222A57W45ObmMmfOHAB+/vOf8/7777NkyRL7/IPFi9Fa4/P5bMObqE+tNfv27QPgtttui3vOlpYWu+3z+awOQ6EQgUDANgQ+ny9uOxKJUF1dDXgeurl3Xl4e169f5/333we8PP7s7GwAsrOzUw7GmvIzXuLp06cJBoNW31prLl68CHjl4sre1NRkG+VwOExra6utX8OHD2fYsGFp67I3cHsy3cWtz6dOnbL6AFiyZInV48WLF8nNze2w92LGMD744APuvfdeAMaMGWOvba5j9vl8vnbvsxkv0VpbHZtnNfrNyspCKdXtHnYmMzgshSAIgtBjBk0TFwqFGDVqFIDtOkN7T0ApZVvtV155hREjRjB69Gi7z3jrZWVllJSUcOPGDQCOHDlCXV2d9b5WrlyZNH6aDDdufPLkSXbs2AFgPQ0TN/7Vr37FypUrAZg5cyZtbW385je/sZ9NDD0/Pz9uFmkkErGfi4qKrMyDBdfbbWxspKysDICzZ8/G6XPz5s0UFhYCXgzd9JZGjRrFhQsXePPNNwFoa2uz3m9eXh61tbXU1dUBXhl++9vfBmDYsGFcvnzZ1ofLly/bslBK8cc//tGWwf79+5k7d66V0y1Tt5dTUVHBjh07mDBhAuD1JFpaWti0aZO9p5HNeKQNDQ0AnD9/nr179wJwxx13UFBQYFM2Dx48aM8zIbO+Dt101zN3PXujD4AJEyZYfQBs2rTJesW5ubkpPfRIJILf7+fIkSMAfPzxx7an9oMf/IDy8nJOnPCWeBo+fLitM1/+8pfj4vY+n8/WrV27djF69Gg+/elPAzBixAjbU6usrOTRRx+luLjYPs9g6cUONIPGoMPNSuIWTuLgV11dHVu2bAFg4sSJrF692sZLTV40wE9+8hMOHDhgZ2vOnz+fmpoaa3DSfdlMmGHbtm2AZ9DXrFkDeEbaJRqN2uN+/OMfo5SyA4MTJkywht90aROf2zz7YMjddkNGpjy01gQCAe677z7AC1fMmjUL8J5hxowZ1lCGQqG4WGleXp59OQ8cOMDkyZMBKC4upri42MbJt2zZYrvdWVlZHD16lFtuuQXw9Hb69GkAjh49ysSJE61xunLlig13JT6Dz+ezM31PnTrF6tWrmTFjRtyxlZWVgDcj+J577rHfRyKRuFm8xtgtWbIEdxmLO++8k1//+teAF3JYtGhRr4REUhEOh+2s6/nz56cdfnDLc+vWrVYfQFKdGL25OjHXMfXW7/cTiURsiK2hocGGcbKysggEAjzwwAOAV/a33nprUnmi0Sjl5eUATJ48mdWrV9syraurY+fOnYDnEM2bNy9th2woIZoQBEHIEAaNh+52vVwP1WybFv+dd95h7NixAHz9618HsF65Owg6c+ZMDhw4wNKlSwG4cOECwWDQek3Nzc3tPLpEeQyVlZU2m+KJJ56wXrfxJI2HMG/ePNtlLC0t5aGHHmLRokUA7N69G7P0QTAYTJkGNlhmV7qZDyalMjs7mxEjRtj0wKysLKuD69evs3jxYpuudvvtt9ueiOmSux67Oc/MGjRhs7lz51oveOTIkWRnZ9uwSn19ve3Kt7W1sXHjxrjwl+mpJXbBq6qqrAe+dOlSZsyY0W6m4tSpUwEvC8P0JFw9gBeuMUyaNIlwOGzvOWrUKHtsc3Mz0WjU1suOskdcOboy6SYUCrFnzx7A01lnHrpbp8zAcmVlpdVHoizg6cSEAo1OknnDLS0tZGdn2/pdXl5ue3FnzpyJ8+5Pnz5te7gQ/8xNTU188sknAHzve99j2LBhNqvppZdeYtq0aQCsXbu2w2cdygwKg25GsJOlmkWjUfx+P3/7298AL475zW9+s91+9zPEh1/AMyBaa44dOwZ4i2F1NGLvfrd161Zbmdy4aSAQiDMekUjEnhcOh232DnjxZtNFN9kbyZ7XnQ1pniedLmVzc7MNTyW7ptuAmGyQVGitrRF/7733+Pvf/w7A+PHjWbZsmb2O1tru+8pXvkI4HObs2bOAF2IxcodCIfx+f5xsbnjNNSTTpk2z8pWWlnL8+HH7uaioiE996lMAPPLII5SVlVkjm5eXZ8vFpLma+23fvt023vfff3/S1NOmpiarG1f/pqwAamtrbVghsexMwwJeFpPP50ur3Do7JnHOg5E3Go0ycuRIu+1Os1dKtbuuW2bbt28HvLi00Yc5xq33TU1NNoYeCATstH3wDLPJMKqvr6e4uNiOYQQCAT76yJs0bsYVTDn5/X6bkpz4/H6/nyeeeALwnAWtNX/+85+tbF/96lfjdDKU0kvTZVAYdJ/Px4kTJ7h69ar97O6LRCLWwwJsnDbZsabCVVRUMHz4cBu3XrhwIX6/3w6kBAKBlBXCfXFqamqAm6shJlubwnxn4rtGlrKyMmsAzp07x4MPPphU7sTrVVdX8+ijj1o5kx0D3ov8u9/9DvA8oVQGHeJf8lWrVpGXlxf3Yrjb0WiUX/ziF4CXF79hwwYAnnvuOcaMGROXqjh//nzAM76zZs3i3DlvfTb3WTt78VxduPHqGTNmMGfOHNs7A3jqqaesXq5evWpj+OFwuJ1OTdlFIhGbFpqdnZ20kTQGf+PGjXEG3dXbmTNnrIF655134gZM//nPf1pjlJeXx+7du22PJD8/355n9Gwa+tLSUhobGwGvIZg+fXpSA5uoQ2PME7eTkVifTQM1Z84cqw9oXydHjBjBt771LbtPKWUnee3bt49169YBUF1dzZ/+9Cfuv99bPfuLX/xiXO+1trbWTkAbNWpUXCPlPld2drYdW9Fas3XrVjtYvnbtWklTTAOJoQuCIGQIA9rkmVZ87969HDt2zGagmO4deJ5JJBKx3u+UKVOsh2FaePfYCxcu2G03tqi1Tnt9ctdzMKlSkyZNsvtcD9n1os6ePWv3zZ49m/fee8+O2pvp/onXN7IaCgsLqaqqsumOCxYssPFdc6wbrjDecjreS6L3nsxzjkQibNu2zer4G9/4hj1u2bJl5Obm2s8PPvigDc0cPHiQ6urqdtP9jZzp4urGZEOY5RAS5XezJdxJPcbjNF6hkaej5za43rmRxVwnMTtJa217KCUlJXZ/OBwmEAjwl7/8BfDCYcZDD4fDBINB6+nu37/fjhEkyu9OpKqqqoqr6+Fw2IaJ3nrrrbjyDwQCPPTQQ3bbDUsm00lH+jDXVUpx+vRp9u/fD3hZPqZeKqXIzs6213GzZXbu3EldXZ1NNf785z8fl5WWqE/z3bZt26iqqrLx8mnTptnJSa2trRQWFvZpFtG/KwNm0N10pZqaGnJycuzL4abuJcYDCwoK2r1YLiZPOBgMMnPmzLiUKPee6WK6ms8++2zKY0y3sKKigq997WsAPP7444TDYRvyycnJSRpDTiQ3N5fCwkIOHToEeIN9bvzTxY23pzOQaq6TzPi7sdnq6mqbZmZCMIAdYDZEo1Ebqnj66adpbW2Ny69PJXc6MnZ1X7Lj3Jx1kxOf2JiasYxXX30V8NYXMfMhjCE04wKhUMgOxCeTxdwvEAhw9913c+DAAQA+/PBDli1bBnhhhdraWht/DgQCfOELX4i7Tl8aKDdxYMqUKSn1AfDqq6/y2c9+FvAaz6qqKvuMCxcutOWan58fFxd37/G5z32O5uZme0234U1WTia+b4y5GbuCm+92UVERIIY8GaIRQRCEDGHAPHQ3dLBhwwa2bNnCyy+/DMCPfvSjdl1fQ2VlJfPmzQO82aBtbW22i9fW1mZDM0uXLiUvLy+tbpnJgEnm+ZsUQxPKiUQidrAtGo1SXFxsF9ZSStlJGj6fj2HDhiX1SBJxvaKDBw9y6NAh6+nn5+enHNVva2vjhRdesNsd4Q4Ym8k8yXRjysUM6Cl1c83zpqYmzp8/b70mV55gMNhhzwluZob4fL4+XZfeyGW8xkOHDtlUSxM6c2U6evSonZ07fPhwq28j4/nz5+0+Q2KPL7FsTp06ZeupCZeAl+JXVlZmU/kuXbpke3F+v7/dQnRmUD1x/R+4GT750pe+1KE+3PLNz8+3vb/a2tqU+gC4ceNGXI8rGAzaayXOhK2srLTXSlwx1E0PTuwRuDrcvn27TX5Yv3593MStiooK+7u+JpvGXfels7o3VBjQGLprqEpKSvjlL38JeAbOFFA0GiUQCNjc1ddee43NmzcDXuUcO3Ysr7/+OgDXrl2ziyyZdKyODLkxgj/96U/JysrimWeeAW4acfBi2DU1Nbz22mtWtsceewyAPXv2UF5ebtPq3PzYdOLVybh+/TpFRUXWGKVKbwQvtcukcCaOJyTi7nMXv0qG1ppr164BngEysdrf//737eK96dLS0mJng0ajUbt99epVG+LoLcyzmhjv1KlT7bIDK1asiJthunfvXq5du8aTTz4JxOukpaWFxsZGO44ye/Zsm4l1yy23tCsXU8ahUIjz58+zfPlywDPopmF/9913mTx5MsePHwe82c5uvn4iqdIWTa47eIbXbYgSw5Ru2U+dOtXq5c0337T6AK9cjD4AnnzyyZRZZI2NjbbR//DDD6moqLAGONUcC1dH7rOZ1NeTJ08yfvx4AEaPHk19fb1NVy4tLbUhWa01LS0t/OxnPwNo9+4OZQbUoCfG7twp5gYTTzde+Jo1a2y8c/To0Zw7d45Lly4BXsrZHXfcAcBf//pXFixY0KGHbr5bsmQJNTU1duDGrRTBYJB169bZ5QYaGhrsmhX19fWMGzfOGoOysjKbguauL9NVEscQOqKznPJ0cb3SdevW2WnlmzZtsmmixcXFcYOdncnmDoo2NDTYHPJly5ZZ3VdWVna7kegI14itXbvWTsJ54403yMrKsvcvKChgzZo1cWMtZruhoYHa2lo7wOjz+axxN9534j3Nc/t8Pls2oVCIF198EfBSbj/zmc/w9tve78KYsQpz/VTXTNS1W0fSyXl3ZTOOx549e6w+zHWMPhLvAdiUX4AXX3zRXnP27NmUlJR0uddlEh7MO7N06VKrsxMnTsQ1DEuXLrXjIEp5Ky0a5y3VuzsUkRi6IAhChjCoMvWTTf2HeG9r2rRpfP/73we89KXW1lYbgjlz5oydaJMs5ph4L5PtsWTJEsrLy9t1U93t9evXA14c2XSNg8EgV69etdPRS0tLefrpp7v+4Alyuffvr2UA3Od1u+XNzc1xMdGOutMdXfOuu+6KmzTUH7j3f/jhhwEv1dKdhJS4hnniJKd0ZdZaWy+xrKwsTmezZs2yvbqSkhKOHTtm47933nlnUnnTeTYje1fPMzz88MNWH3Bz3CfV8VlZWXEhTUNPeol+vz/tdGKDiZkbWRLf3aFMpwZdKTUJeBkYD2jgBa31/1ZKjQG2A3lADfC41vpKT4QxXTY3hp3Y5XQHQILBINnZ2XZQaefOnbZbtnjx4k5j6MYwP//880yfPt0O3nQ03d4d4Ln11ls5cuSI7YavWbPGVu6upkia0JC7PspA4Rrt7hrzZNdM1Tj1x8to9Js4eNtRWacjs1tPzazRtrY2cnJy7DjBPffcY1fmbGxs5MqVK3Ym7aVLl+J+FCVd/WZnZ7Nx40aAbv8CUDQa7ZI+zH5z/3TPSUeOdEgM0T7//PMAab+7Q4F0njwMPKO1LgQWAN9RShUCPwTe1VpPB96NfRYEQRAGiE49dK31J8Anse1rSqlyYCKwClgaO2wLsA/4QXcF8fl8NoPg3LlzKUfME0fJ/X6/XbTn/PnzaXff3DDO7Nmz4wZ8OmrhXXnmz59PJBKJ+7GGriwclJgeB94Aj8k6GCjSyVDozjV7cn5PSVWmHZV1OjInhpXc/6kYN25cp9dKh57+NmdHiQK9eU535OgMrTWzZ88GSPvdHQqoLs7iywP2A7OAs1rrW2PfK+CK+ZyKoqIibbInXEzX1vym6I0bN+zof2Fh4YAbA0gvlt1R49PR9Zqamuw08cOHD7Nu3Tobw+5JmEPoXxLHgFKVW+KMYSlfoTN8Pl+Z1rqos+PSHhRVSuUArwP/qbVuTPCUtVIqqcVTSm0ENsLNpTSTHANgV287efKkXRfZ3Z8K90XqTgvdk7ibO825qy+mOf7y5ct220yo6IqnLwwO0q0HUqa9g6zl0p60NKGUCuIZ81e01m/Evr6olJoQ2z8BqE92rtb6Ba11kda6yCyALwiCIPQ+6WS5KOC/gXKt9XPOrreA9cBPY/93JDk9bVyvZebMme1+rzOdc7vr+fSkhe/uua6skyZNipuCLWEWQegc8czbk07I5QHga8A/lFLHYt/9GM+Qv6qU+g/gDPB4bwnlposNhUJzn1diqoIgdJd0slwOAqkszMO9K44gCILQXQbVTFHDUPNSh9rzCoLQN2R+PEMQBGGIIAZdEAQhQxCDLgiCkCEMyhj6YERi3IIgDHbEQxcEQcgQxKALgiBkCGLQBUEQMgQx6IIgCBmCGHRBEIQMQQy6IAhChiAGXRAEIUMQgy4IgpAhiEEXBEHIEMSgC4IgZAhi0AVBEDIEMeiCIAgZghh0QRCEDEEMuiAIQoYgBl0QBCFDEIMuCIKQIYhBFwRByBCU1rr/bqbUP4EbQEO/3fTfg7GITpIhekmO6CU5mayXyVrr2zs7qF8NOoBS6ojWuqhfbzrIEZ0kR/SSHNFLckQvEnIRBEHIGMSgC4IgZAgDYdBfGIB7DnZEJ8kRvSRH9JKcIa+Xfo+hC4IgCH2DhFwEQRAyBDHogiAIGUK/GXSl1HKlVIVSqlop9cP+uu9gRClVo5T6h1LqmFLqSOy7MUqpPymlqmL/Rw+0nH2NUmqzUqpeKfWh811SPSiP/xOrP8eVUvMGTvK+I4VO/qdS6lysvhxTSq109v0oppMKpdTnBkbqvkcpNUkptVcpdUIp9ZFS6unY90O6viTSLwZdKeUH/gtYARQCJUqpwv649yBmmdZ6rpM3+0PgXa31dODd2OdM5yVgecJ3qfSwApge+9sIPN9PMvY3L9FeJwD/K1Zf5mqt3waIvUNrgHti5/zf2LuWiYSBZ7TWhcAC4Dux5x/q9SWO/vLQ7wOqtdYfa63bgG3Aqn66978Lq4Atse0twP8YQFn6Ba31fuBywtep9LAKeFl7/BW4VSk1oX8k7T9S6CQVq4BtWutWrfVpoBrvXcs4tNafaK3/Ftu+BpQDExni9SWR/jLoE4Fa53Nd7LuhigZ2K6XKlFIbY9+N11p/Etu+AIwfGNEGnFR6GOp16KlY6GCzE44bkjpRSuUBnwFKkfoShwyKDgyLtNbz8LqF31FKPeju1F4u6ZDPJxU9WJ4HpgJzgU+Anw+sOAOHUioHeB34T611o7tP6kv/GfRzwCTn812x74YkWutzsf/1wJt43eSLpksY+18/cBIOKKn0MGTrkNb6otY6orWOAv+Pm2GVIaUTpVQQz5i/orV+I/a11BeH/jLoh4HpSqkpSqksvIGct/rp3oMKpdRIpdQosw18FvgQTx/rY4etB3YMjIQDTio9vAU8GcteWAD8y+lqZzQJsd8v49UX8HSyRik1TCk1BW8A8IP+lq8/UEop4L+Bcq31c84uqS8uWut++QNWApXAKeDZ/rrvYPsD8oG/x/4+MroAbsMbpa8C/gyMGWhZ+0EXW/FCCCG8GOd/pNIDoPAypU4B/wCKBlr+ftTJr2PPfBzPUE1wjn82ppMKYMVAy9+HelmEF045DhyL/a0c6vUl8U+m/guCIGQIMigqCIKQIYhBFwRByBDEoAuCIGQIYtAFQRAyBDHogiAIGYIYdEEQhAxBDLogCEKG8P8B/LjdHmjB3/EAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa280a2940>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2804b588>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa28075160>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa280182e8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2803f048>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20773ba8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2071d7f0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2073fb38>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa206e8860>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGy9JREFUeJztnWuQlMW5gJ+e2Rl22QUFCYgI7AK7IARBA7rIXY0HpAxJSk6WwJGUJ5JUaZlUrFRuf86P/EhVKp6ckzrHyykvaAyrlhpNopBEUQQFdIEQZdkLN3dXLuEWbnubmT4/vummv29ndmfZK7PvU7W1M9+t+3u7++233367R2mtEQRBEK58Qn2dAUEQBKF7EIUuCIKQJYhCFwRByBJEoQuCIGQJotAFQRCyBFHogiAIWUKXFLpSaolSqkopVauU+nF3ZUoQBEHoPOpy49CVUmGgGvgyUA98BKzUWu/tvuwJgiAImdIVC/0WoFZrfUBr3QKUA8u7J1uCIAhCZ8npwr1jgDrnez1wa3s3jBgxQhcWFnYhSaE/05nRXmtra6efH4lEOn1PpiileuzZgtBVKioqTmitv9DRdV1R6BmhlFoLrAUYN24cH330UU8neUVyOa6vnlRCl5OfWCyW8bVHjhzpdF7GjBmT8T2JRCLjawGi0Winru8s/a18e5qe3lLkSpbN5RAKhQ5ndF0X0mgAxjrfr08e86G1flJrPUtrPesLX+iwgxEEQRAuk64o9I+AYqVUkVIqCpQBb3RPtnoGrTWJRKLT1lt3EY/HicfjPW699BZaa/tOQcx7dse7KqUGnEV2pWPaWbD8+7oNZjuX7XLRWseUUg8BG4Ew8LTW+tOuZMYUfk813r5WDOFwuM/S7gmUUmnfqbvetbW11XYYLS0t1o+eqhybm5sBrx71tAtFaJ9QKLWt2NdtMNvpkg9da/0m8GY35UUQBEHoAj0+KdoZTM/d3NxsoyDy8/M77NHd4Zu51vx3h3xVVVUcOHAAgCVLlqS1IjIl6FIIPs89F4vF2LBhAwAlJSVMnjzZnm9ubrbvkJeX16U89TSurIPy1FrbidENGzZQUlICwOTJk0kkEr5ybM8VY9IIh8N8/PHHbNmyBYDx48fz8MMPA175aq3tM8vLy6mr84Ku8vPzuf/++9u10k36qfLR0yPF/kp79dmVdUe0trayceNGAIqLiykpKbHPraqqYv/+/QAsXbq0y21Q8NMvFLqpLDU1NQC8/vrrTJgwAYDly5cTCoXarUyZVAqlFPn5+d2ixN1OI5ivoDIw/yORCAUFBb5jhmPHjvHqq68CMHLkSO699952XQt9jZFhQUGBT55KKZvvgoICX96Dcm/vvYy7JpFIMGvWLM6cOQNAfX09hw97k/1FRUUopdi3bx/gKYrbbrsNgPnz56d1+ZjyC5ZPpnnLNjqqz4bOyCQSifg6UzeNgoICcYf1IP1CoSuliMVi/O53vwNg7ty53HnnnQApLTv3+5kzZ9ixYwfgWcFFRUUA3HDDDb5ra2pqOHv2LEuWLOlyXltaWgBPidTV1ZGT44lx3rx5DB482HetsTbfe+89hg8fDmAtV8P48eP59re/DcCvfvUrPvzwQxYuXGjfv79YMVprQqGQ7Xj/+c9/snTpUnu+paWF9957D4Dhw4f73rOpqcl2dqFQiPPnz9t7IpGIlWEoFLINPhQKEY/HbXoFBQX2c2FhIefPn+fDDz8EvLDD3NxcAHJzc9OGUJryM1biwYMHiUQifPnLX7bveOzYMcALszV5B7h48aLtlGOxGM3NzbZ+5eXlMWjQoM4JtIuYutWV+uHW5/3791t5ACxcuNCW2bFjxxg3bly7oxczh7Fjxw6+9KUvAdg6b+R97Ngxey4UCrVpz6bczGjPPDORSFj5RqNRn/EgXKJ/aApBEAShy/QLCx08v9uQIUMA7NAZ2loCSinba7/wwgsMHjyYYcOG2XPGWt+5cydlZWU0NjYCUFFRQV1dHUePHgVg2bJlGVs4rpVcVVXF66+/DmAtDePvf/zxx1m2bBng+Y1bWlr47W9/a78bv+LEiROtpQdeiJ/5PmvWLC5cuNChvHoT16969uxZKioqAPjss8+sNXv33Xfz1FNPMXXqVAA2btxo3WZDhgzh6NGjvPbaa4BnyRnrqqioiLq6Ouv/Vkrx4IMPAjBo0CBOnTrF3LlzAc8KNmWmlOJPf/qTLYPNmzczc+ZMm0+3TFOV3+jRowG45ppraGpq4oknnrBpGsvcWKQnTpwA4PPPP2fTpk0AXHvttZSUlNgInC1bttj7jMusp103l2uZu/Xerc+jR4+28gB44oknrFU8bty4tBZ6PB638x0ABw4csHL60Y9+RGVlJXv3els85eXl2Trzta99zee3D4VCtm5t3LiRYcOGMWPGDAAGDx5MbW0tANXV1dx1113MmTPHl77QjxQ6XKokQb+sOyyrr69n3bp1gLdycMWKFb7CNA3s5z//OVu2bGHevHmApygPHjzItGnT2qTXHsbNUF5eDngKoaysDPCUdPBac91Pf/pTlFLW7TB69Gg7nGxvqGiGoX2NO2loykNrTU5ODrfccgvguSu++MUvAp4sJ0+ebBVlLBbz+UoLCwtt49y8eTOTJk0CYM6cOZSWllo/+bp163xy2rlzJ0OHDgVg6NChHDx4EIBdu3YxZswYq5xOnz7tc3e57xAKhVi/fj3gDf1XrFjRpuwOHToEwPr16311JB6Pc/3119vvRtktXLgQdxuL6667jueffx7wXA7z5s3rFpdIOmKxmF11PXv2bOuy6gi3PNevX2/lAW3rc3V1tZWbKxPzHNN+wuEw8Xic4uJiAE6cOGGNrmg0Sk5Oju2U33//fa6++uqU+UkkElRWVgKeG3LFihW2TOvr621QwYQJE7j55pt95St4iCQEQRCyhH5jobtDL9dCNZ/NxM1bb73FiBEjAPjWt74FXLLK3d5+8uTJvP/++3Zy8ejRo0SjUWs1NTU12Um0dPkxVFdXU1VVBcDKlSut1W3SNZbKTTfdZIeM27ZtY968eXaEsHHjRpvvSCSS1grvrtWVXcWNfDBuq9zcXAYPHmzdI9Fo1FrT58+fZ8GCBdatNGLECDsSicfjhEIhX+SOkZ1ZNWjcZjNmzLBWcH5+Prm5ucyaNQvw5Pbuu+8CXn1Yu3atz/3lRse4MqypqaG6uhqARYsW2TBKl4kTJwLexKsZSbhyAGwdABg7diyxWMymOWTIEHttY2MjiUSiTf1wMflz89FRNJdLa2sr77zzDgAzZ87s0EIPygO8em3kEcwL+F2DRiaprGHTlszWHpWVlcyePRuAw4cP+6z7gwcP2hEu+CNrLl68aPf4+cEPfsCgQYNsVNOzzz5rR3WrVq2yI3fzDMGjXyh0M4Odyg+WSCQIh8Ps3LkT8PyYJiLEPQ+XlqIHP4M3RNVas2vXLsBz17RXIdxj69evtw2+pKTEFyftDj3dBhGLxWz0DkBdXZ0dopvojVTvG5y9zzTKpbGx0VfJg89089leRwae7IwS37p1K3/7298AGDVqFIsXL/bF+JtzX//612lqauKzzz4DPBeLyXdLS4uVlbnPEAqFfAq4uLjY5m/btm3s2bPHfv/qV7/KVVddBcCdd95JRUWFVbKFhYW2vF0XEcCLL75oh+633nprm1A9uBShkZub65O/KSvwytC4FYJlZzoW8w6hUKhNHLeL665oD1dmwbqWn59vP7syVEqlDRPVWvPiiy8Cnl/ayMNc49b7ixcvWh96Tk6OXbYPnmI2EUbHjx9nzpw5dg4jJyeHTz/1Fo2beQVTTuFw2M6tgL+DCIfDfPOb3wQ8Y0FrzV//+lebt2984xs+mYgib0u/UOihUIi9e/daP6pbyKZBGQsLsH7a4LVuhayuriYvL88q1Llz5xIOh+2Ea3sNyW04xrc6duxYe85tnG6HcuDAAd88QEVFhbUq6uvrWbBggS+v6dKura21YXSpLC/Xuvv9738PeJZQOoVu0jOyWr58OYWFhb6G4X5OJBL8+te/Bry4+AceeACARx99lOHDh/tCFY0ltn37dkpKSmho8PZnMyMjI4v2cMvN9VdPmTKFGTNmWGUL8NBDD1m5nDlzxvrwY7FYm3RM2cXjcW688UbAU9ipOkmj8NeuXetT6K7cDh8+bBXUW2+95Zsw/cc//mGVUWFhIX/+85/tHMKECRNs/TFhuGYifceOHZw9exbwOoLi4uKUCjZYX4wyD35ORbA+mw7qxhtvtPKAtuU0ePBgvvOd79hzSim7yOvdd99l9erVANTW1vKXv/yFW2/1ds++55577PuFQiHq6ursArQhQ4b4Oin3vXJzcxk/frw9t379eurr6wHPKnfbgijz1IgPXRAEIUvoUwvdWM+bNm1i9+7dNuTNDO/gkr/VRDcUFRX53CqudRkOh337bM+YMcP26lpr5s+fn1G+XMvBhEoZC8slaOnV1dXZvEyfPp3nn3/ehmtNnjzZ+t5dX795R8PUqVOpqamx4Y6lpaXW3WOuda02Yy1nEuWQbtjvEo/HKS8vtzK+//777XWLFy9m3Lhx9vuCBQusa2bLli1UVlZaf6y73Lsz1pQrexMNYbZDCI6q3GgJd1GPsTiNVWjy0957G1zr3OTFPCcYnaS1tiOUlStX2vOxWIycnBy7yKqxsdHWHxP9YyzdzZs329DLYP7dRVw1NTW+uh6Lxbh48SIAb7zxhq/8c3JyuP322+3n4CgyKJP25GGeq5Ti4MGDbN68GfBGYKZeKqXIzc21z3G3tdiwYQP19fV2lLVs2TKfizQoT3OsvLycmpoaVq1aBcCkSZPs4qTm5mamTZvmC2EVi92jzxS6q9QOHTpEQUGBbRxu6F7QH1hSUtJu2J+ZKMrJyfGFYZnK0tkQJzPUNCtQzTEXM0lYW1tr07zqqqv47ne/a4eeBQUFad0h7vuOGzeOqVOnsnXrVsCb7HP9pm7Fdf3tmUykmntTKX83jdraWhtm5k5gLlq0yJeW1toq24cffpiTJ0/aSTSj6M0zw+GwnexsamriuuuuAzz3matwXILvdO7cuZRuouA7xONxrrnmGvvMaDTq60xTxVC/9NJLgLe/iFkPYfJl5gVaW1vtRHy6zhA8+d5www1W+X3yySdWdrm5udTV1bFt2zbA6yTuuece33N6MgzPrUNFRUVp5QHw0ksvcddddwFe51lTU2OV6G233WblP2HCBJ9f3E1jyZIlNDY22me6HW+quSfj3zfK3LgsARvbbibJJVyxLSIRQRCELKHPLHR3mPTAAw+wbt06nnvuOQB+8pOftBn6Gqqrq7n55psBb/VnS0uLtYpbWlqsa2bRokUUFhZmtLjDRMCksvxNiKFZYRqPx+1kWyKRYM6cOfzxj3+072QWaYRCIQYNGpTSIgliwvrAW3ixdetW7rvvPsCzftx3cC2qlpYWnnzySfu5PUw0CXiLeebMmZNSNqZc3BBDY9FfuHCBI0eO+Kwmk5doNOpb/ZrqXc1zwuGwbyifjo6ij9Lda44bq/GDDz6wo6ig6ywcDrNr1y67OjcvL8/nwgMvssqcM5ioknSTlvv377f1tLa21p5vamqioqLChvKdPHnSjuLC4XCb0EUTVWP+uxj3yVe+8pWUcjC45TthwgQ++OADwBtZppMHeOXtlmkkErHPCq6Era6uts8K7hjqLvgKjgjcUXN5ebkNflizZo1v4VZVVRWnTp0CsNE07r4vsq+LR5/60N3h88qVK/nNb34DeArOFFAikSAnJ8fGrr788ss8/fTTgFc5R4wYwSuvvAJ4Q3ITXWHCsdpT5EYJ/uIXvyAajfLII48A/jjx0tJSDh06xMsvv2zzdu+99wKee6eystKG1Rl/n/tuhkx9fOfPn2f27NlWGRl/bKrnRKNRG8IZnE8I4p4zsk0nG601586dAzwFZHy1f/jDH3wr9NzGGQyrM8cMjY2NtkHG43H7+fTp09bF0V2Yjs/4eCdOnGi3HVi6dKlvhemmTZs4d+6c7UBdmTQ1NXH27Fk7jzJ9+nQbiTV06FCfm8itay0tLTQ0NNiN4Gpra23H/vbbbzN+/Hj27NkDeOGzbrx+kHRhiybWHTzF63ZEQTelW/YTJ060cnnttdesPMBra0YeAPfdd1+biDOT5tmzZ20UzyeffEJVVZVVwMF64JKqfpjQ13379jFq1CgAhg0bxvHjx2248vbt261LVmtNU1MTv/zlLwHatN2BTJ8q9KDvLhg/DJcap7HCy8rKrL9z2LBhNDQ0cPLkScALObv22msBL4a5tLS0XQvdHFu4cCGHDh2yEzdupYhEIqxevdpuN3DixAm7Z8Xx48cZOXKkVQYVFRU2BM3dX6azMunMoomOYsozxbVKV69ebZeVP/7449bfXVpa6pvwShcyGiQcDnPixAk7ibl48WJr+dXU1FhLtjtxZbhq1So7t/Lqq68SjUZt3ktKSigrK/MtSjLnTpw4QV1dnZ1gDIVCVrmnyrPbuYXDYVs2ra2tPPPMM4A3Z3DTTTfx5pve78KYuQrz/HTPDMrWnXcJxrynk4fJmzE83nnnHSsP8xwjj2AagN07BeCZZ56xz5w+fTorV67s9H4qZn7GtJlFixZZme3du9fXMSxatMjOY5lRozHe0rXdgYj40AVBELKEfrGwyJBqJSH4ra1Jkybxwx/+EPDCl5qbm60L5vDhw3ahTSqfYzAt48pYuHAhlZWVaUMJlVKsWbMG8FbPmaFxJBLhzJkzdjn69u3b+d73vtf5Fw/kK5h+b+Cm5w7LGxsb2/iOM40ucJ85duxYn7+2N36ZyU3/jjvuALxQS3cRUnAPc/fdrr/+et9Cp1S4ddZE8VRUVJCXl2fTnzZtmh3VrVy5kt27d1v/rxn9BPObybuZvHf2PsMdd9xh5QGX5n3SXR+NRn0uTUNXRonhcNiGE2e65YXxmZu8BNvuQKZDha6UGgs8B4wCNPCk1vq/lFLDgReBQuAQ8K9a69NdyYwZsrk+7OCQ050AiUQi5Obm2kmlDRs22GHZ/PnzO/ShG8X82GOPUVxcbCdv2lNa7gTP1Vdfzccff2yH4WVlZbZydzZE0riGIpFI2h9n6C3coe7lKvNUz3SVX3APk57GlW+mWysEVwW7pFpab1aNtrS0kJ+fz+nTXnOYNm0aU6ZMATzf8+nTp+2q4ZMnT9o61Z7vOUhubi5r164FuOxfAEokEp2Shzlv0s/0nkzyAR0r9KCL9rHHHgPIuO0OBDJ58xjwiNZ6KlAKPKiUmgr8GHhba10MvJ38LgiCIPQRHVroWusjwJHk53NKqUpgDLAcWJS8bB3wLvCjy81IKBSyEQQNDQ1pZ8yDs+ThcNhu2vP5559nvBrUdeNMnz7dN+HTXg/v5mf27NnE43G7v0dhYH+UjnAnIs3nw4cP+4bhfUFQxuZ7V3/qzH1ub1tR6dJrLx+ZrEAMupXc/y6u9Tly5MgOn5UJXf1tzvYCBbrznkzzkanLBTxLfPr06QAZt92BQKd86EqpQuAmYDswKqnsAY7iuWQuC+NGMX7bjRs32tl/sx1AezHJbnhaZ3B96JkS9Cm6G251ZsjsVt4LFy7YVXANDQ3cfvvt/WZHub5O/0oiOAeULoTT/SzL1i8PN8pFuETGCl0pVQC8Anxfa302UFm1Uipl96qUWgushUtbaaa4BsDu3rZv3z7fniwdVXi3IV1OD90Vv5u7zPlyQhQBTp06ZdNfs2aNXZINYnFcSWRaD0SBdw89+YtQVyoZSUIpFcFT5i9orV9NHj6mlBqdPD8aOJ7qXq31k1rrWVrrWWYDfEEQBKH7ySTKRQFPAZVa60edU28Aa4BfJP+/nuL2jHGtlilTptiogM7ce7mWT1d6+Mu9N+h7dcPjOuO6EYSBiljmbcnE5TIX+Dfg70qp3cljP8VT5C8ppf4dOAz8a3dlyg0XGwiFprX2/VzZQHhnQRC6n0yiXLYA6czFO7o3O4IgCMLl0q9WihoG2sy/UqrT+2AIgiAEkbG9IAhCliAKXRAEIUsQhS4IgpAl9Esfen+kp336/W3OwN08K1Pcjcs6wuxf3RncH4XuiP4mz/6WHyE7EQtdEAQhSxCFLgiCkCWIQhcEQcgSRKELgiBkCaLQBUEQsgRR6IIgCFmCKHRBEIQsQRS6IAhCliAKXRAEIUsQhS4IgpAliEIXBEHIEkShC4IgZAmi0AVBELIEUeiCIAhZgih0QRCELEEUuiAIQpYgCl0QBCFLUFrr3ktMqX8AF4ATvZbolcEIRCapELmkRuSSmmyWy3it9Rc6uqhXFTqAUupjrfWsXk20nyMySY3IJTUil9SIXMTlIgiCkDWIQhcEQcgS+kKhP9kHafZ3RCapEbmkRuSSmgEvl173oQuCIAg9g7hcBEEQsgRR6IIgCFlCryl0pdQSpVSVUqpWKfXj3kq3P6KUOqSU+rtSardS6uPkseFKqb8opWqS/4f1dT57GqXU00qp40qpT5xjKeWgPP47WX/2KKVu7ruc9xxpZPIfSqmGZH3ZrZS62zn3k6RMqpRS/9I3ue55lFJjlVKblFJ7lVKfKqW+lzw+oOtLkF5R6EqpMPA/wFJgKrBSKTW1N9LuxyzWWs904mZ/DLyttS4G3k5+z3aeBZYEjqWTw1KgOPm3Fnisl/LY2zxLW5kA/GeyvszUWr8JkGxDZcC05D3/m2xr2UgMeERrPRUoBR5Mvv9Ary8+estCvwWo1Vof0Fq3AOXA8l5K+0phObAu+Xkd8NU+zEuvoLXeDJwKHE4nh+XAc9pjG3C1Ump07+S090gjk3QsB8q11s1a64NALV5byzq01ke01juTn88BlcAYBnh9CdJbCn0MUOd8r08eG6ho4M9KqQql1NrksVFa6yPJz0eBUX2TtT4nnRwGeh16KOk6eNpxxw1ImSilCoGbgO1IffEhk6J9wzyt9c14w8IHlVIL3JPaiyUd8PGkIgfLY8BEYCZwBPhV32an71BKFQCvAN/XWp91z0l96T2F3gCMdb5fnzw2INFaNyT/HwdewxsmHzNDwuT/432Xwz4lnRwGbB3SWh/TWse11gng/7jkVhlQMlFKRfCU+Qta61eTh6W+OPSWQv8IKFZKFSmlongTOW/0Utr9CqVUvlJqiPkM3AV8giePNcnL1gCv900O+5x0cngDuC8ZvVAK/NMZamc1Ad/v1/DqC3gyKVNKDVJKFeFNAO7o7fz1BkopBTwFVGqtH3VOSX1x0Vr3yh9wN1AN7Ad+1lvp9rc/YALwt+Tfp0YWwDV4s/Q1wF+B4X2d116QxXo8F0Irno/z39PJAVB4kVL7gb8Ds/o6/70ok+eT77wHT1GNdq7/WVImVcDSvs5/D8plHp47ZQ+wO/l390CvL8E/WfovCIKQJcikqCAIQpYgCl0QBCFLEIUuCIKQJYhCFwRByBJEoQuCIGQJotAFQRCyBFHogiAIWcL/AyzTLfRY8CxeAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20692438>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa206bc080>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGstJREFUeJztnWlwVMe1gL+eRQsIh80QwIBYhWVDsA02Mpvs2DFLEl5SkIhATCqOcVXiLBVXKtuf9yM/UpXKS95LvefllRccO4BdXpPYQIJNgOCAkSHERmgBhMUqg8UTi5A0M/1+3Omm52pGGi1o5NH5qlQa3bn39rmn+54+ffp0S2mtEQRBED75BDItgCAIgtAziEEXBEHIEsSgC4IgZAli0AVBELIEMeiCIAhZghh0QRCELKFbBl0ptUgpVamUqlFK/aSnhBIEQRA6j+pqHrpSKghUAfcCx4F3gZVa64M9J54gCIKQLt3x0G8HarTWR7TWLcAGYFnPiCUIgiB0llA3rh0D1Dl/HwfuaO+C4cOH68LCwm4UKQiC0P8oLy8/q7W+vqPzumPQ00IptRZYCzBu3Djefffda12kIHSaroQelVLXQBJBaEsgEDiW1nndKOMEMNb5+4b4sQS01k9orWdprWddf32HHYwgCILQRbpj0N8FpiilJiilcoAy4PWeEevaoLUmFosRi8UyUr4pO1s2RGtPn+Y5s+VZhc6Rqq1n+h3MdrocctFaR5RSDwObgSDwlNb6g+4IYyr/Wg1llVIZHSYHAtmV9t+ePnvqWSORCJFIxN4zHA7bsv00NzcDXjvKzc2VkEgGSVX/mX4Hs51uxdC11m8Ab/SQLIIgCEI3uOaTop3B9NzNzc20trYCMHDgwA57dHf4Zs41v90hX2VlJUeOHAFg0aJF3fYi/SGF9u7X2trK5s2bAZg6dSpTp0611zY3N9vP+fn53ZLpWpNKn/fddx/BYNDW2+bNm5k6dSqAfdZ0PTNTn4FAgL1797Jz504AQqEQ3/ve9wCvft17btiwgbo6L+lq4MCBPPDAA+Tm5lqZ3bLdemtvVOg/5g8fZJun2V577kz9tba2smXLFgCmTJmS0NYrKys5fPgwAIsXL866UWum6RMG3TSW6upqAF577TUmTpwIwLJlywgEAu02pnQahVKKgQMH9ogRdzuNVC+9/3g4HKagoCDhO/O7vr6el19+GYARI0awfPnydkMLmcbIlEyfRu6CgoIE2TvzHOaesViM2267jYaGBgD27NnDsWPeZP+ECRNQSnHo0CHAMxR33nknAPPnzycUutq0/ca8vXprj75YF92lo/Zs6Myzh8NhcnJykpZRUFCQ8J3Qs/QJg66UIhKJ8Ic//AGAuXPncs899wDeS53shTScP3+ePXv2AF68dcKECQDceOONCedWV1fT2NjIokWLui1rS0sL4BmRuro6azzmzZvHgAEDEs43hmLbtm0MGzYM8LwWl3HjxvGtb30LgF//+te88847LFy40D5/X/Fi/B2vX59aa7Zt2wbAsGHDEp7zypUr9nMgELA6bG1tJRQK2Y7AjZMHAgGi0Sg1NTUABINBW3ZhYSEXL17knXfeASAnJ4e8vDwA8vLyiEajSZ/B1J+5Z21tLaFQKEHf9fX1gFcvWmsre1NTEwMHDgS8ttba2mrbV35+vh0R9BbuSKaruO358OHDHD161Op/4cKFtv2eOXPG6sNc58fMYezZs4fbbrsNgKFDh9p7m/uY7wKBQJv32cyXaK2JRCL2nrFYzOo3JycHpVRCpy149A1LIQiCIHSbPtPFtba2MmjQIAA7dIa2noBSyvbazz//PAMGDGDIkCH2O+Otv/fee5SVldHU1ARAeXk5dXV1nD59GoClS5em7eG4XnJlZSWvvfYagPU0TNz4scceY+nSpQAUFRXR0tLCc889B8C0adNsDH3ixIk2/AIQjUbt37NmzeLSpUsd6qs3cb2yxsZGysvLAairq+PMmTMALFmyhCeffJLi4mLAi6Gb0dKgQYM4ffo0r7zyCgAtLS2MGzcO8Dzturo6jh8/bsv49re/DUBubi4ff/yxbQ8NDQ22zpRS/PnPf7Z1sH37dmbOnGllNt6fkd+tv1dffZXRo0cDngfZ3NzMY489BnjevZHNeKRnz54F4NSpU7z11lsAjBo1iqKiIjsS2Llzp73OhMyudYimq5652+7d9jxq1CiGDRtmRySPP/649YrHjRuX0kOPRqMEg0H27t0LwJEjR+xI7cc//jEVFRUcPOht8ZSfn2/bzJe+9KWEuH0gELBta/PmzQwZMoTPfOYzAAwYMMCOqqqqqrj33nspKSmxz9NXRrGZps8YdLjaSNzK8U9+HT9+nHXr1gEwZswYVqxYQTAYtOebF+wXv/gFO3fuZN68eYBnKI8ePcpNN93Uprz2MMZgw4YNgGcQysrKAM9o+8815/3sZz9DKWUnBkeNGmUNvxnSJpPDNUR9Af/LEg6HueMOb4cHvz6LiooYNWoU4HVybqy0sLDQvpw7duxg/PjxAJSUlFBSUmLj5OvWrbPD7pycHPbt28d1111nyz569CgA+/btY8yYMdY4NTQ0tAl3GQKBAOvXrwe8of/y5cuZNm1awjkmFr9+/fqEZ4rFYowd662fU0pZY7dw4ULbYQGMHj2a3//+94AXcpg3b16PhERSEYlE7Krr2bNnpx1+cDu39evXc/jwYVasWAG0bc9VVVVWb65OzH1Muw0Gg0SjURtiO3v2rA3j5OTkEAqFmDt3LuDV/eDBg5PKE4vFqKioAGD8+PGsWLHC1unx48fZtGkT4DlEt956a0JHIHiIJgRBELKEPuOhJ0slcz+bHv/NN99k+PDhAHzjG98Arnrlbm9fVFTEjh077GTX6dOnycnJsV7TlStX7CRaKnkMVVVVVFZWArBy5UrrdZtyjadyyy232CHjP/7xD+bNm2dHCFu2bMFsfRAOh1OmgfW11ZWBQMB6pbm5ueTn5/Phhx8CnvdldHDx4kXmz59v09Wuv/56OxIxQ3LXYzdeuFk1aMJmM2fOtOUNHDiQvLw8G1b56KOP7FC+paWFtWvXJoS/zEjNvwqxpqbG1l9paSnTpk1LmDRVSjF58mTAy8KYMWNGwvVuyp1h7NixRCIRW+agQYNsfTY1NRGLxRLaR6rRoD/ltjOpgSb8M3PmzA49dLdNmYnlqqoqSktLrWfu19ukSZNsKNCMrpJ5w+ZdMu27oqKC22+/HYBjx44lePdHjx61I1xIfObLly9z6tQpAH74wx+Sm5trs5qeeeYZW0erVq1q91n7M33CoJsZbDd04n4XDAZ57733ADh58qTNCHG/B6/RusbdfWkjkQhaa/bt2wd44Zp0c5DXr1/PpEmTAC+n2jT8YDCYYJjdFyISidjsHYAPP/wQs9Okyd5I9rxKqYSQTLrxwaamJhueSnZPV872OjIgIbPj73//O//85z8BGDlyJHfffbeVR2vN/v37Afjyl79MJBKxxr6wsNCe19raanXlymR04ept8uTJVr7du3dz4MAB+/fs2bP51Kc+BcA999xDeXm5NbKFhYW2vl35ADZu3GiH7nPmzGmjU6WUnWvJy8tL0L8rX11dne3Mg8FgQhsxHQt4WUyBQCCteuvoHNfJ8bc1k3HjX2avlGpzX3ddxsaNGwEvLn3HHXckXOc+0+XLl20MPRQK2WX74Blmk2FUX19PSUmJncMIhUJ88IG3aNzMK5h6CgaDNiXZ//zBYJCvfe1rgOcsaK3561//amX76le/mqCTbEwj7S59wqAHAgEOHjzI+fPn7d/ud9FolKqqKnvMxGn957oNsqqqivz8fGtQ586dSzAYtBNsyYypwX1xamtrAWwc1e9Bux3KkSNHEgxVeXm5jSueOHGCBQsWJJXbX3ZNTQ333nsvQFLPy5Qfi8V49dVXAc8TSmXQjW5MmcuWLaOwsDDhxXA/x2Ixfvvb3wKeEX/wwQcB+M1vfsPQoUNZsmSJve+sWbMAL25cXFzMiRPe/mzus3b04rm6uOGGG+znoqIiZsyYYUdnAA8//LDVy/nz57n55psBrwP1twVTd9Fo1HqYJqXRr3+zoGvt2rXWoJsRnzFitbW11kC9+eabAHbC9KOPPrLGqLCwkC1bttgRycSJE+11Rs+mXe7evZvGxkbA6wimTJmS1MD6dWiMuf9zMvzt2XR8M2bMIC8vL2Wsf8CAATz00EP2O6WUXeS1bds2Vq9eDXgjoL/85S92buULX/hCwnYNdXV1dgHaoEGDEjop97ny8vLs3IrWmvXr19vJ8lWrVkmaYhpIDF0QBCFLyGiXZ3rxt99+m/3799uUNzO8A88ziUajNrthwoQJCWEV17sMBoM2Bgde3M/06lpr5s+fn5ZcrudgUqWMh+7iH7rX1dVZWaZPn85zzz1nZ+3Ncn///c0zGoqLi6murrbpjnPmzLHhHnOuOwpYvHix1Vk6z+WSzHOORqNs2LDB6vib3/ym/a60tNR6muCtyDShmZ07d1JVVdVmub+RM11c3ZhsiFTbIbjZEu6iHrMYzXiFWmsrF7RdOeoec+P8/vv4VzhqrZk9ezbgza0Yzz4SiRAKhfjb3/4GeOEwo7dIJEI4HLae7vbt2+0cgVsuJC6kqq6uTmjrkUiEy5cvA/D6668n1H8oFOLuu++2n/2jSIPRSXsjKHNfpRRHjx5l+/btgJflY9qlUoq8vDx7HzdbZtOmTRw/ftymGi9dujQhROrXpzm2YcMGqqurbbx88uTJdnFSc3MzxcXF1zSL6JNKxgy6O4FZW1tLQUGBfTnc1D1/PHDq1Klt0v5czERRKBRKaFimsXS28s1Q0437+cM1Zlh4+PBhW+bgwYN56KGHbKqim3eeLLfeMG7cOIqLi9m1axfgTfYl25fGXOeGBzrC3CeZ8XdjszU1NTbNzI2bmglmQywWs/Ht7373uzQ3Nyd9zmSymY5u+PDhCQbHxX9dUVFR0jCRv7xoNEpra6s1eK2trTbv3Mw1uM8QDAbt1guLFy+26yFMp2Ym5pqbm1mzZo0tyy+z0VMoFOLGG29kx44dALz//vvcddddgBdWqKurs/HnUCjE5z//+YT7XEsD5a68njBhQhvnwp3beeGFF/jc5z4HeO25urraPuOdd95p9T9x4sSE98Mt47777qOpqcne0+14/XNPSikb3zfG3EyEguf4wdUwnxjytohGBEEQsoSMeehu6ODBBx9k3bp1PPvsswD89Kc/TfDC/SmEt956K+Ct/mxpabFecUtLiw3NlJaWUlhYmNawzGTAJPP8TYqhWWEajUbtZFssFqOkpIQ//elP9pnMIo1AIEBubm5a+3u4oZudO3eya9cuvv71rwOe95NqVr+lpYUnnnjCfm4Pd3LPLOZJphtTLya84Hqily9f5uTJk9ZrcuUJh8Ptjpzg6sgmEAgkDOVT0VH2UUcbSRmvcdeuXXYnRn/oLBgMsm/fPrs6Nz8/PyGEB15mlfnO4NedX5bDhw/bdmrCJeCl+JWXl9tUvnPnztlRXDAYbLMRnZlU9+//A1fDJ1/84heT6sHg1u/EiRPt6K+uri6lPgAuXbqUMOIKh8P2Xv6VsFVVVfZe/hCZu+Ar2c6X5p4bN260yQ9r1qyxWWHgZcl8/PHHADabxt33paO211/IaAzdNVQrV67kd7/7HeAZTVNBsViMUChkc1dffPFFnnrqKcBrnMOHD+ell14C4MKFCzYsYNKx2jPkxgj+8pe/JCcnh0ceeQS4asTBi2HX1tby4osvWtmWL18OeOGdiooKG3Zw82M7s92oy8WLF5k1a5Y1RqnSG8GL6ZoUTv98gh/3O3fzq2Rorblw4QLgGSATuvjjH//YJt6bLleuXLGrQWOxmH05GxoabIijpwgEAkQiEdvxTJ482YZUlixZwujRo61B3rZtGxcuXOD++++317oyNzY22nmU6dOn20ys6667rk29mPpubW3l5MmTduOy6upq27Fv3bqV8ePHc+DAAcBLn3Xz9f2kSls0ue7gGV63I/KHKd26nzRpko19v/LKKyxevJgxY8YAXr28/fbbtu7vv//+NhlnpszGxkbb6b///vtUVlZaA9xe2082f2HSYg8dOsTIkSMBGDJkCPX19TZdeffu3TYka9Jqf/WrXwG0eXf7Mxk16P7YnT9/GK7G040XXlZWxgsvvAB4lX7ixAnOnTsHeClnn/70pwFvYY/JOTb38WOOLVy4kNraWjtx4zaKcDjM6tWr7ejh7Nmzds+K+vp6RowYYY1BeXm5TUEzC2W6gn8OoT06yilPF9crXb16tV1W/vjjj9s00ZKSkoTJzo5kcydFz549a3PI77rrLuuJVVdXW0+2pzHlr1q1iq1btwLw8ssv2936wJuTKSsrS1iUZNrF2bNnqaursxOMgUDAGvdkMrtzBoFAwNZNa2srTz/9NOCl3N5yyy288Yb3f2HMXIW5f6p7+nXttpF0ct5d2Yzj8dZbb1l9mPsYffjLAOzeKQBPP/20vef06dNZuXJlu6nAqWSKRqP2nSktLbU6O3jwYELHUFpaardaUEol7JCZ6t3tj0gMXRAEIUvoU5n6yZb+Q+KQcfLkyfzoRz8CvKyD5uZmG4I5duyYXWiTLOboL8vEcRcuXEhFRUWbYar72WQ3XL582Q6Nw+Ew58+ft8vRd+/ezfe///3OP7hPLrf83toGwH1ed1je1NSUEBPtTCjJPe+GG25IWDQ0YsSI7oqcdtmA3V9/wYIFRKPRlKtm/YucXJnbQ2ttvcTy8vIEnd188812VLdy5Ur2799v478m+yaZzO2hlLJhnM5eZ/jsZz/LggULEhYB+ed83PNzcnISQpqG7owSg8Fg2unEBhMzN7L4393+TIcGXSk1FngWGAlo4Amt9X8qpYYCG4FCoBb4ita6oTvCmCGbG8P2DzndCZBwOExeXp6dVNq0aZMdls2fP7/DGLoxzI8++ihTpkyxkzftLbd3J3gGDx7M3r177TC8rKzMNu7OGD43NTAnJ8e+YJnClb2rxjzZPf0rbA3X+mV0y/bnkvv/gUqq6/z4w4NKKbtqtKWlhYKCAjtncNNNN9ndHRsbG2loaLArac+dO2fbVGf0m5eXx9q1a5M+U7rEYrE2k9kdbTVh6s014t3dvta/h0wq/CHaRx99FCDtd7c/kM6TR4BHtNbFwBzgO0qpYuAnwFat9RRga/xvQRAEIUN06KFrrU8Bp+KfLyilKoAxwDKgNH7aOmAb8OOuChIIBGwGwYkTJ1LOmPtnyYPBoN205+TJk2kP39wwzvTp0xMmfNrr4V15Zs+eTTQaTfhnDV3ZOMidTKqtrbVZB5kinQyFrtzTvb43vSi3bL/H3Z4cfplTnWMw4ZmOwjSpwk2d1W93/zdne4kCPXlNV+ToCK0106dPB0j73e0PdCqGrpQqBG4BdgMj48Ye4DReSKZLmDCKidtu3rzZzv6b7QDay0k217lL5NPBjaGniz+m6G5C1dWQxKVLl+wy8ZMnT9qsCn95Qve5lvr0zwG1F8px5ZE67jxulotwlbQNulKqAHgJ+IHWutHnKWulVNJgo1JqLbAWSNgHxHcOgN297dChQwl7sqSbHteVpf3QvbibG4PtzIvpGv+Ghgb72Syo6IqnL2SWdNuB1GnPIHu5tCUtTSilwnjG/Hmt9cvxw2eUUqPi348C6pNdq7V+Qms9S2s9y2yALwiCIPQ86WS5KOBJoEJr/R/OV68Da4Bfxn+/luTytHG9lmnTprX5n4/pXNtVz6c7PXxXr20vpa872SSC0F8Qz7wt6YRc5gJfB/6llNofP/YzPEP+glLqAeAY8JWeEspNF+sPleY+r8RUBUHoKulkuewEUlmYz/asOIIgCEJX6VMrRQ39zUvtb88rCMK1IfvjGYIgCP0EMeiCIAhZghh0QRCELKFPxtCFTybmf6umQzr/1NpPZzKe0vlPUYKQbYiHLgiCkCWIQRcEQcgSxKALgiBkCWLQBUEQsgQx6IIgCFmCGHRBEIQsQQy6IAhCliAGXRAEIUsQgy4IgpAliEEXBEHIEsSgC4IgZAli0AVBELIEMeiCIAhZghh0QRCELEEMuiAIQpYgBl0QBCFLEIMuCIKQJSitde8VptRHwCXgbK8V+slgOKKTZIhekiN6SU4262W81vr6jk7qVYMOoJTaq7We1auF9nFEJ8kRvSRH9JIc0YuEXARBELIGMeiCIAhZQiYM+hMZKLOvIzpJjuglOaKX5PR7vfR6DF0QBEG4NkjIRRAEIUsQgy4IgpAl9JpBV0otUkpVKqVqlFI/6a1y+yJKqVql1L+UUvuVUnvjx4Yqpf6ilKqO/x6SaTmvNUqpp5RS9Uqp951jSfWgPP4r3n4OKKVuzZzk144UOvl3pdSJeHvZr5Ra4nz307hOKpVS92VG6muPUmqsUuptpdRBpdQHSqnvx4/36/bip1cMulIqCPw3sBgoBlYqpYp7o+w+zF1a65lO3uxPgK1a6ynA1vjf2c4zwCLfsVR6WAxMif+sBR7tJRl7m2doqxOA38Tby0yt9RsA8XeoDLgpfs3/xN+1bCQCPKK1LgbmAN+JP39/by8J9JaHfjtQo7U+orVuATYAy3qp7E8Ky4B18c/rgH/LoCy9gtZ6O/Cx73AqPSwDntUe/wAGK6VG9Y6kvUcKnaRiGbBBa92stT4K1OC9a1mH1vqU1vq9+OcLQAUwhn7eXvz0lkEfA9Q5fx+PH+uvaGCLUqpcKbU2fmyk1vpU/PNpYGRmRMs4qfTQ39vQw/HQwVNOOK5f6kQpVQjcAuxG2ksCMimaGeZprW/FGxZ+Rym1wP1Se7mk/T6fVPRgeRSYBMwETgG/zqw4mUMpVQC8BPxAa93ofiftpfcM+glgrPP3DfFj/RKt9Yn473rgFbxh8hkzJIz/rs+chBkllR76bRvSWp/RWke11jHgf7kaVulXOlFKhfGM+fNa65fjh6W9OPSWQX8XmKKUmqCUysGbyHm9l8ruUyilBiqlBpnPwOeA9/H0sSZ+2hrgtcxImHFS6eF14P549sIc4P+coXZW44v9fgmvvYCnkzKlVK5SagLeBOCe3pavN1BKKeBJoEJr/R/OV9JeXLTWvfIDLAGqgMPAz3ur3L72A0wE/hn/+cDoAhiGN0tfDfwVGJppWXtBF+vxQgiteDHOB1LpAVB4mVKHgX8BszItfy/q5PfxZz6AZ6hGOef/PK6TSmBxpuW/hnqZhxdOOQDsj/8s6e/txf8jS/8FQRCyBJkUFQRByBLEoAuCIGQJYtAFQRCyBDHogiAIWYIYdEEQhCxBDLogCEKWIAZdEAQhS/h/PuXXMzVwX2gAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2065d3c8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20684080>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20628c18>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa205d1860>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa205f4cf8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2059ca20>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGrlJREFUeJztnWtwVMe1qL+e0YwEEg5gDFEwIPEQRDYY28JGBoLi2A6PSrhOmRMpEJPk2LgqcZJTcaXyOH/Oj/xIVSonOSd1LrZv+YFjB4zLduwkNjjBJpjgCCNDiI3QAxAenkJYXF56zvT5saebnmFGGj2YEaP1Vanmsffs7l7de+21Vq9uKa01giAIwrWPL9MVEARBEAYHUeiCIAhZgih0QRCELEEUuiAIQpYgCl0QBCFLEIUuCIKQJQxIoSulliil6pRSjUqpHw9WpQRBEIS+o/qbh66U8gP1wL3AUeB9oEprvX/wqicIgiCkykAs9DuARq31Ia11J7ARWDE41RIEQRD6Ss4AfjsRCDmfjwJ39vSDcePG6aKiogEUKQjXJt3d3X3+jd/vT/lcpVSfry9cO9TU1LRorW/o7byBKPSUUEqtBdYCTJ48mffff/9qFykIQ47Tp0/3+Tdjx45N+dy+KH/h2sPn8x1J6bwBlHEMmOR8vjH6XQxa6ye11mVa67Ibbuj1ASMIgiD0k4Eo9PeBGUqpYqVUEKgEXh+cal0dtNZEIhEikUhGyjdlZ8uGaD3J07QzW9oq9I1kYz3T92C20++Qi9a6Wyn1KLAF8ANPa60/GkhlTOdfrXigUiqjsUafL7vS/nuS52C1tbu728affT4fgUDAlh1PR0cH4I2jvLy8QSlf6B/J+j/T92C2M6AYutb6DeCNQaqLIAiCMACu+qRoXzBP7o6ODrq6ugDIz8/v9Ynuum/mXPPqunx1dXUcOnQIgCVLlgzYiowPKfR0va6uLrZs2QJASUkJJSUl9rcdHR32/YgRIwZUp6tNMnl+8YtfxO/3237bsmULJSUlALatqVpmpj99Ph+7d+9mx44dAOTk5PC9730P8PrXvebGjRsJhbykq/z8fB566CGCwWCv7ejJK4z/rrfw0bVuefY0nvvSf11dXbz11lsAzJgxI2as19XVcfDgQQCWLl2adV5rphkSCt0MloaGBgBee+01pk6dCsCKFSvw+Xw9DqZUBoVSivz8/EFR4u5DI9lNH/99IBCgoKAg5ph5bW5u5pVXXgFg/PjxPPDAAz2GFjKNqVMieZp6FxQUxNS9L+0w14xEItx+++20trYCsGvXLo4c8Sb7i4uLUUpx4MABwFMUd911FwCLFi1KmvVh+i++D1Ih1XP7ovwyTW/j2dCX9gQCgZiHqVtGQUFBjw9aYWAMCYWulKK7u5vf/e53ACxYsIB77rkH8G5qdzDF3yxnz55l165dgBdvLS4uBuCzn/1szLkNDQ2cO3eOJUuWDLiunZ2dgKdEQqEQOTmeGBcuXMjIkSNjzjcKftu2bVx//fWAZ7W4TJ48mYceegiAX/7yl7z33nssXrzYtn+oWDHxD954eWqt2bZtGwDXX399TDvb29vte5/PZ2XY1dVFTk6OfRC4cXKfz0c4HKaxsRHwUvNM2UVFRVy4cIH33nsPgGAwaOPmeXl5SSfdTP8ZK/Hw4cMEAoEYeTc3NwNev2itbd3b2trIz88HvLHW0dFhx9fIkSPJzc21ZaRDqbueTH9xx/PBgwetPAAWL15sx++pU6esPMzv4jFzGLt27eL2228HLqdeGnmfOnXKHvP5fFfIycyXaK2tjE1bjXyDwSBKKXvfCZcZGppCEARBGDBD5hHX1dXFqFGjAKzrDFdaAkop+9R+4YUXGDlyJGPGjLHHjLX+wQcfUFlZSVtbGwA1NTWEQiFOnjwJwPLly1O2cFwrua6ujtdeew3AWhombvz444+zfPlyAGbOnElnZyfPP/88ALNmzbIx9KlTp9rwC0A4HLafy8rKuHjxYq/ySieuVXbu3DlqamoACIVCnDp1CoBly5bx1FNPUVpaCngxdOMtjRo1ipMnT/Lqq68C0NnZyeTJkwHP0g6FQhw9etSW8e1vfxuA3NxcPvnkEzseWltbbZ8ppfjTn/5k+2D79u3MnTvX1tnt00T9V1hYCHieRHt7O48//jjgWfembsYibWlpAeDEiRO8/fbbABQWFlJSUkI4HAbg3XffZcqUKQA2ZHa1s7b6a5m7494dz4WFhVYeAE888YS1iidPnpzUQg+Hw/j9fnbv3g3AoUOHrKf2ox/9iNraWvbv97Z4GjFihB0z999/f0zc3ufz2bG1ZcsWxowZwy233AJ4HpDx1Orr67n33nspLy+37RkqXmymGTIKHS4PErdz4t3Xo0ePsn79egAmTpzIypUrY+Kl5gb72c9+xo4dO1i4cCHgKcrDhw9z0003XVFeT2it8fl8bNy4EfAUQmVlJeAp7fhzzXk//elPUUrZicHCwkKr+I1Lm6gexg0dKsTfLIFAgDvv9HZ4iJfnzJkzraLs6uqKiZUWFRXZm9NVfuXl5ZSXl9s4+fr1663bHQwG2bNnD9ddd50t+/DhwwDs2bOHiRMnWuXU2tqaNNzl8/nYsGED4Ln+K1euvKLv6urqANiwYUNMmyKRCJMmeevnlFJW2S1evBh3G4vCwkJ++9vfAlBdXc2iRYvsWBysVZzufdDV1WUVaFlZWcplmPEMXluNPODK8VxfX2/l5sokvi5+v59wOGxDbC0tLTaMEwwGycnJYcGCBYDX96NHj05Yn0gkQm1tLQBTpkxh5cqVtk+PHj3K5s2bAc8guu2222L6V/AQSQiCIGQJQ8ZCd10v10I1780T/80332TcuHEAfOMb3wAuW+Xu037mzJm8++67drLr5MmTBINBa9G1t7f3uPjErUN9fb214KqqqqzVbco1lsqtt95qXca///3vLFy40HoIb731FmbrA+OOJ/IQhtrqSp/PZ63S3NxcRowYwccffwx41peRwYULF1i0aJFNV7vhhhusJ2JcctdiN1a4WTVowmZz58615eXn55OXl2fDKqdPn7aufGdnJ2vXro0JfxkrNX4ivaGhgfr6egAqKiqYOXPmFZOm06dPB7wsjDlz5sQcc1PuDJMmTbITuuCFlUyZ7e3tMashe7Ig41Nu+5IauHXrVgBuueWWXhdSuWPKTCzX19dbecTXBWDatGk2FGi8q0RtMfeSGd+1tbXccccdABw5ciTGuj98+LD1cCG2zZcuXeLEiRMA/OAHPyA3N9dmNT377LO2j1atWtVjW4czQ0KhmxnsRG5jJBLB7/fzwQcfAHD8+HGbEeIeB2/QusrdvAdPgWit2bNnD+CFa1LNQd6wYQPTpk0DvJxqM/D9fn+MYnZviO7ubpu9A/Dxxx9bF91kbyRqr1IqJiSTanywra3NhqcSXdOtZyo3v1Gqf/vb3/jHP/4BwIQJE7j77rttfbTW7N27F4CvfOUrdHd3W2VfVFRkz+vq6rKycutkZOHKbfr06bZ+1dXV7Nu3z36eN28en/rUpwC45557qKmpsUq2qKjI9rdJczXlvfjii9Z1v/POO69I1QNPmRjZuPJ36xcKhezD3O/3x7TJPFhMG3w+X0r91ts55vrxylZrbZWtWU7vjuf467rrMl588UXAi0sbeZhz3HF/6dIlG0PPycmx5YCnmE2GUXNzM+Xl5XYOIycnh48+8haNm/kI009+v9+mJMe33+/387WvfQ3wjAWtNX/5y19s3b761a/GyORaSQ1NJ0NCoft8Pvbv38/Zs2ftZ/dYOBy2FhZg47Tx57oDsr6+nhEjRliFumDBAvx+v51g6ynm6N7wTU1NADaOGm9Buw+UQ4cOxSiqmpoaG1c8duwYn/vc5xLWO77sxsZG7r33XoCEqVnuTf773/8e8CyhZArdyMaUuWLFCoqKimJuDPd9JBLh17/+NeAp8YcffhiAX/3qV4wdO5Zly5bZ65aVlQFeqlppaSnHjnn7s7lt7e3Gc2Vx44032vczZ85kzpw51jsDePTRR61czp49y8033wx4D9B4mZq+C4fD1uo2KY3x55oFXWvXrrUK3Xh8Rok1NTVZBfXmm2/GTJiePn3aWo7FxcVs2bLFKsPi4mL7QDFyNuOyurqac+fOAV4664wZMxI++OLHa35+vj2vt/UV8ePZPPjmzJkTk+IZf42RI0fyyCOP2GNKKbvIa9u2baxevRqAxsZG/vznP9u5lS996Usx2zWEQiG7AG3UqFExnrg7NvLy8uzcitaaDRs22MnyVatWSZpiCkgMXRAEIUvI6CPPPMXfeecd9u7da1PejHsHnoUSDodtdkNxcXFMWMW1Lv1+v43BgRf3M091rTWLFi1KqV6u5WBSpYyF7hJv6YVCIVuX2bNn8/zzz9tZe7PcP/76po2G0tJSGhoabLrj/PnzbbjHnOt6AUuXLrUyS6VdLoks53A4zMaNG62Mv/Wtb9ljFRUV1kIFb0WmCc3s2LGD+vr6K5b7m3qmiisbkw2RbDsEN1vCWMNwOYZurEJTH0NPITY3zh9/nfgVjpFIxHooVVVV1rI3sXUT729ra7PndXd3EwgErKW7fft2O0fgXtdg4t0NDQ0xY727u9um5L7++usx7c/JyeHuu++27+O9yHiZ9ORBmXGllOLw4cNs374d8LJ8zLhUSpGXl2ev42bLbN68maNHj9pU4+XLl8eESF1c73fjxo00NDRYr2f69Ol2cVJHRwelpaWDsrAq28iYQncnMJuamigoKGDevHlAbOpefDywpKTkirQ/F5MnnJOTEzOwzGDpa+cbV9ON+8W7v8YtPHjwoC1z9OjRPPLIIzZV0c07T5Rbb5g8eTKlpaXs3LkT8Cb7Eu1LY37nhgd6w1wnkfJ35wEaGxttmpkbNzUTzIZIJGLj29/97nfp6OhI2M5EdXPd7kQhhmTt7QtuzrrJiY9/mJq5DBNXXrp0qV0PYRS6mRfo7Oy0E/FKKV5++WWbX71///4YBXPixAlefvllwAvP/PWvfwW8sEIoFLKGQmFhId/85jcTysZcy7y6Ct2N0fdlTLsTxsXFxUnlAbBp0ybuu+8+wBvPDQ0Nto133XWXrcvUqVNj7g/3gXTffffR1tZmr5mbm3tF0oMrN9MPRpmbiVDwDD+4HOYTRX4lIhFBEIQsIWMWuhs6ePjhh1m/fj3PPfccAD/5yU9irPD4FMLbbrsN8FZ/dnZ2Wqu4s7PThmYqKiooKipKyS0zGTCJLH+TYmhWmIbDYTvZFolEKC8v549//KNtk1mk4fP5yM3NjXGFk+GGbnbs2MHOnTv5+te/DnjWT7JZ/c7OTp588kn7vifcyT2zmCeRbEy/mPCCUspaV5cuXeL48ePWanLrEwgEevSc4LJn4/P5Ylz5ZPQ3i8H8zliNO3futDsxxofO/H4/e/bssatzR4wYERPCAy+zyhwzRCIRwuGwlV18XQ8ePGjHqQmXgJfiV1NTY1P5zpw5Y704v98fsxGd1tpOqsfv/2PKAPjyl7/co6zc/p06dar1/kKhUFJ5AFy8eDHG4woEAvZagUAgpsz6+np7LVeGQMyCr3iPwPWaN23aZJMf1qxZE7Nwq66ujk8++QTwUlvNXi/mGkN5M7t0ktEYuquoqqqq+M1vfgN4StN0UCQSIScnx+auvvTSSzz99NOANzjHjRtnXdvz58/bsIBJx+pJkRsl+POf/5xgMMhjjz0GELNse/78+TQ1NfHSSy/Zuj3wwAOAF96pra21YQc3P7a/mzNduHCBsrIyq4ySpTeCF9M1KZzx8wnxuMfcza8SobXm/PnzgKeATErfH/7whyvivanS3t5uV4NGIhF7c7a2ttoQx2Bh2mpivNOmTbPbDixdujRmhek777zD+fPnefDBB4FYmbS3t3Pu3DkbHpk9e7bNxLruuuuu6Bd3Fefx48ftxmUNDQ32wb5161amTJnCvn37AC991s3XjydZ2mJ7e7s9dvHiRUaOHJk0TOn2/bRp06xcXn31VSsPU4aRB8CDDz54RcaZqce5c+fsQ//DDz+krq7OKuCewn+JQmomLfbAgQNMmDABgDFjxtDc3GzTlaurq21I1qTV/uIXvwC88WzuXZPuOFwVe0YVenzszs1vNph4urHCKysr2bRpE+B1+rFjxzhz5gzgpZx9+tOfBryFPfPnz+/RQjffLV68mKamJjtx41qagUCA1atXW++hpaXFLrlubm5m/PjxVhnU1NTYFDSzUKY/xM8h9MRg/Wce1ypdvXq1/WfeTzzxhE0TLS8vj5ns7K1ubry3paXF5pB//vOft7JvaGiwluxg4iqxVatW2bmVV155hWAwaMsvKSmhsrIyZg7BHGtpaSEUCtkJRp/PZ5V7ojq7lrXP57N909XVxTPPPAN4MfNbb72VN97w/i+Mmasw1092zfg1D65yTSXn3a2bMTzefvttKw9zHSMP89m9F83eKQDPPPOMvebs2bOpqqqKecClOqcTDoftPVNRUWFltn///pj2VlRU2HkQ4zWa1NgjR47EbDUwnJEYuiAIQpYwpDL1Ey39h1hra/r06fzwhz8EvPSljo4OG4I5cuSIXWiTKOYYX5aJ4y5evJja2tor3FT3/Zo1awAvjmxc40AgwNmzZ216WnV1Nd///vf73vC4ernlp2sbALe9rlve1tYWEzvuizvrnnfjjTfGLBpKB275X/jCFwBvwZO7CMmEQhKlWPalzlpr6+HV1NTEyOzmm2+2mTJVVVXs3bvXxn8/85nPJKxvKvTHO4uXiZEHXJ73SXZ+MBiMCWkOpB4Gv99v04lTbb/WmmAwSEVFBQDr1q2TjJcovSp0pdQk4DlgAqCBJ7XW/6WUGgu8CBQBTcC/aK1bB1IZ47Il2nrUVXAmJBIIBMjLy7OTSps3b7Zu2aJFi3qNoRvFvG7dOmbMmGEnb3pabu9O8IwePZrdu3dbN7yystIO7r4oPjd1KxgM2hssU7h1768yT3TNZA+ndNyMRr7xk7c99XUqdXbHqVk12tnZSUFBgZ0zuOmmm7j//vsBL/bc2tpqwwVnzpyJWUWa6iRxXl4ea9eutW3qD5FIpE/yMMdN+an+JpV6QO8K3T3e3d3NunXrAGLu3Z7mnIYDqfRCN/CY1roUmA98RylVCvwY2Kq1ngFsjX4WBEEQMkSvFrrW+gRwIvr+vFKqFpgIrAAqoqetB7YBP+pvRXw+n80gOHbsWMyMebJVlVpr/H6/3bTn+PHjKa8GdcM4s2fPjpnwSXVfjHnz5hEOh2P+WUN/Ng5yLYqmpiabdZApktV9IJkD8Rs/pZtkfdpTX6dS5/iwkvvq4q72HT9+fK/X6g03tbS/9JQoMJi/SbUefWm/1truz5PqvTsc6FMMXSlVBNwKVAMTosoe4CReSKZfmDCKGfRbtmyxs/9mO4Celmu76Wl9wY2hp0p8TNHdhKq/IYmLFy/alYTHjx+3WRXx5QlDm/g5oGSrXuNXxg4kjBVfznDB/T+wLsNRFi4pK3SlVAHwMvBvWutzcYNVK6USBhuVUmuBtUDMPiBx5wDY3dsOHDgQsydLqulx/VnaDwOLAbpLqftqYZjzW1tb7XuzoGI436zXKj2Ng2Re5mCUN1xxY+/DXRaGlLSYUiqAp8xf0Fq/Ev36lFKqMHq8EGhO9Fut9ZNa6zKtdZnZAF8QBEEYfFLJclHAU0Ct1vo/nUOvA2uAn0dfX0vw85Rxn7CzZs1i1qxZff5tf5/SA4m79fe3PaX0DeeVboKQKsM9Xp6IVEIuC4CvA/9USu2NfvdTPEW+SSn1r8AR4F8Gq1Juuthw6DS3veI+CoLQX1LJctkBJNMwXxjc6giCIAj9ZUitFDUMNyt1uLVXEISrQ/bHMwRBEIYJotAFQRCyBFHogiAIWcKQjKELQrrpzxyG+UcnqWB2YuwL1/Le3jInlBnEQhcEQcgSRKELgiBkCaLQBUEQsgRR6IIgCFmCKHRBEIQsQRS6IAhCliAKXRAEIUsQhS4IgpAliEIXBEHIEkShC4IgZAmi0AVBELIEUeiCIAhZgih0QRCELEEUuiAIQpYgCl0QBCFLEIUuCIKQJYhCFwRByBKU1jp9hSl1GrgItKSt0GuDcYhMEiFySYzIJTHZLJcpWusbejsprQodQCm1W2tdltZChzgik8SIXBIjckmMyEVCLoIgCFmDKHRBEIQsIRMK/ckMlDnUEZkkRuSSGJFLYoa9XNIeQxcEQRCuDhJyEQRByBJEoQuCIGQJaVPoSqklSqk6pVSjUurH6Sp3KKKUalJK/VMptVcptTv63Vil1J+VUg3R1zGZrufVRin1tFKqWSn1ofNdQjkoj/+Ojp99SqnbMlfzq0cSmfyHUupYdLzsVUotc479JCqTOqXUFzNT66uPUmqSUuodpdR+pdRHSqnvR78f1uMlnrQodKWUH/gfYClQClQppUrTUfYQ5vNa67lO3uyPga1a6xnA1ujnbOdZYEncd8nksBSYEf1bC6xLUx3TzbNcKROAX0XHy1yt9RsA0XuoErgp+pv/G73XspFu4DGtdSkwH/hOtP3DfbzEkC4L/Q6gUWt9SGvdCWwEVqSp7GuFFcD66Pv1wP/JYF3SgtZ6O/BJ3NfJ5LACeE57/B0YrZQqTE9N00cSmSRjBbBRa92htT4MNOLda1mH1vqE1vqD6PvzQC0wkWE+XuJJl0KfCIScz0ej3w1XNPCWUqpGKbU2+t0ErfWJ6PuTwITMVC3jJJPDcB9Dj0ZDB0874bhhKROlVBFwK1CNjJcYZFI0MyzUWt+G5xZ+Ryn1Ofeg9nJJh30+qcjBsg6YBswFTgC/zGx1ModSqgB4Gfg3rfU595iMl/Qp9GPAJOfzjdHvhiVa62PR12bgVTw3+ZRxCaOvzZmrYUZJJodhO4a01qe01mGtdQT4f1wOqwwrmSilAnjK/AWt9SvRr2W8OKRLob8PzFBKFSulgngTOa+nqewhhVIqXyk1yrwH7gM+xJPHmuhpa4DXMlPDjJNMDq8DD0azF+YD/99xtbOauNjv/XjjBTyZVCqlcpVSxXgTgLvSXb90oJRSwFNArdb6P51DMl5ctNZp+QOWAfXAQeDf01XuUPsDpgL/iP59ZGQBXI83S98A/AUYm+m6pkEWG/BCCF14Mc5/TSYHQOFlSh0E/gmUZbr+aZTJb6Nt3oenqAqd8/89KpM6YGmm638V5bIQL5yyD9gb/Vs23MdL/J8s/RcEQcgSZFJUEAQhSxCFLgiCkCWIQhcEQcgSRKELgiBkCaLQBUEQsgRR6IIgCFmCKHRBEIQs4X8BinP5L8JIgX4AAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa205c5588>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGqpJREFUeJztnXtwVdW5wH9rn5wQCFgQFBkUEiBAoyBiUCJQ8M1jWq4daYOgdFrFmda+xulU23/uH/2jM53e9t7OvRbvqMVHAR212lbBFxQoyiOCVIEkPAIBBAyGGx55nrPuH/usxTo755ycPMgJJ99vJnP2Ofux1vrW2t/+vm99a0dprREEQRAuf7xMV0AQBEHoHkShC4IgZAmi0AVBELIEUeiCIAhZgih0QRCELEEUuiAIQpbQJYWulJqrlKpQSu1XSj3RXZUSBEEQOo7qbB66UioEVAJ3A0eB7cBirfWe7queIAiCkC5dsdBvAfZrrQ9qrZuB1cDC7qmWIAiC0FFyunDuSKDG+X4UuDXVCcOGDdMFBQVdKFIQLk864wlHo9G0jw2FQh2+vnD5UF5eXqu1vqq947qi0NNCKbUcWA4watQotm/ffqmLFIReR2tra4fPOX/+fNrHfuUrX+nw9YXLB8/zDqd1XBfKOAZc53y/NvZbHFrrp7XWJVrrkquuavcBIwiCIHSSrij07UCRUqpQKZULlAFvdk+1Lg1aa6LRaIdc2e7ElJ0tL0RLJU/Tzmxpq9AxIpEIkUikTf9rre0+ofvpdMhFa92qlHoMWAeEgGe11p91pTKm85VSXblMUpRSl+za6eB52ZX2n0qe3dXW1tZWG67wPI9wOGzLDtLU1AT44ygvL69byhc6R7KYvlJK4v2XkC7F0LXWbwFvdVNdBEEQhC5wySdFO4KxupqammhpaQEgPz+/XavadfnNsebTdfkqKio4ePAgAHPnzu2yFRkMKaS6XktLC+vWrQNg/PjxjB8/3p7b1NRkt/v379+lOl1qksnz3nvvJRQK2X5bt24d48ePB7BtTdc7Mv3peR47duxg8+bNAOTk5PCjH/0I8PvXvebq1aupqfGTrvLz83n44YfJzc1ttx3m0+27dD3FYDghk95fd5BqPHek/xKNddOnFRUVHDhwAIB58+YRCoUSyru9UN3lLutLRa9Q6GawVFVVAfDGG28wZswYABYuXIjneSk7MB3FrJQiPz+/W5S4+9AI1iuZMgiHwwwcODBun/k8deoUr732GgBXX301999/f8rQQqYxdUokT1PvgQMHxtW9I+0w14xGo9x8883U1dUBsG3bNg4f9if7CwsLUUqxb98+wFcUt912GwCzZs1K6tab/gv2QaL2tUey4zqi/DJNe+PZ0JH2hMNh+vXrF1eG6dNBgwbF7UvG5SK/3kavUOhKKVpbW/nzn/8MwIwZM7jrrrsA/6YOPrnd72fOnGHbtm2AH28tLCwE4Ktf/WrcsVVVVdTX1zN37twu17W5uRnwlUhNTQ05Ob4YZ86cyYABA+KONwp+w4YNDB06FICioqK4Y0aNGsXDDz8MwG9/+1s+/PBDZs+ebdvfW2LvwQdvUJ5aazZs2ADA0KFD49rZ2Nhotz3PszJsaWkhJyfHPgjcOLnneUQiEfbv3w/4cVlTdkFBAefOnePDDz8EIDc318bN8/Lykk58m/4zVuKhQ4cIh8NW3lprTp48Cfj94tb9woUL9qHc2tpKY2OjHV8DBgywiiroPVwqXE+ms7jj+cCBA1YeALNnz7bj9+TJk4waNSql92LmMLZt28bUqVMB7Jg38j558iQ333wz4PdncHwbD89MnhrZR6NR27/hcBjP8+x9J1ykd2gKQRAEocv0mkdcS0sLgwYNArCuM7S1BJRS1hJ46aWXGDBgAEOGDLH7jLX+8ccfU1ZWRkNDAwDl5eXU1NRw4sQJABYsWJC2heNaERUVFbzxxhsA1tIwVsUf//hHFixYAMCECRNobm7mxRdfBGDixIk2rjhmzBhr6YGf4mW+l5SUdGhBSU/gWmX19fWUl5cDUFNTY63Z+fPn88wzz1BcXAz4MXTjLQ0aNIgTJ07w+uuvA9Dc3Gyt34KCAmpqajh69Kgt4/vf/z4A/fr148svv7Tjoa6uzvaZUoq///3vtg82btzIlClTbJ3dPk3UfyNGjAB8C7KxsZEVK1bYMk3djEVaW1sLwPHjx1m/fj0A11xzDePHj7fpd5s2bWL06NEANmR2qbO2OmOZu3Fyz/PixvOIESOsPABWrFhhvY5Ro0YltdAjkQihUIgdO3YAcPDgQeup/fznP2fPnj3s2eO/4ql///52zNx3333WEgffYjdja926dQwZMsT26YABA6x3VlVVxd13301paSnQu7zYTNNrFDpcHCRu5wTd16NHj7Jy5UoARo4cyaJFi+LipWZw/OpXv2Lz5s3MnDkT8BXloUOHuP7669uUlwoT/1u9ejXgK4SysjLAV9rBY81xv/jFL1BK2YnBESNGWMVvXNpE9fA8r1flbgdvlnA4zK23+m94CMpzwoQJVlG2tLTETUoWFBRw4403AvHKr7S0lNLSUhsnX7lypU1TzM3NZefOnVxxxRW27EOHDgGwc+dORo4caRV8XV1d0nCX53msWrUK8F3/RYsWtem7yspKAFatWhXXpkgkwrXXXmu/G2U3e/Zs3NdYjBgxghdeeAGArVu3MmvWrDhF1d20trbaVdfTpk0D0p/MNf25atUqKw9oO54rKyut3FyZmLJMOaFQiEgkYkNstbW1NoyTm5tLOBy29+GmTZsYPHhw3LXMdaLRKHv37gVg9OjRLFq0iPz8fMC/79euXQv4BtHUqVMTTmj3dUQSgiAIWUKvsdBdV9C1UM22eeK//fbbDBs2DIDvfOc7wEWr3J1NnzBhAps2bbKTXSdOnCA3N9dadI2NjSkXn7h1qKyspKKiAoDFixdbq9uUayyMm266ybqMH330ETNnzrSWyTvvvIN59YFxxxNZUr1tdaXnedYq7devH/379+fIkSOAb30ZGZw7d45Zs2bxzjvvAHDVVVdZT8S45K7Fbqxws9LUhM2mTJliy8vPzycvL8+GVb744gvryjc3N7N8+fK48JexhIMT6VVVVdYCnzNnDhMmTGgzaTp27FjAz84xngTEW7tmDABcd911dkIX/LCSObaxsTFuBW0qCzKYctuR1MAPPvgAIC7UlE45ZpK5srLSyiN4DPgyMaFAI5NEbTH3khnfe/fu5ZZbbgGguro6zro/ePCg9XDBb7O55vnz521I9Kc//Sl5eXk2q+m5556zHsADDzwgWTBJ6BUKPRqN0q9fv4SuaTQaJRQK8fHHHwN+HNNkhLj7gbh4nLsNvgLRWrNz507AD9ekclHd31atWmVveDen1uTQui6jW57J3gE4cuSIddFN9kai9iql4kIy6cYHGxoabHgq0TXdera3ilJrbZXqP//5Tz755BMAhg8fzh133GHro7Vm165dAHzzm9+ktbXVKvuCggJ7XEtLS1y+samTkYUrt3Hjxtn6bd26ld27d9vv06ZNsy+huuuuuygvL7dKtqCgwPa3SXM15a1Zs8aGY2699dY2qXrgZ7AY2bjyN30F/pyBUSqhUCiuTebBYtrgeV5a/dbeMa6RExxrJhwRjUbbLKV3x5Z7ntaaNWvWAH5c2sjDyMId9xcuXLAx9JycHPuqB/DDbSbD6NSpU5SWltoHS05ODp995i8aN/MRpp9CoZBNSTZlunV+4IEHAN94iEajvPfee/a4b3/723EyEaXell6h0D3PY8+ePZw5c8Z+d/dFIhFrYQE2Ths81h2QlZWV9O/f3yrUGTNmEAqF7ARbqrimewNUV1cDvkVm9rmKyX2gHDx4ME5RlZeXWwVw7Ngxvva1ryWsd7Ds/fv3c/fddwMkTM0y5UejUf7yl78AcPjw4aQK3cjGlLlw4UIKCgribgx3OxqN8vvf/x7wlfgjjzwCwO9+9zuuvPJK5s+fb69bUlIC+KlqxcXFHDvmv5/NbWu6MV0gLl49YcIEJk+ebL0zgMcee8zK5cyZM9xwww2A/wANytT0XSQSYfLkycDFlMbgsUbhL1++PE6hu3I7fPiwVVBvv/123ITpF198wZIlSwA/R37dunVWGRYWFjJy5EjgYt+Zcbl161bq6+sBP521qKgo4YMvKEOjzM12e+PZtKG6utoq/8mTJ8eleCaSyaOPPmr3KaXsIq8NGzawdOlSwLf43333XTu38vWvf922TynFkSNH7AI0Mx8CbT2pvLw8O7di5qPMYrElS5ZImmIaSAxdEAQhS8joI888xdevX8+uXbtsyptx78B/wkciEZvdUFhYGBdWca3LUCjE559/bq9/44032qe61ppZs2alVS/XQjfxRmOhuwQtvZqaGluXSZMm8eKLL9pZe7MEOnh900ZDcXExVVVVNt1x+vTpNtxjjnW9gHnz5lmZpdMul0SWcyQSYfXq1VbG3/3ud+2+OXPmWAsV/BWZJjSzefNmKisr2yz3N/VMF1c2Jhsi2esQ3GwJd/WhsfyMVWjqY0jlMbjWuamLuU4wOykajVoPZfHixXa/ia2beH9DQ4O10FtbWwmHw9bS3bhxo50jcK9rcFP13LHe2tpqw0RvvvkmQ4cOtfvC4TB33HEHEH8vge9Fmu9GJqnkYcaVUopDhw6xceNGwM/yMeNSKUVeXp69jpsts3btWmpqamyq8YIFC9rMdSSaO1u9ejWVlZXW6ykqKrKLk5qamiguLu6WhVXZRsYUetANHDhwoE2/clP3XJcX/EEYvLFczERRTk5O3MAyoZKOdr5xNd24X9C9NTnUBw4csGUOHjyYRx991KYqunnniXLrDaNGjaK4uJgtW7YA/mRfovfSmPOMLNKZSDXXSaT83djs/v37mTFjhr2uuXHMBLPBXb33wx/+kKampoTt7Mgkb3vhmURhomTluTnrJic++DA1cxkvv/wy4L9fxKyHMOE0My/Q0tJiJ+KVUnaNA/gK1oSGPM+jsLCQd999F4Dt27fb0FBeXh5HjhzhrbfesmUYWQN8+eWXdjs/P9/2VU5OTps2u/vSkZ0pz9wDhYWFSeUB8PLLL3PPPfcA/niuqqqyMr3ttttsXcaMGRN3f7ihlHvvvZeGhgZ7TffB687FmPqbtN+qqiqWLl3KuHHj7PFmDYB5iIoib4tIRBAEIUvImIXuhg4eeeQRVq5cyfPPPw/Ak08+2cb1NVRWVtr3RJSXl9Pc3Gyt4ubmZhuamTNnDgUFBWm5ZSYDJpHlb1IMTTpVJBKxk23RaJTS0lL+9re/2TaZRRqe59GvX7+0XkTkWk2bN29my5YtPPjgg4Bv/SSb1W9ububpp5+226lws0nMYp5EsjH9YlIMlbr4/uoLFy5w/PhxazW59QmHwyk9J7jo2Xie1+nFNslCVYmOMVbjli1b7ORaMHQWCoXYuXOnXZ3bv3//uBAe+JlVZp/BZJYY2QXrcuDAATtOTdgE/BS/8vJyG148ffq09eISycVMqgff/wPYUNA3vvGNuEnSIG7/jhkzxnp/NTU1SeUBfhqh63GZd6iYbbfNlZWV9lrBEJm74CvoEbhe85o1a2zyw7Jly+IWblVUVFjvxWTTmJBtsnu3L5LRGLqrqBYvXswf/vAHwFeapoOi0Sg5OTk2d/WVV17h2WefBfzBOWzYMF599VUAzp49a8MCJh0rlSI3SvDXv/41ubm5PP744wBxy7anT59OdXU1r7zyiq3b/fffD/jhnb1799qwg4n3mbZ1Jq3q3LlzlJSUWGWULL0R/Dxwk8IZnE8I4u5zX36VCK01Z8+eBXwFZGK1f/3rX9vEe9OlsbHRrgaNRqN2+8yZMzbE0V2YkJ2J8Y4dO9a+dmDevHlxK0zXr1/P2bNneeihh+y5bp3r6+vtPMqkSZNsJtYVV1zRpl9Mf7e0tHD8+HH74rKqqio7Rt577z1Gjx7N7t27AT991s3XD5IsbbGhocG24fz583EPomCY0u37sWPHWrm8/vrrVh7g94uRB8BDDz3UJuPMlFlfX28f+p9++ikVFRVWAaca+0FlDti02H379jF8+HAAhgwZwqlTp2y68tatW+NWxDY2NvKb3/wGoM2925fJqEIPxu6CMTW4eHMaK7ysrMzGO4cMGcKxY8c4ffo04KecXXPNNYC/sGf69OkpLXTz2+zZs6murrYTN+6gCIfDLF261HoPtbW19p0Vp06d4uqrr7bKoLy83KagmYUynSE4h5CK7vrPPK5VunTpUrusfMWKFTZNtLS0NG6yM914t+d51NbW2hzy22+/3cq+srKy0w+JVLhKbMmSJXZu5bXXXiM3N9eWP378eMrKyuJe2Wu2a2trqampsROMnudZ5W6s72CZpt2e59m+aWlp4bnnngP8d8BMnTrVxtDNwjP3/ETXDO5zx0g6Oe9u3Yzh8cEHH1h5mOsYeQTLAOy7U8Bf6GOuOWnSJBYvXtxhr8skPJh7Zs6cOVZme/bsiXswzJkzx86DKKXIycmxxluye7cvIjF0QRCELKFXZeonSl+CeGtr3Lhx/OxnPwP89KWmpiYbgjl8+LBdaJMo5hgsy2QHzJ49m71797ZxU93tZcuWAX4c2bjG4XCYM2fO2PS0rVu38uMf/7jjDQ/Uyy2/p14D4LbXdcsbGhriYqIdCSW5x1177bVxi4Z6Arf8O++8E/AXPLmLkIJzHMFFTunWWWttrcTy8vI4md1www02Q6OsrIydO3fa+K+7SK4jITqlVNz71ztynuHOO++08oCL8z7Jjs/NzY0LaRq64iWGQqG004kNJmZu6hK8d/sy7Sp0pdR1wPPAcEADT2ut/1MpdSWwBigAqoFvaa3rulIZ47IlevWoq+CMWxUOh8nLy7OTSmvXrrVu2axZs9qNoRvF/NRTT1FUVGQnb1Itt3cneAYPHsyOHTusG15WVmYHd0cUn5samJuba2+wTOHWvbPKPNE1kz2ceuJmNPINTt6m6ut06uyOU7NqtLm5mYEDB9p5guuvv94+GOrr66mrq7PK6PTp0yknNJORl5fH8uXLAVL+q71URKPRDsnD7Dflp3tOOvVIh2CI9qmnngJI+97tC6TT8lbgca11MTAd+IFSqhh4Anhfa10EvB/7LgiCIGSIdi10rfXnwOex7bNKqb3ASGAhMCd22EpgA/DzzlbE8zybQXDs2LGkM+bBWfJQKGRf2nP8+PG03Tc3jDNp0qS4CZ9UT3i3PtOmTSMSicT9s4bOvDjInUyqrq62WQeZIp0Mhc5cM5MvU0rWp6n6Op06B8NK7qfLuXPn7LbJ5Eh1rXTorGVuSJUo0J3ndKYe7aG1ZtKkSQBp37t9AdXBVXwFwEbgBuCI1npw7HcF1JnvySgpKdEme8LFuLbmf4qeP3/erp4rLi7OuDJIl86GJM6fP88//vEPwF9VuHTpUhvD7kqYQ7i0uCtFoe0ckNtvJhXQGBJuhkqy/g3+w45UdCZsI1w+eJ5XrrUuae+4tCdFlVIDgVeBn2it6wOWslZKJXwyKKWWA8uBuPeABI4BsG9v27dvX9w7WdJNj+vM0n7oWtzNXebcEcXrKuq6ujq7bRZUdMbSFzJLqnGQbMJd6DzyLpe2pCUJpVQYX5m/pLV+LfbzSaXUiNj+EcCpROdqrZ/WWpdorUvMC/AFQRCE7iedLBcFPAPs1Vr/h7PrTWAZ8OvY5xsJTk8b12qZOHEiEydO7PC5nbV8uvKE7+y5qVL6JMwiCO0jlnlb0gm5zAAeBP6llNoV++0X+Ir8ZaXU94DDwLe6q1LBGGO247b3cpkvEASh95FOlstmIJmGubN7qyMIgiB0ll61UtTQ16zUvtZeQRAuDdkfzxAEQegjiEIXBEHIEkShC4IgZAm9MoYuCJcD5qVw6dDef5RKRFeX9gt9D7HQBUEQsgRR6IIgCFmCKHRBEIQsQRS6IAhCliAKXRAEIUsQhS4IgpAliEIXBEHIEkShC4IgZAmi0AVBELIEUeiCIAhZgih0QRCELEEUuiAIQpYgCl0QBCFLEIUuCIKQJYhCFwRByBJEoQuCIGQJotAFQRCyBKW17rnClPoCOA/U9lihlwfDEJkkQuSSGJFLYrJZLqO11le1d1CPKnQApdQOrXVJjxbayxGZJEbkkhiRS2JELhJyEQRByBpEoQuCIGQJmVDoT2egzN6OyCQxIpfEiFwS0+fl0uMxdEEQBOHSICEXQRCELEEUuiAIQpbQYwpdKTVXKVWhlNqvlHqip8rtjSilqpVS/1JK7VJK7Yj9dqVS6l2lVFXsc0im63mpUUo9q5Q6pZT61PktoRyUz3/Fxs9updTUzNX80pFEJv+ulDoWGy+7lFLznX1PxmRSoZS6NzO1vvQopa5TSq1XSu1RSn2mlPpx7Pc+PV6C9IhCV0qFgP8G5gHFwGKlVHFPlN2LuV1rPcXJm30CeF9rXQS8H/ue7fwJmBv4LZkc5gFFsb/lwFM9VMee5k+0lQnA72LjZYrW+i2A2D1UBlwfO+d/YvdaNtIKPK61LgamAz+Itb+vj5c4espCvwXYr7U+qLVuBlYDC3uo7MuFhcDK2PZK4N8yWJceQWu9Efgy8HMyOSwEntc+HwGDlVIjeqamPUcSmSRjIbBaa92ktT4E7Me/17IOrfXnWuuPY9tngb3ASPr4eAnSUwp9JFDjfD8a+62vooF3lFLlSqnlsd+Ga60/j22fAIZnpmoZJ5kc+voYeiwWOnjWCcf1SZkopQqAm4CtyHiJQyZFM8NMrfVUfLfwB0qpr7k7tZ9L2ufzSUUOlqeAscAU4HPgt5mtTuZQSg0EXgV+orWud/fJeOk5hX4MuM75fm3stz6J1vpY7PMU8Dq+m3zSuISxz1OZq2FGSSaHPjuGtNYntdYRrXUU+F8uhlX6lEyUUmF8Zf6S1vq12M8yXhx6SqFvB4qUUoVKqVz8iZw3e6jsXoVSKl8pNchsA/cAn+LLY1nssGXAG5mpYcZJJoc3gYdi2QvTgf9zXO2sJhD7vQ9/vIAvkzKlVD+lVCH+BOC2nq5fT6CUUsAzwF6t9X84u2S8uGite+QPmA9UAgeAX/ZUub3tDxgDfBL7+8zIAhiKP0tfBbwHXJnpuvaALFbhhxBa8GOc30smB0DhZ0odAP4FlGS6/j0okxdibd6Nr6hGOMf/MiaTCmBeput/CeUyEz+cshvYFfub39fHS/BPlv4LgiBkCTIpKgiCkCWIQhcEQcgSRKELgiBkCaLQBUEQsgRR6IIgCFmCKHRBEIQsQRS6IAhClvD/aTz1lqf8s6MAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2056f1d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa206ce4e0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa3007d908>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXQAAABaCAYAAACosq2hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAGpFJREFUeJztnWtwVMe1qL+eh5AQOLwM5mEQLwEiEGwLGxkIYJOYRxJuUuZEBGJcObZclTjJqbhSeZw/50d+pCqVk5yTOtfBt/zAsQPYZTt2EhtIMAQTbDAyhNiWNRIgLIQBi8AVLz1mps+PPd3u2ZqRRg80ymh9VSrNzN6ze/Xq3qtXr169R2mtEQRBEP75CWRbAEEQBKF3EIMuCIKQI4hBFwRByBHEoAuCIOQIYtAFQRByBDHogiAIOUKPDLpSaoVSqlopVauU+mFvCSUIgiB0HdXdPHSlVBCIAJ8DTgFvA+u01u/3nniCIAhCpvTEQ78dqNVaH9datwJbgTW9I5YgCILQVUI9+O54oN55fwq4o6MvjBo1ShcVFfWgSEEYOFy7di3jc8PhcJevHwr15PYX+pLKyspGrfWNnZ133VtUKVUBVABMnDiRt99++3oXKQg5wXvvvZfxuaNHj+7y9W+8sVP70G/pTqhYKXUdJOkbAoHAyYzO60EZDcDNzvsJic+S0Fo/prUu1VqX/jN3IEEQhP5OTwz628B0pdRkpVQeUA680jtiXR+01sTjceLxeFbKN2XnygPROtKnqWeu1FXoGun6erbvwVyn2yEXrXVUKfUwsAMIAk9orTOfI6a+JnD9pkZKqaxOuwKB3Er770ifvVXXaDRKNBq11zSx4lTltrS0AF4/ys/P75Xyhe6Rrv2zfQ/mOj2KoWutXwVe7SVZBEEQhB7Qr5a5zcjd0tJCW1sbAIWFhZ2O6O70zZxr/rtTvurqao4fPw7AihUreuxF+kMKHV2vra2NHTt2AFBcXExxcbH9bktLi31dUFDQI5muN+n0ec899xAMBm277dixg+LiYgBb10w9M9OegUCAQ4cOsW/fPsDLyvjOd74DeO3rXnPr1q3U13tJV4WFhTzwwAPk5eV1Wg/z32276z1T7K901J+70n5tbW3s3LkTgOnTpyf19erqao4dOwbAypUrc27Wmm36hUE3naWmpgaAl19+mSlTpgCwZs0aAoFAh50pk06hlKKwsLBXjLg7aPjlSmcMwuEwQ4YMSTpm/p87d44XX3wR8LIV7r333g5DC9nGyJRKn0buIUOGJMnelXqYa8bjcW677TYuXLgAwMGDBzl50lvsnzx5MkopPvjgA8AzFHfeeScAixcvJhgMpry2aT9/G6Sq30Cgs/5s6IpOwuFw0mDqljFkyJAOB1qhZ/QLg66UIhqN8tvf/haAhQsXsnz5csC7qd3O5PcULl68yMGDBwEv3jp58mQAZs2alXRuTU0NTU1NrFixoseytra2Ap4Rqa+vt/m8ixYtYvDgwUnnGwO/Z88eRo4cCXhei8vEiRN54IEHAPj5z3/Om2++yZIlS2z9+4sX4x94/frUWrNnzx4ARo4cmVTP5uZm+zoQCFgdtrW1EQqF7EDgxskDgQCxWIza2loAgsGgLbuoqIjLly/z5ptvApCXl2fj5vn5+WkX3Uz7GS/xxIkThMNhq2+tNWfPngW8dnFlv3r1qh2Uo9EoLS0ttn8VFBQwaNCgjHXZG7gzme7i9udjx45ZfQAsWbLE9t+zZ88yceLEDmcvZg3j4MGD3HbbbQCMGDHCXttcxxwLBALt7mezXqK1tjo2dTX6NQOC5NG3p39YCkEQBKHH9Jshrq2tjaFDhwLYqTO09wSUUnbUfvbZZxk8eDDDhw+3x4y3/s4771BeXm5321VWVlJfX8+ZM2cAWL16dcYejuslV1dX8/LLLwNYT8PEjX/961+zevVqAGbMmEFrayvPPPMMADNnzrQx9ClTplhPDyAWi9n3paWlXLlypVN99SWuV9bU1ERlZSUA9fX11ptdtWoVjz/+OCUlJYAXQzezpaFDh3LmzBleeuklAFpbW633W1RURH19PadOnbJlfPOb3wRg0KBB/OMf/7D94cKFC7bNlFL88Y9/tG2wd+9e5s2bZ2V22zRV+40dOxbwZhLNzc1s2rTJlmlkMx5pY2MjAKdPn2b37t0A3HTTTRQXFxOLxQDYt2+f/Z4JmV3v0E13PHN/nNztz2PHjrX6ANi0aZP1iidOnJjWQ4/FYgSDQQ4dOgTA8ePH7UztBz/4AVVVVbz/vveIp4KCAttnvvzlLyfJEwgEbN/asWMHw4cP5zOf+QwAgwcPtjO1SCTC8uXLKSsrA/rXLDbb9BuDDp90Erdx/Itfp06dYvPmzQCMHz+etWvXJsVLzQ32k5/8hH379rFo0SLAM5QnTpxg9uzZ7crrCK01gUCArVu3At4NUF5eDnhG23+uOe/HP/4xSim7MDh27Fhr+P3btF05zDS0v+C/WcLhMHfc4T3hwa/PGTNmWEPZ1taWFCstKiqyN+cbb7zBpEmTACgrK6OsrMzGyTdv3myn3Xl5eRw+fJgbbrjBln3ixAkADh8+zPjx462Bv3DhQtpwVyAQYMuWLYA39V+7dm27totEIgBs2bIlqU6xWIwJEybY98bYLVmyBPcxFuPGjeM3v/kN4IUcFi1a1CshEX99TF9pa2uzBrS0tDSpvp31ayPPli1brD6gfX+ORCJWb65O/LIEg0FisZgNsTU2NtowTl5eHqFQiIULFwJe2w8bNizpOu6aSVVVFQCTJk1i7dq1tk1PnTrF9u3bAc8huvXWW1MuaA90RBOCIAg5Qr/x0N2pl+uhmtdmxH/ttdcYNWoUAPfffz/wiVfujvYzZszgjTfesItdZ86cIS8vz3pNzc3NHW4+cWWIRCJUV1cDsG7dOut1m3KNp3LLLbfYKeNbb73FokWL7Axh586d9tkZ4XA4bRpYf9tdGQgErFc6aNAgCgoK+PDDDwHP+zI6uHz5MosXL7bpajfeeKOdiZgpueuxGy/c7Bo0YbN58+bZ8goLC8nPz7dhlY8//thO5VtbW6moqEgKf5mZmn8hvaamxnrgS5cuZcaMGe0WTadOnQp4WRhmJgHJ3q7pAwA333wz0WjUljl06FB77rVr14jH40n9I53X7E+57Upq4K5duwCS5E2HW44bujD68J8Dnk5MKNCUkcobNveS6d9VVVXcfvvtAJw8eTLJuz9x4oSd4UJyna9evcpHH30EwPe+9z0GDRpks5qeeuoppk2bBsD69ev71T3Sn+gXBt2sYKdKNYvH4wSDQd555x3Ai2OajBD3OHjG0DXu5jV4BkRrzeHDhwEvXNPRFNX9bMuWLfaGLy4uth0/GAwmGWb3hohGozZ7B+DDDz+0U3STvZGqvkqppJBMpvHBa9eu2fBUqmu6cna2i1JrbY3qX//6V/72t78BMGbMGO666y4rj9aaI0eOAPCVr3yFaDRqjX1RUZE9r62tzerKlcnowtXbtGnTrHwHDhzg6NGj9v38+fP51Kc+BcDy5cuprKy0RraoqMi2t0lzNeVt27bNTt3vuOOOdql64BkToxtX/6atwFszMGEFf9uZgQW8LKZAIJBRu3V2jqmD39hqra2x9fd1v3xufbXWbNu2DYAJEyZYfUD7AeXq1as2hh4Khey2ffAMs8kwOnfuHGVlZXYNIxQK2QeLmXUF007BYNCmJPvrHwwG+drXvgZ4zoLWmj//+c9Wtq9+9au2Dl3Jix9I9AuDHggEeP/997l48aJ97x6LxWLWwwJsnNZ/rtshI5EIBQUF1qAuXLiQYDBoF9jS5SlD8g1QV1cHeB6ZOeYaJndAOX78eJKhqqystAagoaGBz372synl9pddW1vL5z73OSB1apZ7k//ud78DPE8onUE3ujFlrlmzhqKioqQb2X0dj8f55S9/CXhG/MEHHwTgF7/4BSNGjGDVqlX2uiZ+e/DgQUpKSmho8J7P5tY105gukBSvnjFjBnPnzrWzM4CHH37Y6uXixYt8+tOfBrwB1K9T03axWIy5c+cCn6Q0+s81Br+ioiLJoLt6O3nypDVQr732WtKC6ccff2yNUVFRETt37rQzkilTptjvGT2bfnngwAGampoAbyCYPn16yoHP318LCwvteYWFhZ32Z1OHuro6a/znzp2blOKZSicPPfSQPaaUspu89uzZw4YNGwDP4//Tn/5k11a++MUvJj2uob6+3m5AGzp0aNJM3O0b+fn5dm1Fa82WLVvsYvn69eslTTEDJIYuCIKQI2R1yDOj+O7duzly5IhNeTPTO/A8lFgsZrMbJk+enBRWcb3LYDBoY3Dgxf3MqK61ZvHixRnJ5XoOJt5oPHQXv6dXX19vZZkzZw7PPPOMXbU32/391zd1NJSUlFBTU2PTHRcsWGDDPeZcdxawcuVKq7NM6uWSynOOxWJs3brV6vgb3/iGPbZ06VLraYK3I9OEZvbt20ckEmm33d/ImSmubkw2RLrHIbjZEu6mHhNDN16hkcfQ0YzB9c6NLOY6/uwkrTXz588HvLUVczwajRIKhfjLX/4CeOEwo7doNEo4HLae7t69e+0agSu/wWykqqmpSerr0WjUpuS+8sorjBs3zh4Lh8PcddddQPK9BN4s0rw3OulIH6ZfKaU4ceIEe/fuBbwsH9MvlVLk5+fb67jZMtu3b+fUqVM21Xj16tVJIVK/Ps1nW7dupaamhvXr1wNeKM5sTmppaWHWrFm9nkWUC2TNoPungUOGDLE3h5u65055weuEHf06y+uvvw54HdHtWKazdLXxzVTTjfv5p7dmWnjs2DFb5rBhw3jooYdsqqKbd54qt94wceJESkpK2L9/P+At9qV6Lo35ntFFJotE5jqpjL+7DlBbW2vTzNy4qVlgNsTjcRvf/va3v01LS0vKenZlAas346JuzrrJifcPpmYt47nnngO854uY/RAmnGbWBdra2uxCfLrBEDz9zpo1yxq/d999l2XLlgFeWKG+vt7Gn0OhEF/4wheSrmP6aCwWs9eMxWJJBt1/zP2tgVAoZN+HQqGk9ZqRI0cyZswYwHOO0ukD4LnnnuPzn/884PXnmpoaq9M777zTyjJlypSk+8NdlL7nnnu4du2avaY78PrXnpRSNu03EomwYcMGuxCqtbZ7AEpLS1FKdRhmGqjI0CYIgpAjZM1Dd0MHDz74IJs3b+bpp58G4Ec/+lG7qa8hEolw6623At7uz9bWVusVt7a22tDM0qVLKSoqymhaZjJgUnn+JsXQ7DCNxWJ2sS0ej1NWVsYf/vAHWyezSSMQCDBo0KCMnu/hhm727dvH/v37+frXvw543k+6bJzW1lYee+wx+7oj3GwSs5knlW5Mu5gFPdcTunr1KqdPn7ZekytPOBzu9HctzXUCgcB19a6MXMZr3L9/v30Soz90FgwGOXz4sN2dW1BQkBTCAy+zyhwzmB9vSPeQr2PHjtl+Wltba483NzdTWVlpU/nOnz9vZ3HBYLDdg+jMorr/+T+ADQV96Utfsou+qXDbd8qUKXb2V19fn1YfAFeuXEmacYXDYXst/07YSCRir+UPkbkbvvwzAnfWvG3bNpv8cP/99ydt3IpEIpw/fx7AZtO4z33pzm+q5iJZjaG7hmrdunX86le/AjyjaRooHo8TCoVs7urzzz/PE088AXidc9SoUbzwwgsAXLp0yYYFTDpWR4bcGMGf/vSn5OXl8cgjjwCfGHHwYth1dXU8//zzVrZ7770X8MI7VVVVNuxg4n2mbt0JH1y+fJnS0lJrjNKlN4KX2mVSOP3rCX7cY+7Dr1KhtebSpUuAZ4BMSt/vf//7dvHeTGlubra7QePxuH198eJFG+LoLUxdTYx36tSp9rEDK1euTNphunv3bi5dusR9990HJOukubmZpqYmu44yZ84cm4l1ww03tEsNNN9tbW2loaHBPristrbWDuy7du1i0qRJHD16FPDSZ918fT9uRojbp0yuO3iG1/11IH+Y0m37qVOnWr289NJLVh/gtYvRB8B9993XLuPMlNnU1GQH/XfffZfq6mprgDvq+35jDti02KqqKhsOGjZsGGfPnrWDy1tvvWVz201a7c9+9jOAdvfuQCarBt0fu3Pzmw0mnm688PLychvvHD58OA0NDXbkrqio4KabbgK8DrBgwYIOPXTz2ZIlS6irq7MLN26nCIfDbNiwwc4eGhsb7Zbrc+fOMXr0aGsMKisrbQqa2SjTHfxrCB3RW7/M43qlGzZssD/mvWnTJpsmWlZWlrTY2Zls7qJoY2OjzSFftmyZ1X0kEun2INERrhFbv369XVt58cUXycvLs+UXFxdTXl6etP3cvG5sbKS+vt4uMAYCAWvcjfftL9PUOxgM2rZpa2vjySefBLyU21tuuYVXX/V+F8asVZjrp7umX9duH8kk592VzTger7/+utWHuY7Rh78MwD47BeDJJ5+015wzZw7r1q3r8qzLJDyYe2bZsmVWZ+bZL4alS5fadRClFKFQyDpv6e7dgYjE0AVBEHKEfpWpn2rrPyR7W9OmTeP73/8+4KUvtbS02BDMyZMn7UabVDFHf1km22PJkiVUVVW1m6a6rzdu3Ah4cWQzNQ6Hw1y8eNFuRz9w4ADf/e53u15xn1xu+X21xdmtrzstv3btWlJMtCuhJPe8CRMmJG0a6gvc8u+++27A2/DkbkLyr3H4Nzl1JrPbZ00KZ2VlJQUFBbb82bNn20dCrFu3jiNHjtj477hx41LKm0ndjOxd/Z7h7rvvtvqAT9Z90p2fl5eXFNI09GSWGAwGM04nNpiYuZHFf+8OZDo16Eqpm4GngTGABh7TWv+XUmoEsA0oAuqAf9FaX+iJMGbK5saw/VNOdwEkHA6Tn59vF5W2b99up2WLFy/uNIZuDPOjjz7K9OnT7eJNR9vt3QWeYcOGcejQITsNLy8vt527K4bPTQ3My8uzN1i2cGXvrjFPdc10g1Nf3IxGv/7F247auisyK6XsrtHW1lYKCwvtOsHs2bOZNWsW4MWeL1y4YHfSnj9/3vaprug3Pz+fiooKgG7/AlA8Hu+SPsxxU36m38lEDkhd/1S7Zk2o5tFHHwXI+N4dCGRS8yjwiNa6BFgAfEspVQL8ENiltZ4O7Eq8FwRBELJEpx661voj4KPE60tKqSpgPLAGWJo4bTOwB/hBdwUJBAI2g6ChoSHtirl/lTwYDNqH9pw+fTrj6ZsbxpkzZ07Sgk9HI7wrz/z584nFYkk/1pDpgqGLu5hUV1dnsw6yRSYZCt25ZjYfppSuTTtq60xkdo+btL1Uu4pdRo8e3em1MqGnv83ZUaJAb36nO3Kkwv/soTlz5gBkfO8OBLoUQ1dKFQG3AAeAMQljD3AGLyTTLUwYxcRtd+zYYVf/zeMAUnV285mbntYV3Bh6pvhjiu5DqLobkrhy5YrdJn769GmbVeEvT+jf+NeA0rWdP4wgbZwZrp7cLBfhEzI26EqpIcALwL9prZt8nrJWSqUMNiqlKoAKIOk5IL5zAOzT2z744IOkZ7Jkmh7Xna390LO4m7vNuSs3pmv8L1y4YF9v3Lix256+kF0y7QfSpr2DPMulPRlpQikVxjPmz2qtX0x8fFYpNTZxfCxwLtV3tdaPaa1Ltdal7vMmBEEQhN4lkywXBTwOVGmt/9M59AqwEfhp4v/LKb6eMa7XMnPmTGbOnNnl73bX8+nJCN/d73aU0teTbBJBGCiIZ96eTEIuC4GvA39XSh1JfPZjPEP+nFLqX4GTwL/0llBuuthAaDS3vhJTFQShu2SS5bIPSGdh7u5dcQRBEITu0q92ihoGmpc60OorCML1IffjGYIgCAMEMeiCIAg5ghh0QRCEHKFfxtAFQUj+sWVByATx0AVBEHIEMeiCIAg5ghh0QRCEHEEMuiAIQo4gBl0QBCFHEIMuCIKQI4hBFwRByBHEoAuCIOQIYtAFQRByBDHogiAIOYIYdEEQhBxBDLogCEKOIAZdEAQhRxCDLgiCkCOIQRcEQcgRxKALgiDkCGLQBUEQcgSlte67wpT6GLgCNPZZof8cjEJ0kgrRS2pEL6nJZb1M0lrf2NlJfWrQAZRSh7TWpX1aaD9HdJIa0UtqRC+pEb1IyEUQBCFnEIMuCIKQI2TDoD+WhTL7O6KT1IheUiN6Sc2A10ufx9AFQRCE64OEXARBEHIEMeiCIAg5Qp8ZdKXUCqVUtVKqVin1w74qtz+ilKpTSv1dKXVEKXUo8dkIpdSflFI1if/Dsy3n9UYp9YRS6pxS6l3ns5R6UB7/neg/R5VSt2ZP8utHGp38h1KqIdFfjiilVjnHfpTQSbVS6p7sSH39UUrdrJTarZR6Xyn1nlLqu4nPB3R/8dMnBl0pFQT+B1gJlADrlFIlfVF2P2aZ1nqekzf7Q2CX1no6sCvxPtd5Cljh+yydHlYC0xN/FcCjfSRjX/MU7XUC8ItEf5mntX4VIHEPlQOzE9/5v4l7LReJAo9orUuABcC3EvUf6P0lib7y0G8HarXWx7XWrcBWYE0flf3Pwhpgc+L1ZuD/ZFGWPkFrvRf4h+/jdHpYAzytPd4ChimlxvaNpH1HGp2kYw2wVWvdorU+AdTi3Ws5h9b6I631O4nXl4AqYDwDvL/46SuDPh6od96fSnw2UNHATqVUpVKqIvHZGK31R4nXZ4Ax2REt66TTw0DvQw8nQgdPOOG4AakTpVQRcAtwAOkvSciiaHZYpLW+FW9a+C2l1Gfdg9rLJR3w+aSiB8ujwFRgHvAR8PPsipM9lFJDgBeAf9NaN7nHpL/0nUFvAG523k9IfDYg0Vo3JP6fA17CmyafNVPCxP9z2ZMwq6TTw4DtQ1rrs1rrmNY6Dvw/PgmrDCidKKXCeMb8Wa31i4mPpb849JVBfxuYrpSarJTKw1vIeaWPyu5XKKUKlVJDzWvg88C7ePrYmDhtI/BydiTMOun08ApwXyJ7YQHw/52pdk7ji/1+Ga+/gKeTcqXUIKXUZLwFwIN9LV9foJRSwONAldb6P51D0l9ctNZ98gesAiLAMeDf+6rc/vYHTAH+lvh7z+gCGIm3Sl8D/BkYkW1Z+0AXW/BCCG14Mc5/TacHQOFlSh0D/g6UZlv+PtTJbxJ1PopnqMY65/97QifVwMpsy38d9bIIL5xyFDiS+Fs10PuL/0+2/guCIOQIsigqCIKQI4hBFwRByBHEoAuCIOQIYtAFQRByBDHogiAIOYIYdEEQhBxBDLogCEKO8L9taNnZputN+wAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2051fc88>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2054b630>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa281c1438>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20493940>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa204bd518>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa20467160>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa204a1908>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa2042dcc0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa203d6898>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa204004e0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7faa203ddc50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化所有的 attention slices\n", "for attentionVector in model.components.attention_mechanism.ctx_vector:\n", "    att = sorted(list(enumerate(attentionVector[0].flatten())),\n", "                 key = lambda tup:tup[1],\n", "                 reverse = True) # attention 按权重从大到小递减排序\n", "    idxs,att = zip(*att)\n", "\n", "    positions = idxs[:]\n", "    \n", "    # 把扁平化的一维的 attention slice 重整成二维的图片矩阵，像素颜色值范围 [0, 255]\n", "    outarray = np.ones((att_h, att_w)) * 255.\n", "    \n", "    for i in range(len(positions)):\n", "        pos = positions[i]\n", "        loc_x = int(pos / att_w)\n", "        loc_y = int(pos % att_w)\n", "        att_pos = att[i]\n", "        outarray[loc_x, loc_y] = (1 - att_pos) * 255. \n", "        # (1 - att_pos) * 255. 而不是直接 att_pos * 255\n", "        # 因为颜色值越小越暗，而权重需要越大越暗\n", "    \n", "    out_image = PILImage.fromarray(outarray).resize((img_w, img_h), PILImage.NEAREST)\n", "    inp_image = PILImage.open(img_path)\n", "    \n", "    combine = PILImage.blend(inp_image.convert('RGBA'), out_image.convert('RGBA'),0.5)\n", "    \n", "    plt.figure()\n", "    plt.imshow(np.asarray(combine))"]}, {"cell_type": "markdown", "metadata": {"heading_collapsed": true}, "source": ["## Save Attention Slices as GIF\n", "\n", "it will generate a GIF file look like these.\n", "\n", "attention window will slide with the LaTeX symbol predicted one by one. One Attention Slice \n", "corresponds to one LaTeX Symbol.\n", "\n", "![](art/visualization_data.images_test.6.gif)\n", "\n", "![](art/visualization_data.images_test.2.gif)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"ExecuteTime": {"end_time": "2019-03-09T14:14:57.113261Z", "start_time": "2019-03-09T14:13:48.390145Z"}, "code_folding": [], "hidden": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/pyworks/env35/lib/python3.5/site-packages/ipykernel_launcher.py:11: DeprecationWarning: `imread` is deprecated!\n", "`imread` is deprecated in SciPy 1.0.0, and will be removed in 1.2.0.\n", "Use ``imageio.imread`` instead.\n", "  # This is added back by InteractiveShellApp.init_path()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["fig size: 72.0 DPI, size in inches [25.  6.]\n", "finish!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/pyworks/env35/lib/python3.5/site-packages/matplotlib/figure.py:2022: UserWarning: This figure includes Axes that are not compatible with tight_layout, so results might be incorrect.\n", "  warnings.warn(\"This figure includes Axes that are not compatible \"\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f6ff0452b38>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.animation import FuncAnimation\n", "from matplotlib import transforms\n", "\n", "LaTeX_symbols_count = len(hyps[0].split(\" \"))\n", "attention_slices_count = len(model.components.attention_mechanism.ctx_vector)\n", "img, img_w, img_h = readImageAndShape(img_path)\n", "att_w, att_h = getWH(img_w, img_h)\n", "    \n", "fig, ax = plt.subplots()\n", "fig.set_tight_layout(True)\n", "fig.set_figwidth(25)\n", "fig.set_figheight(6)\n", "\n", "# 询问图形在屏幕上的大小和DPI（每英寸点数）\n", "# 注意当把图形保存为文件时，需要为此单独再提供一个DPI\n", "print('fig size: {0} DPI, size in inches {1}'.format(fig.get_dpi(), fig.get_size_inches()))\n", "\n", "\n", "def rainbow_text(x, y, strings, colors, ax=None, **kw):\n", "    \"\"\"\n", "    Take a list of ``strings`` and ``colors`` and place them next to each\n", "    other, with text strings[i] being shown in colors[i].\n", "\n", "    This example shows how to do both vertical and horizontal text, and will\n", "    pass all keyword arguments to plt.text, so you can set the font size,\n", "    family, etc.\n", "\n", "    The text will get added to the ``ax`` axes, if provided, otherwise the\n", "    currently active axes will be used.\n", "    \"\"\"\n", "    if ax is None:\n", "        ax = plt.gca()\n", "    t = ax.transData\n", "    canvas = ax.figure.canvas\n", "\n", "    # horizontal version\n", "    for s, c in zip(strings, colors):\n", "        text = ax.text(x, y, s + \" \", color=c, transform=t, **kw)\n", "        text.draw(canvas.get_renderer())\n", "        ex = text.get_window_extent()\n", "        t = transforms.offset_copy(\n", "            text.get_transform(), x=ex.width, units='dots')\n", "\n", "def update(i):\n", "    '''\n", "    在这里绘制动画帧\n", "    args:\n", "       i : (int) range [0, ?)\n", "    return:\n", "       (tuple) 以元组形式返回这一帧需要重新绘制的物体\n", "    '''\n", "    # 1. 更新标题\n", "    LaTeX_symbols = hyps[0].split()\n", "    LaTeX_symbols_count = len(LaTeX_symbols)\n", "    LaTeX_symbols_colors = ['green']*LaTeX_symbols_count\n", "    if i < LaTeX_symbols_count:\n", "        LaTeX_symbols_colors[i] = \"red\"\n", "#     ax.set_xlabel(label)\n", "    rainbow_text(0, img_h+6 , LaTeX_symbols[:], LaTeX_symbols_colors[:], ax, size=32)\n", "    \n", "    # 2. 更新图片\n", "    attentionVector = model.components.attention_mechanism.ctx_vector[i][0]\n", "    att = sorted(list(enumerate(attentionVector.flatten())),\n", "                 key = lambda tup:tup[1],\n", "                 reverse = True) # attention 按权重从大到小递减排序\n", "    idxs,att = zip(*att)\n", "#     j=1\n", "#     while sum(att[:j])<0.9:\n", "#         # 取 90% 的 attention\n", "#         j+=1\n", "    positions = idxs[:]\n", "    \n", "    # 把扁平化的一维的 attention slice 重整成二维的图片矩阵，像素颜色值范围 [0, 255]\n", "    outarray = np.ones((att_h, att_w)) * 255.\n", "    \n", "    for i in range(len(positions)):\n", "        pos = positions[i]\n", "        loc_x = int(pos / att_w)\n", "        loc_y = int(pos % att_w)\n", "        att_pos = att[i]\n", "        outarray[loc_x, loc_y] = (1 - att_pos) * 255. \n", "        # (1 - att_pos) * 255. 而不是直接 att_pos * 255\n", "        # 因为颜色值越小越暗，而权重需要越大越暗\n", "    \n", "    out_image = PILImage.fromarray(outarray).resize((img_w, img_h), PILImage.NEAREST)\n", "    inp_image = PILImage.open(img_path)\n", "    \n", "    combine = PILImage.blend(inp_image.convert('RGBA'), out_image.convert('RGBA'),0.5)\n", "    \n", "    ax.imshow(np.asarray(combine))\n", "    \n", "    # 3. 以元组形式返回这一帧需要重新绘制的物体\n", "    return ax\n", "\n", "# 会为每一帧调用Update函数\n", "# 这里FunAnimation设置一个10帧动画，每帧间隔200ms\n", "plt.title(\"Visualize Attention over Image\", fontsize=40)\n", "anim = FuncAnimation(fig, update, frames=np.arange(0, LaTeX_symbols_count), interval=200)\n", "anim.save('visualization1.gif', dpi=80, writer='imagemagick')\n", "print(\"finish!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}, "toc": {"nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "toc_cell": true, "toc_position": {}, "toc_section_display": "block", "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "oldHeight": 651.5, "position": {"height": "40px", "left": "1179px", "right": "20px", "top": "107px", "width": "305px"}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "varInspector_section_display": "none", "window_display": true}}, "nbformat": 4, "nbformat_minor": 2}