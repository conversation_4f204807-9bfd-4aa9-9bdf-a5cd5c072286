{"_args": [["katex", "/home/<USER>/Projects/im2latex"]], "_from": "katex@latest", "_id": "katex@0.6.0", "_inCache": true, "_installable": true, "_location": "/katex", "_nodeVersion": "4.2.1", "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/katex-0.6.0.tgz_1460769444991_0.38667152682319283"}, "_npmUser": {"email": "<EMAIL>", "name": "kevinbarabash"}, "_npmVersion": "2.15.2", "_phantomChildren": {}, "_requested": {"name": "katex", "raw": "katex", "rawSpec": "", "scope": null, "spec": "latest", "type": "tag"}, "_requiredBy": ["#USER"], "_resolved": "https://registry.npmjs.org/katex/-/katex-0.6.0.tgz", "_shasum": "12418e09121c05c92041b6b3b9fb6bab213cb6f3", "_shrinkwrap": null, "_spec": "katex", "_where": "/home/<USER>/Projects/im2latex", "bin": {"katex": "cli.js"}, "bugs": {"url": "https://github.com/Khan/KaTeX/issues"}, "dependencies": {"match-at": "^0.1.0"}, "description": "Fast math typesetting for the web.", "devDependencies": {"browserify": "^10.2.4", "clean-css": "~2.2.15", "eslint": "^1.10.2", "express": "~3.3.3", "glob": "^5.0.15", "jasmine": "^2.3.2", "jasmine-core": "^2.3.4", "js-yaml": "^3.3.1", "jspngopt": "^0.1.0", "less": "~1.7.5", "nomnom": "^1.8.1", "pako": "0.2.7", "selenium-webdriver": "^2.46.1", "uglify-js": "~2.4.15"}, "directories": {}, "dist": {"shasum": "12418e09121c05c92041b6b3b9fb6bab213cb6f3", "tarball": "https://registry.npmjs.org/katex/-/katex-0.6.0.tgz"}, "files": ["cli.js", "dist/", "katex.js", "src/"], "gitHead": "b94fc6534d5c23f944906a52a592bee4e0090665", "homepage": "https://github.com/Khan/KaTeX#readme", "license": "MIT", "main": "katex.js", "maintainers": [{"name": "kevinbarabash", "email": "<EMAIL>"}, {"name": "spicyj", "email": "<EMAIL>"}, {"name": "xymostech", "email": "<EMAIL>"}], "name": "katex", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git://github.com/Khan/KaTeX.git"}, "scripts": {"prepublish": "make dist", "start": "node server.js", "test": "make lint test"}, "version": "0.6.0"}